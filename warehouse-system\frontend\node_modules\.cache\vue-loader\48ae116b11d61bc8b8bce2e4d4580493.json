{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\index.vue", "mtime": 1756537442794}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiSW5kZXgiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDnu5/orqHmlbDmja4KICAgICAgc3RhdGlzdGljc0RhdGE6IHsKICAgICAgICB3YXJlaG91c2VDb3VudDogMywKICAgICAgICBwcm9kdWN0Q291bnQ6IDEyOCwKICAgICAgICBpbmJvdW5kQ291bnQ6IDI1NiwKICAgICAgICBvdXRib3VuZENvdW50OiAxOTgKICAgICAgfSwKICAgICAgLy8g5pyA6L+R5YWl5bqT6K6w5b2VCiAgICAgIHJlY2VudEluYm91bmQ6IFsKICAgICAgICB7IGlkOiAxLCBwcm9kdWN0TmFtZTogIueslOiusOacrOeUteiEkSIsIHF1YW50aXR5OiAxMCwgd2FyZWhvdXNlTmFtZTogIuS4u+S7k+W6kyIsIGNyZWF0ZVRpbWU6ICIyMDIzLTA1LTE2IDE0OjMwOjAwIiB9LAogICAgICAgIHsgaWQ6IDIsIHByb2R1Y3ROYW1lOiAi5Yqe5YWs5qGMIiwgcXVhbnRpdHk6IDUsIHdhcmVob3VzZU5hbWU6ICLkuLvku5PlupMiLCBjcmVhdGVUaW1lOiAiMjAyMy0wNS0xNiAxMToyMDowMCIgfSwKICAgICAgICB7IGlkOiAzLCBwcm9kdWN0TmFtZTogIuaJk+WNsOacuiIsIHF1YW50aXR5OiAzLCB3YXJlaG91c2VOYW1lOiAi5aSH55So5LuT5bqTIiwgY3JlYXRlVGltZTogIjIwMjMtMDUtMTUgMTY6NDU6MDAiIH0sCiAgICAgICAgeyBpZDogNCwgcHJvZHVjdE5hbWU6ICLmipXlvbHku6oiLCBxdWFudGl0eTogMiwgd2FyZWhvdXNlTmFtZTogIuS4u+S7k+W6kyIsIGNyZWF0ZVRpbWU6ICIyMDIzLTA1LTE1IDA5OjE1OjAwIiB9LAogICAgICAgIHsgaWQ6IDUsIHByb2R1Y3ROYW1lOiAi5Yqe5YWs5qSFIiwgcXVhbnRpdHk6IDgsIHdhcmVob3VzZU5hbWU6ICLlpIfnlKjku5PlupMiLCBjcmVhdGVUaW1lOiAiMjAyMy0wNS0xNCAxNDozMDowMCIgfQogICAgICBdLAogICAgICAvLyDmnIDov5Hlh7rlupPorrDlvZUKICAgICAgcmVjZW50T3V0Ym91bmQ6IFsKICAgICAgICB7IGlkOiAxLCBwcm9kdWN0TmFtZTogIueslOiusOacrOeUteiEkSIsIHF1YW50aXR5OiAyLCB3YXJlaG91c2VOYW1lOiAi5Li75LuT5bqTIiwgY3JlYXRlVGltZTogIjIwMjMtMDUtMTYgMTU6MzA6MDAiIH0sCiAgICAgICAgeyBpZDogMiwgcHJvZHVjdE5hbWU6ICLmiZPljbDmnLoiLCBxdWFudGl0eTogMSwgd2FyZWhvdXNlTmFtZTogIuWkh+eUqOS7k+W6kyIsIGNyZWF0ZVRpbWU6ICIyMDIzLTA1LTE2IDEwOjIwOjAwIiB9LAogICAgICAgIHsgaWQ6IDMsIHByb2R1Y3ROYW1lOiAi5Yqe5YWs5qGMIiwgcXVhbnRpdHk6IDIsIHdhcmVob3VzZU5hbWU6ICLkuLvku5PlupMiLCBjcmVhdGVUaW1lOiAiMjAyMy0wNS0xNSAxMzo0NTowMCIgfSwKICAgICAgICB7IGlkOiA0LCBwcm9kdWN0TmFtZTogIuaKleW9seS7qiIsIHF1YW50aXR5OiAxLCB3YXJlaG91c2VOYW1lOiAi5Li75LuT5bqTIiwgY3JlYXRlVGltZTogIjIwMjMtMDUtMTUgMDg6MTU6MDAiIH0sCiAgICAgICAgeyBpZDogNSwgcHJvZHVjdE5hbWU6ICLlip7lhazmpIUiLCBxdWFudGl0eTogMywgd2FyZWhvdXNlTmFtZTogIuWkh+eUqOS7k+W6kyIsIGNyZWF0ZVRpbWU6ICIyMDIzLTA1LTE0IDExOjMwOjAwIiB9CiAgICAgIF0sCiAgICAgIC8vIOWbvuihqOWunuS+iwogICAgICBpbnZlbnRvcnlDaGFydDogbnVsbCwKICAgICAgdHJlbmRDaGFydDogbnVsbAogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmluaXRJbnZlbnRvcnlDaGFydCgpOwogICAgdGhpcy5pbml0VHJlbmRDaGFydCgpOwogICAgLy8g55uR5ZCs56qX5Y+j5aSn5bCP5Y+Y5YyW77yM6YeN5paw6LCD5pW05Zu+6KGo5aSn5bCPCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5yZXNpemVDaGFydHMpOwogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIC8vIOenu+mZpOS6i+S7tuebkeWQrAogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQ2hhcnRzKTsKICAgIC8vIOmUgOavgeWbvuihqOWunuS+iwogICAgaWYgKHRoaXMuaW52ZW50b3J5Q2hhcnQpIHsKICAgICAgdGhpcy5pbnZlbnRvcnlDaGFydC5kaXNwb3NlKCk7CiAgICB9CiAgICBpZiAodGhpcy50cmVuZENoYXJ0KSB7CiAgICAgIHRoaXMudHJlbmRDaGFydC5kaXNwb3NlKCk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliJ3lp4vljJblupPlrZjnu5/orqHlm77ooagKICAgIGluaXRJbnZlbnRvcnlDaGFydCgpIHsKICAgICAgdGhpcy5pbnZlbnRvcnlDaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLmludmVudG9yeUNoYXJ0KTsKICAgICAgY29uc3Qgb3B0aW9uID0gewogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdpdGVtJywKICAgICAgICAgIGZvcm1hdHRlcjogJ3thfSA8YnIvPntifToge2N9ICh7ZH0lKScKICAgICAgICB9LAogICAgICAgIGxlZ2VuZDogewogICAgICAgICAgb3JpZW50OiAndmVydGljYWwnLAogICAgICAgICAgbGVmdDogMTAsCiAgICAgICAgICBkYXRhOiBbJ+eUteWtkOS6p+WTgScsICflip7lhaznlKjlk4EnLCAn55Sf5rS755So5ZOBJywgJ+WFtuS7liddCiAgICAgICAgfSwKICAgICAgICBzZXJpZXM6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgbmFtZTogJ+W6k+WtmOWIhuW4gycsCiAgICAgICAgICAgIHR5cGU6ICdwaWUnLAogICAgICAgICAgICByYWRpdXM6IFsnNTAlJywgJzcwJSddLAogICAgICAgICAgICBhdm9pZExhYmVsT3ZlcmxhcDogZmFsc2UsCiAgICAgICAgICAgIGxhYmVsOiB7CiAgICAgICAgICAgICAgc2hvdzogZmFsc2UsCiAgICAgICAgICAgICAgcG9zaXRpb246ICdjZW50ZXInCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGVtcGhhc2lzOiB7CiAgICAgICAgICAgICAgbGFiZWw6IHsKICAgICAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE4JywKICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgbGFiZWxMaW5lOiB7CiAgICAgICAgICAgICAgc2hvdzogZmFsc2UKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YTogWwogICAgICAgICAgICAgIHsgdmFsdWU6IDQ1LCBuYW1lOiAn55S15a2Q5Lqn5ZOBJyB9LAogICAgICAgICAgICAgIHsgdmFsdWU6IDMwLCBuYW1lOiAn5Yqe5YWs55So5ZOBJyB9LAogICAgICAgICAgICAgIHsgdmFsdWU6IDE1LCBuYW1lOiAn55Sf5rS755So5ZOBJyB9LAogICAgICAgICAgICAgIHsgdmFsdWU6IDEwLCBuYW1lOiAn5YW25LuWJyB9CiAgICAgICAgICAgIF0KICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH07CiAgICAgIHRoaXMuaW52ZW50b3J5Q2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7CiAgICB9LAogICAgLy8g5Yid5aeL5YyW5Ye65YWl5bqT6LaL5Yq/5Zu+6KGoCiAgICBpbml0VHJlbmRDaGFydCgpIHsKICAgICAgdGhpcy50cmVuZENoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMudHJlbmRDaGFydCk7CgogICAgICAvLyDojrflj5bnnJ/lrp7nmoTotovlir/mlbDmja4KICAgICAgdGhpcy5nZXRTdG9ja1RyZW5kRGF0YSgpLnRoZW4odHJlbmREYXRhID0+IHsKICAgICAgICBjb25zdCBkYXRlcyA9IHRyZW5kRGF0YS5tYXAoaXRlbSA9PiBpdGVtLmRhdGUpOwogICAgICAgIGNvbnN0IGluRGF0YSA9IHRyZW5kRGF0YS5tYXAoaXRlbSA9PiBpdGVtLmluT3BlcmF0aW9ucyB8fCAwKTsKICAgICAgICBjb25zdCBvdXREYXRhID0gdHJlbmREYXRhLm1hcChpdGVtID0+IGl0ZW0ub3V0T3BlcmF0aW9ucyB8fCAwKTsKICAgICAgICBjb25zdCB0cmFuc2ZlckRhdGEgPSB0cmVuZERhdGEubWFwKGl0ZW0gPT4gaXRlbS50cmFuc2Zlck9wZXJhdGlvbnMgfHwgMCk7CiAgICAgICAgY29uc3QgcHVyY2hhc2VEYXRhID0gdHJlbmREYXRhLm1hcChpdGVtID0+IGl0ZW0ucHVyY2hhc2VPcGVyYXRpb25zIHx8IDApOwoKICAgICAgICBjb25zdCBvcHRpb24gPSB7CiAgICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywKICAgICAgICAgICAgYXhpc1BvaW50ZXI6IHsKICAgICAgICAgICAgICB0eXBlOiAnY3Jvc3MnCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgICAgZGF0YTogWyflhaXlupMnLCAn5Ye65bqTJywgJ+iwg+aLqCcsICfnlLPotK0nXQogICAgICAgICAgfSwKICAgICAgICAgIGdyaWQ6IHsKICAgICAgICAgICAgbGVmdDogJzMlJywKICAgICAgICAgICAgcmlnaHQ6ICc0JScsCiAgICAgICAgICAgIGJvdHRvbTogJzMlJywKICAgICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgeEF4aXM6IHsKICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywKICAgICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLAogICAgICAgICAgICBkYXRhOiBkYXRlcy5sZW5ndGggPiAwID8gZGF0ZXMgOiBbJ+aaguaXoOaVsOaNriddLAogICAgICAgICAgICBheGlzTGFiZWw6IHsKICAgICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHZhbHVlKSB7CiAgICAgICAgICAgICAgICBpZiAoIXZhbHVlKSByZXR1cm4gJyc7CiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUuc3Vic3RyaW5nKDUpOyAvLyDlj6rmmL7npLrmnIgt5pelCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgeUF4aXM6IHsKICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywKICAgICAgICAgICAgbmFtZTogJ+aTjeS9nOasoeaVsCcKICAgICAgICAgIH0sCiAgICAgICAgICBzZXJpZXM6IFsKICAgICAgICAgICAgewogICAgICAgICAgICAgIG5hbWU6ICflhaXlupMnLAogICAgICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgICAgICBkYXRhOiBpbkRhdGEubGVuZ3RoID4gMCA/IGluRGF0YSA6IFswXSwKICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICAgIGNvbG9yOiAnIzY3QzIzQScKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIGFyZWFTdHlsZTogewogICAgICAgICAgICAgICAgb3BhY2l0eTogMC4zCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgbmFtZTogJ+WHuuW6kycsCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICAgIGRhdGE6IG91dERhdGEubGVuZ3RoID4gMCA/IG91dERhdGEgOiBbMF0sCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgICBjb2xvcjogJyNGNTZDNkMnCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBhcmVhU3R5bGU6IHsKICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuMwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgIG5hbWU6ICfosIPmi6gnLAogICAgICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgICAgICBkYXRhOiB0cmFuc2ZlckRhdGEubGVuZ3RoID4gMCA/IHRyYW5zZmVyRGF0YSA6IFswXSwKICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICAgIGNvbG9yOiAnI0U2QTIzQycKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIGFyZWFTdHlsZTogewogICAgICAgICAgICAgICAgb3BhY2l0eTogMC4zCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgbmFtZTogJ+eUs+i0rScsCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICAgIGRhdGE6IHB1cmNoYXNlRGF0YS5sZW5ndGggPiAwID8gcHVyY2hhc2VEYXRhIDogWzBdLAogICAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgICAgY29sb3I6ICcjNDA5RUZGJwogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgYXJlYVN0eWxlOiB7CiAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjMKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIF0KICAgICAgICB9OwogICAgICAgIHRoaXMudHJlbmRDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsKICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlui2i+WKv+aVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgICAgLy8g5L2/55So6buY6K6k5pWw5o2uCiAgICAgICAgY29uc3Qgb3B0aW9uID0gewogICAgICAgICAgdG9vbHRpcDogewogICAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycKICAgICAgICAgIH0sCiAgICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgICAgZGF0YTogWyflhaXlupMnLCAn5Ye65bqTJywgJ+iwg+aLqCcsICfnlLPotK0nXQogICAgICAgICAgfSwKICAgICAgICAgIGdyaWQ6IHsKICAgICAgICAgICAgbGVmdDogJzMlJywKICAgICAgICAgICAgcmlnaHQ6ICc0JScsCiAgICAgICAgICAgIGJvdHRvbTogJzMlJywKICAgICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgeEF4aXM6IHsKICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywKICAgICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLAogICAgICAgICAgICBkYXRhOiBbJ+aaguaXoOaVsOaNriddCiAgICAgICAgICB9LAogICAgICAgICAgeUF4aXM6IHsKICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJwogICAgICAgICAgfSwKICAgICAgICAgIHNlcmllczogWwogICAgICAgICAgICB7CiAgICAgICAgICAgICAgbmFtZTogJ+WFpeW6kycsCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICAgIGRhdGE6IFswXSwKICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjNjdDMjNBJyB9CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBuYW1lOiAn5Ye65bqTJywKICAgICAgICAgICAgICB0eXBlOiAnbGluZScsCiAgICAgICAgICAgICAgZGF0YTogWzBdLAogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNGNTZDNkMnIH0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgIG5hbWU6ICfosIPmi6gnLAogICAgICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgICAgICBkYXRhOiBbMF0sCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0U2QTIzQycgfQogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgbmFtZTogJ+eUs+i0rScsCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICAgIGRhdGE6IFswXSwKICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjNDA5RUZGJyB9CiAgICAgICAgICAgIH0KICAgICAgICAgIF0KICAgICAgICB9OwogICAgICAgIHRoaXMudHJlbmRDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6YeN5paw6LCD5pW05Zu+6KGo5aSn5bCPCiAgICByZXNpemVDaGFydHMoKSB7CiAgICAgIGlmICh0aGlzLmludmVudG9yeUNoYXJ0KSB7CiAgICAgICAgdGhpcy5pbnZlbnRvcnlDaGFydC5yZXNpemUoKTsKICAgICAgfQogICAgICBpZiAodGhpcy50cmVuZENoYXJ0KSB7CiAgICAgICAgdGhpcy50cmVuZENoYXJ0LnJlc2l6ZSgpOwogICAgICB9CiAgICB9LAogICAgLy8g5p+l55yL5pu05aSa5YWl5bqT6K6w5b2VCiAgICB2aWV3TW9yZUluYm91bmQoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvaW52ZW50b3J5L2luJyk7CiAgICB9LAogICAgLy8g5p+l55yL5pu05aSa5Ye65bqT6K6w5b2VCiAgICB2aWV3TW9yZU91dGJvdW5kKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL2ludmVudG9yeS9vdXQnKTsKICAgIH0sCgogICAgLy8g6I635Y+W5Ye65YWl5bqT6LaL5Yq/5pWw5o2uCiAgICBnZXRTdG9ja1RyZW5kRGF0YSgpIHsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsKICAgICAgICAvLyDosIPnlKjlupPlrZjml6Xlv5fotovlir9BUEkKICAgICAgICB0aGlzLiRodHRwLmdldCgnL2xvZy9zdG9jay90cmVuZCcsIHsKICAgICAgICAgIHBhcmFtczogeyBkYXlzOiA3IH0KICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgIHJlc29sdmUocmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOeUn+aIkOaooeaLn+aVsOaNrgogICAgICAgICAgICBjb25zdCBtb2NrRGF0YSA9IHRoaXMuZ2VuZXJhdGVNb2NrVHJlbmREYXRhKCk7CiAgICAgICAgICAgIHJlc29sdmUobW9ja0RhdGEpOwogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlui2i+WKv+aVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgICAgICAvLyDnlJ/miJDmqKHmi5/mlbDmja4KICAgICAgICAgIGNvbnN0IG1vY2tEYXRhID0gdGhpcy5nZW5lcmF0ZU1vY2tUcmVuZERhdGEoKTsKICAgICAgICAgIHJlc29sdmUobW9ja0RhdGEpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g55Sf5oiQ5qih5ouf6LaL5Yq/5pWw5o2uCiAgICBnZW5lcmF0ZU1vY2tUcmVuZERhdGEoKSB7CiAgICAgIGNvbnN0IGRhdGEgPSBbXTsKICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOwoKICAgICAgZm9yIChsZXQgaSA9IDY7IGkgPj0gMDsgaS0tKSB7CiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRvZGF5KTsKICAgICAgICBkYXRlLnNldERhdGUoZGF0ZS5nZXREYXRlKCkgLSBpKTsKICAgICAgICBjb25zdCBkYXRlU3RyID0gZGF0ZS50b0lTT1N0cmluZygpLnN1YnN0cmluZygwLCAxMCk7CgogICAgICAgIGRhdGEucHVzaCh7CiAgICAgICAgICBkYXRlOiBkYXRlU3RyLAogICAgICAgICAgaW5PcGVyYXRpb25zOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAyMCkgKyA1LAogICAgICAgICAgb3V0T3BlcmF0aW9uczogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTUpICsgMywKICAgICAgICAgIHRyYW5zZmVyT3BlcmF0aW9uczogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogOCkgKyAxLAogICAgICAgICAgcHVyY2hhc2VPcGVyYXRpb25zOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiA1KSArIDEKICAgICAgICB9KTsKICAgICAgfQoKICAgICAgcmV0dXJuIGRhdGE7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgJA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"app-container home\">\n    <el-row :gutter=\"20\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"24\">\n        <el-card shadow=\"hover\" class=\"welcome-card\">\n          <div class=\"welcome-header\">\n            <h1>万裕物业仓库管理系统</h1>\n            <p>欢迎使用万裕物业仓库管理系统，高效管理您的仓库和物品信息</p>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 统计卡片 -->\n    <el-row :gutter=\"20\" class=\"statistics-row\">\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-s-home\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">仓库总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.warehouseCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-shopping-cart-full\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">物品总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.productCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-upload\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">入库总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.inboundCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-download\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">出库总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.outboundCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 图表区域 -->\n    <el-row :gutter=\"20\" class=\"chart-row\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"chart-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>库存统计</span>\n          </div>\n          <div class=\"chart-container\" ref=\"inventoryChart\"></div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"chart-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>出入库趋势</span>\n          </div>\n          <div class=\"chart-container\" ref=\"trendChart\"></div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 最近活动 -->\n    <el-row :gutter=\"20\" class=\"activity-row\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"activity-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>最近入库记录</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"viewMoreInbound\">查看更多</el-button>\n          </div>\n          <el-table :data=\"recentInbound\" style=\"width: 100%\" :show-header=\"false\">\n            <el-table-column width=\"50\">\n              <template slot-scope=\"scope\">\n                <el-avatar icon=\"el-icon-upload\" size=\"small\" style=\"background-color: #67C23A;\"></el-avatar>\n              </template>\n            </el-table-column>\n            <el-table-column>\n              <template slot-scope=\"scope\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-title\">{{ scope.row.productName }} 入库</div>\n                  <div class=\"activity-desc\">数量: {{ scope.row.quantity }} | 仓库: {{ scope.row.warehouseName }}</div>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column width=\"150\" align=\"right\">\n              <template slot-scope=\"scope\">\n                <span class=\"activity-time\">{{ scope.row.createTime }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"activity-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>最近出库记录</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"viewMoreOutbound\">查看更多</el-button>\n          </div>\n          <el-table :data=\"recentOutbound\" style=\"width: 100%\" :show-header=\"false\">\n            <el-table-column width=\"50\">\n              <template slot-scope=\"scope\">\n                <el-avatar icon=\"el-icon-download\" size=\"small\" style=\"background-color: #F56C6C;\"></el-avatar>\n              </template>\n            </el-table-column>\n            <el-table-column>\n              <template slot-scope=\"scope\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-title\">{{ scope.row.productName }} 出库</div>\n                  <div class=\"activity-desc\">数量: {{ scope.row.quantity }} | 仓库: {{ scope.row.warehouseName }}</div>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column width=\"150\" align=\"right\">\n              <template slot-scope=\"scope\">\n                <span class=\"activity-time\">{{ scope.row.createTime }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: \"Index\",\n  data() {\n    return {\n      // 统计数据\n      statisticsData: {\n        warehouseCount: 3,\n        productCount: 128,\n        inboundCount: 256,\n        outboundCount: 198\n      },\n      // 最近入库记录\n      recentInbound: [\n        { id: 1, productName: \"笔记本电脑\", quantity: 10, warehouseName: \"主仓库\", createTime: \"2023-05-16 14:30:00\" },\n        { id: 2, productName: \"办公桌\", quantity: 5, warehouseName: \"主仓库\", createTime: \"2023-05-16 11:20:00\" },\n        { id: 3, productName: \"打印机\", quantity: 3, warehouseName: \"备用仓库\", createTime: \"2023-05-15 16:45:00\" },\n        { id: 4, productName: \"投影仪\", quantity: 2, warehouseName: \"主仓库\", createTime: \"2023-05-15 09:15:00\" },\n        { id: 5, productName: \"办公椅\", quantity: 8, warehouseName: \"备用仓库\", createTime: \"2023-05-14 14:30:00\" }\n      ],\n      // 最近出库记录\n      recentOutbound: [\n        { id: 1, productName: \"笔记本电脑\", quantity: 2, warehouseName: \"主仓库\", createTime: \"2023-05-16 15:30:00\" },\n        { id: 2, productName: \"打印机\", quantity: 1, warehouseName: \"备用仓库\", createTime: \"2023-05-16 10:20:00\" },\n        { id: 3, productName: \"办公桌\", quantity: 2, warehouseName: \"主仓库\", createTime: \"2023-05-15 13:45:00\" },\n        { id: 4, productName: \"投影仪\", quantity: 1, warehouseName: \"主仓库\", createTime: \"2023-05-15 08:15:00\" },\n        { id: 5, productName: \"办公椅\", quantity: 3, warehouseName: \"备用仓库\", createTime: \"2023-05-14 11:30:00\" }\n      ],\n      // 图表实例\n      inventoryChart: null,\n      trendChart: null\n    };\n  },\n  mounted() {\n    this.initInventoryChart();\n    this.initTrendChart();\n    // 监听窗口大小变化，重新调整图表大小\n    window.addEventListener('resize', this.resizeCharts);\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    window.removeEventListener('resize', this.resizeCharts);\n    // 销毁图表实例\n    if (this.inventoryChart) {\n      this.inventoryChart.dispose();\n    }\n    if (this.trendChart) {\n      this.trendChart.dispose();\n    }\n  },\n  methods: {\n    // 初始化库存统计图表\n    initInventoryChart() {\n      this.inventoryChart = echarts.init(this.$refs.inventoryChart);\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 10,\n          data: ['电子产品', '办公用品', '生活用品', '其他']\n        },\n        series: [\n          {\n            name: '库存分布',\n            type: 'pie',\n            radius: ['50%', '70%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: false,\n              position: 'center'\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '18',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            data: [\n              { value: 45, name: '电子产品' },\n              { value: 30, name: '办公用品' },\n              { value: 15, name: '生活用品' },\n              { value: 10, name: '其他' }\n            ]\n          }\n        ]\n      };\n      this.inventoryChart.setOption(option);\n    },\n    // 初始化出入库趋势图表\n    initTrendChart() {\n      this.trendChart = echarts.init(this.$refs.trendChart);\n\n      // 获取真实的趋势数据\n      this.getStockTrendData().then(trendData => {\n        const dates = trendData.map(item => item.date);\n        const inData = trendData.map(item => item.inOperations || 0);\n        const outData = trendData.map(item => item.outOperations || 0);\n        const transferData = trendData.map(item => item.transferOperations || 0);\n        const purchaseData = trendData.map(item => item.purchaseOperations || 0);\n\n        const option = {\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'cross'\n            }\n          },\n          legend: {\n            data: ['入库', '出库', '调拨', '申购']\n          },\n          grid: {\n            left: '3%',\n            right: '4%',\n            bottom: '3%',\n            containLabel: true\n          },\n          xAxis: {\n            type: 'category',\n            boundaryGap: false,\n            data: dates.length > 0 ? dates : ['暂无数据'],\n            axisLabel: {\n              formatter: function(value) {\n                if (!value) return '';\n                return value.substring(5); // 只显示月-日\n              }\n            }\n          },\n          yAxis: {\n            type: 'value',\n            name: '操作次数'\n          },\n          series: [\n            {\n              name: '入库',\n              type: 'line',\n              data: inData.length > 0 ? inData : [0],\n              itemStyle: {\n                color: '#67C23A'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            },\n            {\n              name: '出库',\n              type: 'line',\n              data: outData.length > 0 ? outData : [0],\n              itemStyle: {\n                color: '#F56C6C'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            },\n            {\n              name: '调拨',\n              type: 'line',\n              data: transferData.length > 0 ? transferData : [0],\n              itemStyle: {\n                color: '#E6A23C'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            },\n            {\n              name: '申购',\n              type: 'line',\n              data: purchaseData.length > 0 ? purchaseData : [0],\n              itemStyle: {\n                color: '#409EFF'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            }\n          ]\n        };\n        this.trendChart.setOption(option);\n      }).catch(error => {\n        console.error('获取趋势数据失败:', error);\n        // 使用默认数据\n        const option = {\n          tooltip: {\n            trigger: 'axis'\n          },\n          legend: {\n            data: ['入库', '出库', '调拨', '申购']\n          },\n          grid: {\n            left: '3%',\n            right: '4%',\n            bottom: '3%',\n            containLabel: true\n          },\n          xAxis: {\n            type: 'category',\n            boundaryGap: false,\n            data: ['暂无数据']\n          },\n          yAxis: {\n            type: 'value'\n          },\n          series: [\n            {\n              name: '入库',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#67C23A' }\n            },\n            {\n              name: '出库',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#F56C6C' }\n            },\n            {\n              name: '调拨',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#E6A23C' }\n            },\n            {\n              name: '申购',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#409EFF' }\n            }\n          ]\n        };\n        this.trendChart.setOption(option);\n      });\n    },\n    // 重新调整图表大小\n    resizeCharts() {\n      if (this.inventoryChart) {\n        this.inventoryChart.resize();\n      }\n      if (this.trendChart) {\n        this.trendChart.resize();\n      }\n    },\n    // 查看更多入库记录\n    viewMoreInbound() {\n      this.$router.push('/inventory/in');\n    },\n    // 查看更多出库记录\n    viewMoreOutbound() {\n      this.$router.push('/inventory/out');\n    },\n\n    // 获取出入库趋势数据\n    getStockTrendData() {\n      return new Promise((resolve, reject) => {\n        // 调用库存日志趋势API\n        this.$http.get('/log/stock/trend', {\n          params: { days: 7 }\n        }).then(response => {\n          if (response.data && response.data.code === 200) {\n            resolve(response.data.data || []);\n          } else {\n            // 生成模拟数据\n            const mockData = this.generateMockTrendData();\n            resolve(mockData);\n          }\n        }).catch(error => {\n          console.error('获取趋势数据失败:', error);\n          // 生成模拟数据\n          const mockData = this.generateMockTrendData();\n          resolve(mockData);\n        });\n      });\n    },\n\n    // 生成模拟趋势数据\n    generateMockTrendData() {\n      const data = [];\n      const today = new Date();\n\n      for (let i = 6; i >= 0; i--) {\n        const date = new Date(today);\n        date.setDate(date.getDate() - i);\n        const dateStr = date.toISOString().substring(0, 10);\n\n        data.push({\n          date: dateStr,\n          inOperations: Math.floor(Math.random() * 20) + 5,\n          outOperations: Math.floor(Math.random() * 15) + 3,\n          transferOperations: Math.floor(Math.random() * 8) + 1,\n          purchaseOperations: Math.floor(Math.random() * 5) + 1\n        });\n      }\n\n      return data;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.home {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 84px);\n\n  .welcome-card {\n    margin-bottom: 20px;\n    background: linear-gradient(to right, #1890ff, #36cfc9);\n    color: #fff;\n\n    .welcome-header {\n      text-align: center;\n      padding: 20px 0;\n\n      h1 {\n        font-size: 28px;\n        margin-bottom: 10px;\n      }\n\n      p {\n        font-size: 16px;\n        opacity: 0.8;\n      }\n    }\n  }\n\n  .statistics-row {\n    margin-bottom: 20px;\n\n    .statistics-card {\n      height: 120px;\n      display: flex;\n      align-items: center;\n      padding: 20px;\n      margin-bottom: 20px;\n\n      .statistics-icon {\n        width: 60px;\n        height: 60px;\n        border-radius: 50%;\n        background-color: #f0f9eb;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 15px;\n\n        i {\n          font-size: 30px;\n          color: #67C23A;\n        }\n      }\n\n      .statistics-info {\n        flex: 1;\n\n        .statistics-title {\n          font-size: 16px;\n          color: #606266;\n          margin-bottom: 10px;\n        }\n\n        .statistics-value {\n          font-size: 24px;\n          font-weight: bold;\n          color: #303133;\n        }\n      }\n    }\n  }\n\n  .chart-row {\n    margin-bottom: 20px;\n\n    .chart-card {\n      margin-bottom: 20px;\n\n      .chart-container {\n        height: 300px;\n      }\n    }\n  }\n\n  .activity-row {\n    .activity-card {\n      margin-bottom: 20px;\n\n      .activity-item {\n        .activity-title {\n          font-size: 14px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n\n        .activity-desc {\n          font-size: 12px;\n          color: #909399;\n        }\n      }\n\n      .activity-time {\n        font-size: 12px;\n        color: #909399;\n      }\n    }\n  }\n}\n</style>"]}]}