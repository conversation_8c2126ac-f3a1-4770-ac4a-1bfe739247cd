{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\transfer\\print.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\transfer\\print.vue", "mtime": 1756537534758}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEludmVudG9yeVRyYW5zZmVyIH0gZnJvbSAiQC9hcGkvaW52ZW50b3J5L3RyYW5zZmVyIjsKaW1wb3J0IHsgZ2V0QmF0Y2hVc2VyUmVhbE5hbWVzIH0gZnJvbSAiQC91dGlscy91c2VyVXRpbHMiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJUcmFuc2ZlclByaW50IiwKICBkaWN0czogWydpbnZlbnRvcnlfdHJhbnNmZXJfc3RhdHVzJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBpc1ByaW50aW5nOiBmYWxzZSwKICAgICAgcHJpbnRTZXR0aW5nc1Zpc2libGU6IGZhbHNlLAogICAgICB0cmFuc2ZlckRhdGE6IHsKICAgICAgICB0cmFuc2ZlckNvZGU6ICIiLAogICAgICAgIGZyb21XYXJlaG91c2VOYW1lOiAiIiwKICAgICAgICB0b1dhcmVob3VzZU5hbWU6ICIiLAogICAgICAgIHRyYW5zZmVyVGltZTogbnVsbCwKICAgICAgICBzdGF0dXM6ICIiLAogICAgICAgIGNyZWF0ZUJ5OiAiIiwKICAgICAgICBjcmVhdGVCeU5hbWU6ICIiLAogICAgICAgIGF1ZGl0Qnk6ICIiLAogICAgICAgIGF1ZGl0QnlOYW1lOiAiIiwKICAgICAgICByZW1hcms6ICIiLAogICAgICAgIGRldGFpbHM6IFtdCiAgICAgIH0sCiAgICAgIHRyYW5zZmVySWQ6IG51bGwsCiAgICAgIHByaW50U2V0dGluZ3M6IHsKICAgICAgICBtYXJnaW5Ub3A6IDE1LAogICAgICAgIG1hcmdpblJpZ2h0OiAxNSwKICAgICAgICBtYXJnaW5Cb3R0b206IDE1LAogICAgICAgIG1hcmdpbkxlZnQ6IDE1LAogICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICBvcmllbnRhdGlvbjogJ3BvcnRyYWl0JywKICAgICAgICBwYXBlclNpemU6ICdBNCcKICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLnRyYW5zZmVySWQgPSB0aGlzLiRyb3V0ZS5wYXJhbXMudHJhbnNmZXJJZCB8fCB0aGlzLiRyb3V0ZS5wYXJhbXMuaWQgfHwgdGhpcy4kcm91dGUucXVlcnkuaWQ7CiAgICBjb25zb2xlLmxvZygn6LCD5ouo5Y2V5omT5Y2w6aG16Z2i5Yid5aeL5YyW77yM6LCD5ouo5Y2VSUQ6JywgdGhpcy50cmFuc2ZlcklkKTsKICAgIGlmICghdGhpcy50cmFuc2ZlcklkKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e8uuWwkeiwg+aLqOWNlUlE5Y+C5pWwJyk7CiAgICAgIHJldHVybjsKICAgIH0KICAgIHRoaXMuZ2V0VHJhbnNmZXJEYXRhKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog6I635Y+W6LCD5ouo5Y2V5L+h5oGvICovCiAgICBnZXRUcmFuc2ZlckRhdGEoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGdldEludmVudG9yeVRyYW5zZmVyKHRoaXMudHJhbnNmZXJJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy50cmFuc2ZlckRhdGEgPSByZXNwb25zZS5kYXRhOwogICAgICAgIAogICAgICAgIC8vIOiOt+WPlueUqOaIt+ecn+WunuWnk+WQjQogICAgICAgIGNvbnN0IHVzZXJOYW1lcyA9IFsKICAgICAgICAgIHRoaXMudHJhbnNmZXJEYXRhLmNyZWF0ZUJ5LAogICAgICAgICAgdGhpcy50cmFuc2ZlckRhdGEuYXVkaXRCeQogICAgICAgIF0uZmlsdGVyKG5hbWUgPT4gbmFtZSk7IC8vIOi/h+a7pOepuuWAvAoKICAgICAgICBpZiAodXNlck5hbWVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIGdldEJhdGNoVXNlclJlYWxOYW1lcyh1c2VyTmFtZXMpLnRoZW4obmFtZU1hcCA9PiB7CiAgICAgICAgICAgIC8vIOabtOaWsOeUqOaIt+ecn+WunuWnk+WQjQogICAgICAgICAgICBpZiAodGhpcy50cmFuc2ZlckRhdGEuY3JlYXRlQnkgJiYgbmFtZU1hcFt0aGlzLnRyYW5zZmVyRGF0YS5jcmVhdGVCeV0pIHsKICAgICAgICAgICAgICB0aGlzLnRyYW5zZmVyRGF0YS5jcmVhdGVCeU5hbWUgPSBuYW1lTWFwW3RoaXMudHJhbnNmZXJEYXRhLmNyZWF0ZUJ5XTsKICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAodGhpcy50cmFuc2ZlckRhdGEuYXVkaXRCeSAmJiBuYW1lTWFwW3RoaXMudHJhbnNmZXJEYXRhLmF1ZGl0QnldKSB7CiAgICAgICAgICAgICAgdGhpcy50cmFuc2ZlckRhdGEuYXVkaXRCeU5hbWUgPSBuYW1lTWFwW3RoaXMudHJhbnNmZXJEYXRhLmF1ZGl0QnldOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgICAgCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5bosIPmi6jljZXkv6Hmga/lpLHotKUiKTsKICAgICAgfSk7CiAgICB9LAogICAgCiAgICAvKiog5aSE55CG5omT5Y2w6K6+572uICovCiAgICBoYW5kbGVQcmludFNldHRpbmdzKCkgewogICAgICB0aGlzLnByaW50U2V0dGluZ3NWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAKICAgIC8qKiDkv53lrZjmiZPljbDorr7nva4gKi8KICAgIHNhdmVQcmludFNldHRpbmdzKCkgewogICAgICAvLyDkv53lrZjliLDmnKzlnLDlrZjlgqgKICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3RyYW5zZmVyUHJpbnRTZXR0aW5ncycsIEpTT04uc3RyaW5naWZ5KHRoaXMucHJpbnRTZXR0aW5ncykpOwogICAgICB0aGlzLnByaW50U2V0dGluZ3NWaXNpYmxlID0gZmFsc2U7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5omT5Y2w6K6+572u5bey5L+d5a2YJyk7CiAgICB9LAogICAgCiAgICAvKiog5Yqg6L295omT5Y2w6K6+572uICovCiAgICBsb2FkUHJpbnRTZXR0aW5ncygpIHsKICAgICAgY29uc3Qgc2F2ZWRTZXR0aW5ncyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0cmFuc2ZlclByaW50U2V0dGluZ3MnKTsKICAgICAgaWYgKHNhdmVkU2V0dGluZ3MpIHsKICAgICAgICB0aGlzLnByaW50U2V0dGluZ3MgPSBKU09OLnBhcnNlKHNhdmVkU2V0dGluZ3MpOwogICAgICB9CiAgICB9LAogICAgCiAgICAvKiog5omT5Y2wICovCiAgICBoYW5kbGVQcmludCgpIHsKICAgICAgdGhpcy5pc1ByaW50aW5nID0gdHJ1ZTsKICAgICAgCiAgICAgIC8vIOehruS/neaVsOaNruW3suWKoOi9vQogICAgICBpZiAoIXRoaXMudHJhbnNmZXJEYXRhLnRyYW5zZmVyQ29kZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pWw5o2u6L+Y5Zyo5Yqg6L295Lit77yM6K+356iN5ZCO5YaN6K+VJyk7CiAgICAgICAgdGhpcy5pc1ByaW50aW5nID0gZmFsc2U7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIAogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgLy8g5Yib5bu65paw56qX5Y+j6L+b6KGM5omT5Y2wCiAgICAgICAgY29uc3QgcHJpbnRXaW5kb3cgPSB3aW5kb3cub3BlbignJywgJ19ibGFuaycsICd3aWR0aD0xMDAwLGhlaWdodD04MDAsc2Nyb2xsYmFycz15ZXMscmVzaXphYmxlPXllcycpOwogICAgICAgIAogICAgICAgIC8vIOeUn+aIkOaJk+WNsOWGheWuuQogICAgICAgIGNvbnN0IHByaW50Q29udGVudCA9IHRoaXMuZ2VuZXJhdGVQcmludEhUTUwoKTsKICAgICAgICAKICAgICAgICBwcmludFdpbmRvdy5kb2N1bWVudC53cml0ZShwcmludENvbnRlbnQpOwogICAgICAgIHByaW50V2luZG93LmRvY3VtZW50LmNsb3NlKCk7CiAgICAgICAgCiAgICAgICAgLy8g562J5b6F5YaF5a655Yqg6L295a6M5oiQ5ZCO5omT5Y2wCiAgICAgICAgcHJpbnRXaW5kb3cub25sb2FkID0gKCkgPT4gewogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgIHByaW50V2luZG93LnByaW50KCk7CiAgICAgICAgICAgIC8vIOaJk+WNsOWujOaIkOWQjuS4jeiHquWKqOWFs+mXreeql+WPo++8jOiuqeeUqOaIt+aJi+WKqOWFs+mXrQogICAgICAgICAgICB0aGlzLmlzUHJpbnRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0sIDUwMCk7CiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9LAogICAgCiAgICAvKiog55Sf5oiQ5omT5Y2w6aG16Z2iSFRNTCAqLwogICAgZ2VuZXJhdGVQcmludEhUTUwoKSB7CiAgICAgIGNvbnN0IGRldGFpbHMgPSB0aGlzLnRyYW5zZmVyRGF0YS5kZXRhaWxzIHx8IFtdOwogICAgICBsZXQgZGV0YWlsc0hUTUwgPSAnJzsKICAgICAgCiAgICAgIGRldGFpbHMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICBkZXRhaWxzSFRNTCArPSBgCiAgICAgICAgICA8dHI+CiAgICAgICAgICAgIDx0ZCBzdHlsZT0iYm9yZGVyOiAxcHggc29saWQgIzAwMDsgcGFkZGluZzogOHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7Ij4ke2luZGV4ICsgMX08L3RkPgogICAgICAgICAgICA8dGQgc3R5bGU9ImJvcmRlcjogMXB4IHNvbGlkICMwMDA7IHBhZGRpbmc6IDhweDsgdGV4dC1hbGlnbjogY2VudGVyOyI+JHtpdGVtLnByb2R1Y3RDb2RlIHx8ICcnfTwvdGQ+CiAgICAgICAgICAgIDx0ZCBzdHlsZT0iYm9yZGVyOiAxcHggc29saWQgIzAwMDsgcGFkZGluZzogOHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7Ij4ke2l0ZW0ucHJvZHVjdE5hbWUgfHwgJyd9PC90ZD4KICAgICAgICAgICAgPHRkIHN0eWxlPSJib3JkZXI6IDFweCBzb2xpZCAjMDAwOyBwYWRkaW5nOiA4cHg7IHRleHQtYWxpZ246IGNlbnRlcjsiPiR7aXRlbS5xdWFudGl0eSB8fCAnJ308L3RkPgogICAgICAgICAgICA8dGQgc3R5bGU9ImJvcmRlcjogMXB4IHNvbGlkICMwMDA7IHBhZGRpbmc6IDhweDsgdGV4dC1hbGlnbjogY2VudGVyOyI+JHtpdGVtLnJlbWFyayB8fCAnJ308L3RkPgogICAgICAgICAgPC90cj4KICAgICAgICBgOwogICAgICB9KTsKICAgICAgCiAgICAgIC8vIOagueaNruiuvue9ruehruWumumhtemdouaWueWQkeWSjOe6uOW8oOWkp+WwjwogICAgICBsZXQgcGFnZVNpemVTdHlsZSA9ICcnOwogICAgICBpZiAodGhpcy5wcmludFNldHRpbmdzLm9yaWVudGF0aW9uID09PSAnbGFuZHNjYXBlJykgewogICAgICAgIHBhZ2VTaXplU3R5bGUgPSBgc2l6ZTogJHt0aGlzLnByaW50U2V0dGluZ3MucGFwZXJTaXplfSBsYW5kc2NhcGU7YDsKICAgICAgfSBlbHNlIHsKICAgICAgICBwYWdlU2l6ZVN0eWxlID0gYHNpemU6ICR7dGhpcy5wcmludFNldHRpbmdzLnBhcGVyU2l6ZX0gcG9ydHJhaXQ7YDsKICAgICAgfQogICAgICAKICAgICAgcmV0dXJuIGAKICAgICAgICA8IURPQ1RZUEUgaHRtbD4KICAgICAgICA8aHRtbD4KICAgICAgICA8aGVhZD4KICAgICAgICAgIDxtZXRhIGNoYXJzZXQ9IlVURi04Ij4KICAgICAgICAgIDxtZXRhIG5hbWU9InZpZXdwb3J0IiBjb250ZW50PSJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MS4wIj4KICAgICAgICAgIDx0aXRsZT7lupPlrZjosIPmi6jljZXmiZPljbA8L3RpdGxlPgogICAgICAgICAgPHN0eWxlPgogICAgICAgICAgICBAcGFnZSB7IAogICAgICAgICAgICAgICR7cGFnZVNpemVTdHlsZX0KICAgICAgICAgICAgICBtYXJnaW46ICR7dGhpcy5wcmludFNldHRpbmdzLm1hcmdpblRvcH1tbSAke3RoaXMucHJpbnRTZXR0aW5ncy5tYXJnaW5SaWdodH1tbSAke3RoaXMucHJpbnRTZXR0aW5ncy5tYXJnaW5Cb3R0b219bW0gJHt0aGlzLnByaW50U2V0dGluZ3MubWFyZ2luTGVmdH1tbTsgCiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgICogewogICAgICAgICAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIGh0bWwsIGJvZHkgeyAKICAgICAgICAgICAgICBmb250LWZhbWlseTogIk1pY3Jvc29mdCBZYUhlaSIsIFNpbVN1biwgc2Fucy1zZXJpZjsgCiAgICAgICAgICAgICAgZm9udC1zaXplOiAke3RoaXMucHJpbnRTZXR0aW5ncy5mb250U2l6ZX1wdDsgCiAgICAgICAgICAgICAgY29sb3I6ICMwMDA7IAogICAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmY7IAogICAgICAgICAgICAgIG1hcmdpbjogMDsgCiAgICAgICAgICAgICAgcGFkZGluZzogJHt0aGlzLnByaW50U2V0dGluZ3MubWFyZ2luVG9wLzN9bW07CiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgICAgICAgLXdlYmtpdC1wcmludC1jb2xvci1hZGp1c3Q6IGV4YWN0OwogICAgICAgICAgICAgIHByaW50LWNvbG9yLWFkanVzdDogZXhhY3Q7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5jb250YWluZXIgewogICAgICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgICAgIG1heC13aWR0aDogMTAwMHB4OwogICAgICAgICAgICAgIG1hcmdpbjogMCBhdXRvOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAucHJpbnQtY29udGFpbmVyIHsKICAgICAgICAgICAgICBwYWRkaW5nOiAyMHB4OwogICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgICAgICAgICAgICAgY29sb3I6ICMwMDA7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5wcmludC1jb250ZW50IHsKICAgICAgICAgICAgICBtYXgtd2lkdGg6IDEwMDBweDsKICAgICAgICAgICAgICBtYXJnaW46IDAgYXV0bzsKICAgICAgICAgICAgICBmb250LWZhbWlseTogIlNpbVN1biIsICLlrovkvZMiLCBzZXJpZjsKICAgICAgICAgICAgICBjb2xvcjogIzAwMDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLnRyYW5zZmVyLWhlYWRlciB7CiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7CiAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMwMDA7CiAgICAgICAgICAgICAgcGFkZGluZy1ib3R0b206IDEwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC50cmFuc2Zlci1oZWFkZXIgLnRpdGxlIHsKICAgICAgICAgICAgICBmb250LXNpemU6ICR7dGhpcy5wcmludFNldHRpbmdzLmZvbnRTaXplICsgOH1wdDsKICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgICAgICAgICBtYXJnaW46IDAgMCAyMHB4IDA7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5oZWFkZXItaW5mbyB7CiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5oZWFkZXItaXRlbSB7CiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaGVhZGVyLWl0ZW0gLmxhYmVsIHsKICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDVweDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLnRyYW5zZmVyLWluZm8gewogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5pbmZvLWl0ZW0gewogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmluZm8taXRlbSAubGFiZWwgewogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgICAgICAgIG1pbi13aWR0aDogMTAwcHg7CiAgICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC50cmFuc2Zlci1kZXRhaWxzIGgzLAogICAgICAgICAgICAuYXBwcm92YWwtaW5mbyBoMyB7CiAgICAgICAgICAgICAgZm9udC1zaXplOiAke3RoaXMucHJpbnRTZXR0aW5ncy5mb250U2l6ZSArIDR9cHQ7CiAgICAgICAgICAgICAgbWFyZ2luOiAwIDAgMTVweCAwOwogICAgICAgICAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzQwOUVGRjsKICAgICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDEwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIHRhYmxlIHsKICAgICAgICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICAgICAgICBib3JkZXItY29sbGFwc2U6IGNvbGxhcHNlOwogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7CiAgICAgICAgICAgICAgdGFibGUtbGF5b3V0OiBmaXhlZDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgdGggewogICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7CiAgICAgICAgICAgICAgY29sb3I6ICMwMDA7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzAwMDsKICAgICAgICAgICAgICBwYWRkaW5nOiA4cHg7CiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICB0ZCB7CiAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzAwMDsKICAgICAgICAgICAgICBwYWRkaW5nOiA4cHg7CiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICAgIHdvcmQtd3JhcDogYnJlYWstd29yZDsKICAgICAgICAgICAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC50cmFuc2Zlci1mb290ZXIgewogICAgICAgICAgICAgIG1hcmdpbjogMzBweCAwOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuZm9vdGVyLWl0ZW0gewogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5zaWduYXR1cmUtbGluZSB7CiAgICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICAgICAgICAgIHdpZHRoOiA4MHB4OwogICAgICAgICAgICAgIGhlaWdodDogMXB4OwogICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDA7CiAgICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgIDwvc3R5bGU+CiAgICAgICAgPC9oZWFkPgogICAgICAgIDxib2R5PgogICAgICAgICAgPGRpdiBjbGFzcz0icHJpbnQtY29udGFpbmVyIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0icHJpbnQtY29udGVudCI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0idHJhbnNmZXItaGVhZGVyIj4KICAgICAgICAgICAgICAgIDxoMiBjbGFzcz0idGl0bGUiPuW6k+WtmOiwg+aLqOWNlTwvaDI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItaW5mbyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImhlYWRlci1pdGVtIj4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0ibGFiZWwiPuiwg+aLqOWNleWPt++8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0idmFsdWUiPiR7dGhpcy50cmFuc2ZlckRhdGEudHJhbnNmZXJDb2RlIHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImhlYWRlci1pdGVtIj4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0ibGFiZWwiPuiwg+aLqOaXpeacn++8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0idmFsdWUiPiR7dGhpcy5wYXJzZVRpbWUodGhpcy50cmFuc2ZlckRhdGEudHJhbnNmZXJUaW1lKSB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItaXRlbSI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImxhYmVsIj7nirbmgIHvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InZhbHVlIj4ke3RoaXMuZ2V0U3RhdHVzTmFtZSh0aGlzLnRyYW5zZmVyRGF0YS5zdGF0dXMpIHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAKICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJ0cmFuc2Zlci1pbmZvIj4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGZsZXg7IG1hcmdpbi1ib3R0b206IDE1cHg7Ij4KICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0id2lkdGg6IDUwJTsgZGlzcGxheTogZmxleDsiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWluLXdpZHRoOiAxMDBweDsiPuiwg+WHuuS7k+W6k++8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8c3Bhbj4ke3RoaXMudHJhbnNmZXJEYXRhLmZyb21XYXJlaG91c2VOYW1lIHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiA1MCU7IGRpc3BsYXk6IGZsZXg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7osIPlhaXku5PlupPvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4+JHt0aGlzLnRyYW5zZmVyRGF0YS50b1dhcmVob3VzZU5hbWUgfHwgJyd9PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsgbWFyZ2luLWJvdHRvbTogMTVweDsiPgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogNTAlOyBkaXNwbGF5OiBmbGV4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEwMHB4OyI+5Yi25Y2V5Lq677yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuPiR7dGhpcy50cmFuc2ZlckRhdGEuY3JlYXRlQnlOYW1lIHx8IHRoaXMudHJhbnNmZXJEYXRhLmNyZWF0ZUJ5IHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiA1MCU7IGRpc3BsYXk6IGZsZXg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7lrqHmoLjkurrvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4+JHt0aGlzLnRyYW5zZmVyRGF0YS5hdWRpdEJ5TmFtZSB8fCB0aGlzLnRyYW5zZmVyRGF0YS5hdWRpdEJ5IHx8ICfmnKrlrqHmoLgnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsgbWFyZ2luLWJvdHRvbTogMTVweDsiPgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogMTAwJTsgZGlzcGxheTogZmxleDsiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWluLXdpZHRoOiAxMDBweDsiPuWkh+azqO+8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8c3Bhbj4ke3RoaXMudHJhbnNmZXJEYXRhLnJlbWFyayB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0idHJhbnNmZXItZGV0YWlscyI+CiAgICAgICAgICAgICAgICA8aDM+6LCD5ouo54mp5ZOB5L+h5oGvPC9oMz4KICAgICAgICAgICAgICAgIDx0YWJsZT4KICAgICAgICAgICAgICAgICAgPHRoZWFkPgogICAgICAgICAgICAgICAgICAgIDx0cj4KICAgICAgICAgICAgICAgICAgICAgIDx0aD7luo/lj7c8L3RoPgogICAgICAgICAgICAgICAgICAgICAgPHRoPueJqeWTgee8lueggTwvdGg+CiAgICAgICAgICAgICAgICAgICAgICA8dGg+54mp5ZOB5ZCN56ewPC90aD4KICAgICAgICAgICAgICAgICAgICAgIDx0aD7osIPmi6jmlbDph488L3RoPgogICAgICAgICAgICAgICAgICAgICAgPHRoPuWkh+azqDwvdGg+CiAgICAgICAgICAgICAgICAgICAgPC90cj4KICAgICAgICAgICAgICAgICAgPC90aGVhZD4KICAgICAgICAgICAgICAgICAgPHRib2R5PgogICAgICAgICAgICAgICAgICAgICR7ZGV0YWlsc0hUTUx9CiAgICAgICAgICAgICAgICAgIDwvdGJvZHk+CiAgICAgICAgICAgICAgICA8L3RhYmxlPgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIAogICAgICAgICAgICAgIDxkaXYgY2xhc3M9InRyYW5zZmVyLWZvb3RlciI+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiAzMy4zMyU7IGRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBmbGV4LWVuZDsgaGVpZ2h0OiAzMHB4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEyMHB4OyI+6LCD5Ye65LuT5bqT6LSf6LSj5Lq677yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGlubGluZS1ibG9jazsgd2lkdGg6IDgwcHg7IGhlaWdodDogMXB4OyBiYWNrZ3JvdW5kLWNvbG9yOiAjMDAwOyBtYXJnaW4tbGVmdDogMTBweDsgcG9zaXRpb246IHJlbGF0aXZlOyBib3R0b206IDNweDsiPjwvZGl2PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0id2lkdGg6IDMzLjMzJTsgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGZsZXgtZW5kOyBoZWlnaHQ6IDMwcHg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTIwcHg7Ij7osIPlhaXku5PlupPotJ/otKPkurrvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogaW5saW5lLWJsb2NrOyB3aWR0aDogODBweDsgaGVpZ2h0OiAxcHg7IGJhY2tncm91bmQtY29sb3I6ICMwMDA7IG1hcmdpbi1sZWZ0OiAxMHB4OyBwb3NpdGlvbjogcmVsYXRpdmU7IGJvdHRvbTogM3B4OyI+PC9kaXY+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogMzMuMzMlOyBkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogZmxleC1lbmQ7IGhlaWdodDogMzBweDsiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWluLXdpZHRoOiAxMDBweDsiPuWuoeaguOS6uuetvuWtl++8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBpbmxpbmUtYmxvY2s7IHdpZHRoOiA4MHB4OyBoZWlnaHQ6IDFweDsgYmFja2dyb3VuZC1jb2xvcjogIzAwMDsgbWFyZ2luLWxlZnQ6IDEwcHg7IHBvc2l0aW9uOiByZWxhdGl2ZTsgYm90dG9tOiAzcHg7Ij48L2Rpdj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2JvZHk+CiAgICAgICAgPC9odG1sPgogICAgICBgOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlueKtuaAgeWQjeensCAqLwogICAgZ2V0U3RhdHVzTmFtZShzdGF0dXMpIHsKICAgICAgLy8g5L2/55So5LiO5bGP5bmV6aKE6KeI55u45ZCM55qE5a2X5YW45pig5bCE5pa55byPCiAgICAgIGNvbnN0IHN0YXR1c0RpY3QgPSB0aGlzLmRpY3QudHlwZS5pbnZlbnRvcnlfdHJhbnNmZXJfc3RhdHVzIHx8IFtdOwogICAgICBjb25zdCBzdGF0dXNJdGVtID0gc3RhdHVzRGljdC5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PT0gc3RhdHVzKTsKICAgICAgcmV0dXJuIHN0YXR1c0l0ZW0gPyBzdGF0dXNJdGVtLmxhYmVsIDogJyc7CiAgICB9LAogICAgCiAgICAvKiog5YWz6ZetICovCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICAvLyDliqDovb3miZPljbDorr7nva4KICAgIHRoaXMubG9hZFByaW50U2V0dGluZ3MoKTsKICB9Cn07Cg=="}, {"version": 3, "sources": ["print.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0KA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "print.vue", "sourceRoot": "src/views/inventory/transfer", "sourcesContent": ["<template>\n  <div class=\"print-container\">\n    <!-- 打印预览界面 -->\n    <div class=\"print-header\" v-if=\"!isPrinting\">\n      <el-button type=\"primary\" icon=\"el-icon-printer\" size=\"small\" @click=\"handlePrint\">\n        打印调拨单\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-setting\" size=\"small\" @click=\"handlePrintSettings\">\n        打印设置\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-close\" size=\"small\" @click=\"handleClose\">\n        关闭\n      </el-button>\n    </div>\n    \n    <!-- 打印设置对话框 -->\n    <el-dialog title=\"打印设置\" :visible.sync=\"printSettingsVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"printSettingsForm\" :model=\"printSettings\" label-width=\"100px\">\n        <el-form-item label=\"页面边距\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginTop\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginRight\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginBottom\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginLeft\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n          </el-row>\n          <div class=\"margin-labels\">\n            <span>上</span>\n            <span>右</span>\n            <span>下</span>\n            <span>左</span>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"字体大小\">\n          <el-slider v-model=\"printSettings.fontSize\" :min=\"8\" :max=\"20\" show-input></el-slider>\n        </el-form-item>\n        \n        <el-form-item label=\"页面方向\">\n          <el-radio-group v-model=\"printSettings.orientation\">\n            <el-radio label=\"portrait\">纵向</el-radio>\n            <el-radio label=\"landscape\">横向</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"纸张大小\">\n          <el-select v-model=\"printSettings.paperSize\" placeholder=\"请选择纸张大小\">\n            <el-option label=\"A4\" value=\"A4\"></el-option>\n            <el-option label=\"A5\" value=\"A5\"></el-option>\n            <el-option label=\"B5\" value=\"B5\"></el-option>\n            <el-option label=\"Letter\" value=\"letter\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"printSettingsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"savePrintSettings\">确 定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 打印内容 -->\n    <div class=\"print-content\" id=\"printContent\">\n      <div class=\"transfer-header\">\n        <h2 class=\"title\">库存调拨单</h2>\n        <div class=\"header-info\">\n          <div class=\"header-item\">\n            <span class=\"label\">调拨单号：</span>\n            <span class=\"value\">{{ transferData.transferCode }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">调拨日期：</span>\n            <span class=\"value\">{{ parseTime(transferData.transferTime) }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">状态：</span>\n            <span class=\"value\">\n              <dict-tag :options=\"dict.type.inventory_transfer_status\" :value=\"transferData.status\" />\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"transfer-info\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">调出仓库：</span>\n              <span class=\"value\">{{ transferData.fromWarehouseName }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">调入仓库：</span>\n              <span class=\"value\">{{ transferData.toWarehouseName }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">制单人：</span>\n              <span class=\"value\">{{ transferData.createByName || transferData.createBy }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">审核人：</span>\n              <span class=\"value\">{{ transferData.auditByName || transferData.auditBy || '未审核' }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <div class=\"info-item\">\n              <span class=\"label\">备注：</span>\n              <span class=\"value\">{{ transferData.remark }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <div class=\"transfer-details\">\n        <h3>调拨物品信息</h3>\n        <el-table :data=\"transferData.details\" class=\"detail-table\" show-summary>\n          <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n          <el-table-column label=\"物品编码\" prop=\"productCode\" />\n          <el-table-column label=\"物品名称\" prop=\"productName\" />\n          <el-table-column label=\"调拨数量\" prop=\"quantity\" />\n          <el-table-column label=\"备注\" prop=\"remark\" />\n        </el-table>\n      </div>\n      \n      <div class=\"transfer-footer\">\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 120px;\">调出仓库负责人：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 120px;\">调入仓库负责人：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getInventoryTransfer } from \"@/api/inventory/transfer\";\nimport { getBatchUserRealNames } from \"@/utils/userUtils\";\n\nexport default {\n  name: \"TransferPrint\",\n  dicts: ['inventory_transfer_status'],\n  data() {\n    return {\n      loading: false,\n      isPrinting: false,\n      printSettingsVisible: false,\n      transferData: {\n        transferCode: \"\",\n        fromWarehouseName: \"\",\n        toWarehouseName: \"\",\n        transferTime: null,\n        status: \"\",\n        createBy: \"\",\n        createByName: \"\",\n        auditBy: \"\",\n        auditByName: \"\",\n        remark: \"\",\n        details: []\n      },\n      transferId: null,\n      printSettings: {\n        marginTop: 15,\n        marginRight: 15,\n        marginBottom: 15,\n        marginLeft: 15,\n        fontSize: 12,\n        orientation: 'portrait',\n        paperSize: 'A4'\n      }\n    };\n  },\n  created() {\n    this.transferId = this.$route.params.transferId || this.$route.params.id || this.$route.query.id;\n    console.log('调拨单打印页面初始化，调拨单ID:', this.transferId);\n    if (!this.transferId) {\n      this.$message.error('缺少调拨单ID参数');\n      return;\n    }\n    this.getTransferData();\n  },\n  methods: {\n    /** 获取调拨单信息 */\n    getTransferData() {\n      this.loading = true;\n      getInventoryTransfer(this.transferId).then(response => {\n        this.transferData = response.data;\n        \n        // 获取用户真实姓名\n        const userNames = [\n          this.transferData.createBy,\n          this.transferData.auditBy\n        ].filter(name => name); // 过滤空值\n\n        if (userNames.length > 0) {\n          getBatchUserRealNames(userNames).then(nameMap => {\n            // 更新用户真实姓名\n            if (this.transferData.createBy && nameMap[this.transferData.createBy]) {\n              this.transferData.createByName = nameMap[this.transferData.createBy];\n            }\n            if (this.transferData.auditBy && nameMap[this.transferData.auditBy]) {\n              this.transferData.auditByName = nameMap[this.transferData.auditBy];\n            }\n          });\n        }\n        \n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n        this.$message.error(\"获取调拨单信息失败\");\n      });\n    },\n    \n    /** 处理打印设置 */\n    handlePrintSettings() {\n      this.printSettingsVisible = true;\n    },\n    \n    /** 保存打印设置 */\n    savePrintSettings() {\n      // 保存到本地存储\n      localStorage.setItem('transferPrintSettings', JSON.stringify(this.printSettings));\n      this.printSettingsVisible = false;\n      this.$message.success('打印设置已保存');\n    },\n    \n    /** 加载打印设置 */\n    loadPrintSettings() {\n      const savedSettings = localStorage.getItem('transferPrintSettings');\n      if (savedSettings) {\n        this.printSettings = JSON.parse(savedSettings);\n      }\n    },\n    \n    /** 打印 */\n    handlePrint() {\n      this.isPrinting = true;\n      \n      // 确保数据已加载\n      if (!this.transferData.transferCode) {\n        this.$message.warning('数据还在加载中，请稍后再试');\n        this.isPrinting = false;\n        return;\n      }\n      \n      this.$nextTick(() => {\n        // 创建新窗口进行打印\n        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');\n        \n        // 生成打印内容\n        const printContent = this.generatePrintHTML();\n        \n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        \n        // 等待内容加载完成后打印\n        printWindow.onload = () => {\n          setTimeout(() => {\n            printWindow.print();\n            // 打印完成后不自动关闭窗口，让用户手动关闭\n            this.isPrinting = false;\n          }, 500);\n        };\n      });\n    },\n    \n    /** 生成打印页面HTML */\n    generatePrintHTML() {\n      const details = this.transferData.details || [];\n      let detailsHTML = '';\n      \n      details.forEach((item, index) => {\n        detailsHTML += `\n          <tr>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${index + 1}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productCode || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productName || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.quantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.remark || ''}</td>\n          </tr>\n        `;\n      });\n      \n      // 根据设置确定页面方向和纸张大小\n      let pageSizeStyle = '';\n      if (this.printSettings.orientation === 'landscape') {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;\n      } else {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;\n      }\n      \n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>库存调拨单打印</title>\n          <style>\n            @page { \n              ${pageSizeStyle}\n              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; \n            }\n            \n            * {\n              box-sizing: border-box;\n            }\n            \n            html, body { \n              font-family: \"Microsoft YaHei\", SimSun, sans-serif; \n              font-size: ${this.printSettings.fontSize}pt; \n              color: #000; \n              background: #fff; \n              margin: 0; \n              padding: ${this.printSettings.marginTop/3}mm;\n              width: 100%;\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n            \n            .container {\n              width: 100%;\n              max-width: 1000px;\n              margin: 0 auto;\n            }\n            \n            .print-container {\n              padding: 20px;\n              background-color: #fff;\n              color: #000;\n            }\n            \n            .print-content {\n              max-width: 1000px;\n              margin: 0 auto;\n              font-family: \"SimSun\", \"宋体\", serif;\n              color: #000;\n            }\n            \n            .transfer-header {\n              text-align: center;\n              margin-bottom: 30px;\n              border-bottom: 2px solid #000;\n              padding-bottom: 10px;\n            }\n            \n            .transfer-header .title {\n              font-size: ${this.printSettings.fontSize + 8}pt;\n              font-weight: bold;\n              margin: 0 0 20px 0;\n            }\n            \n            .header-info {\n              display: flex;\n              justify-content: space-between;\n            }\n            \n            .header-item {\n              display: flex;\n              align-items: center;\n            }\n            \n            .header-item .label {\n              font-weight: bold;\n              margin-right: 5px;\n            }\n            \n            .transfer-info {\n              margin-bottom: 30px;\n            }\n            \n            .info-item {\n              margin-bottom: 15px;\n              display: flex;\n              align-items: flex-start;\n            }\n            \n            .info-item .label {\n              font-weight: bold;\n              min-width: 100px;\n              flex-shrink: 0;\n            }\n            \n            .transfer-details h3,\n            .approval-info h3 {\n              font-size: ${this.printSettings.fontSize + 4}pt;\n              margin: 0 0 15px 0;\n              border-left: 4px solid #409EFF;\n              padding-left: 10px;\n            }\n            \n            table {\n              width: 100%;\n              border-collapse: collapse;\n              margin-bottom: 30px;\n              table-layout: fixed;\n            }\n            \n            th {\n              background-color: #f5f5f5;\n              color: #000;\n              font-weight: bold;\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n            }\n            \n            td {\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n            \n            .transfer-footer {\n              margin: 30px 0;\n            }\n            \n            .footer-item {\n              margin-bottom: 15px;\n            }\n            \n            .signature-line {\n              display: inline-block;\n              width: 80px;\n              height: 1px;\n              background-color: #000;\n              margin-left: 10px;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"print-container\">\n            <div class=\"print-content\">\n              <div class=\"transfer-header\">\n                <h2 class=\"title\">库存调拨单</h2>\n                <div class=\"header-info\">\n                  <div class=\"header-item\">\n                    <span class=\"label\">调拨单号：</span>\n                    <span class=\"value\">${this.transferData.transferCode || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">调拨日期：</span>\n                    <span class=\"value\">${this.parseTime(this.transferData.transferTime) || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">状态：</span>\n                    <span class=\"value\">${this.getStatusName(this.transferData.status) || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"transfer-info\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">调出仓库：</span>\n                    <span>${this.transferData.fromWarehouseName || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">调入仓库：</span>\n                    <span>${this.transferData.toWarehouseName || ''}</span>\n                  </div>\n                </div>\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">制单人：</span>\n                    <span>${this.transferData.createByName || this.transferData.createBy || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人：</span>\n                    <span>${this.transferData.auditByName || this.transferData.auditBy || '未审核'}</span>\n                  </div>\n                </div>\n                \n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 100%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">备注：</span>\n                    <span>${this.transferData.remark || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"transfer-details\">\n                <h3>调拨物品信息</h3>\n                <table>\n                  <thead>\n                    <tr>\n                      <th>序号</th>\n                      <th>物品编码</th>\n                      <th>物品名称</th>\n                      <th>调拨数量</th>\n                      <th>备注</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    ${detailsHTML}\n                  </tbody>\n                </table>\n              </div>\n              \n              <div class=\"transfer-footer\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 120px;\">调出仓库负责人：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 120px;\">调入仓库负责人：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    \n    /** 获取状态名称 */\n    getStatusName(status) {\n      // 使用与屏幕预览相同的字典映射方式\n      const statusDict = this.dict.type.inventory_transfer_status || [];\n      const statusItem = statusDict.find(item => item.value === status);\n      return statusItem ? statusItem.label : '';\n    },\n    \n    /** 关闭 */\n    handleClose() {\n      this.$router.go(-1);\n    }\n  },\n  mounted() {\n    // 加载打印设置\n    this.loadPrintSettings();\n  }\n};\n</script>\n\n<style scoped>\n.print-container {\n  padding: 20px;\n  background-color: #fff;\n  color: #000;\n}\n\n.print-header {\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.print-content {\n  max-width: 1000px;\n  margin: 0 auto;\n  font-family: \"SimSun\", \"宋体\", serif;\n  color: #000;\n}\n\n.transfer-header {\n  text-align: center;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #000;\n  padding-bottom: 10px;\n}\n\n.transfer-header .title {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0 0 20px 0;\n}\n\n.header-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.header-item {\n  display: flex;\n  align-items: center;\n}\n\n.header-item .label {\n  font-weight: bold;\n}\n\n.transfer-info {\n  margin-bottom: 30px;\n}\n\n.info-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.info-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.detail-table {\n  margin-bottom: 30px;\n}\n\n.detail-table ::v-deep .el-table__header th {\n  background-color: #f5f5f5;\n  color: #000;\n}\n\n.detail-table ::v-deep .el-table__row td {\n  color: #000;\n}\n\n.transfer-details h3,\n.approval-info h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n  border-left: 4px solid #409EFF;\n  padding-left: 10px;\n}\n\n.transfer-footer {\n  margin: 30px 0;\n}\n\n.footer-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-end;\n  height: 30px;\n}\n\n.footer-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.signature-line {\n  display: inline-block;\n  width: 80px;\n  height: 1px;\n  background-color: #000;\n  margin-left: 5px;\n  position: relative;\n  bottom: 3px;\n}\n\n.margin-labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n</style>"]}]}