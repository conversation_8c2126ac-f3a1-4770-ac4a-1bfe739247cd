<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :query-params="queryParams"
      :warehouse-options="warehouseOptions"
      :dict-options="dict.type.inventory_status"
      :show-search="showSearch"
      :is-mobile="isMobile"
      @query="handleQuery"
      @reset="resetQuery"
      @update:queryParams="queryParams = { ...queryParams, ...$event }"
    />

    <!-- 操作按钮栏 -->
    <ActionBar
      :single="single"
      :multiple="multiple"
      :show-search.sync="showSearch"
      :is-mobile="isMobile"
      @add="handleAdd"
      @update="handleUpdate"
      @delete="handleDelete"
      @export="handleExport"
      @report="handleReport"
      @alert-report="handleAlertReport"
      @threshold="handleThreshold"
      @analysis="handleAnalysis"
      @product-stock="handleProductStock"
      @refresh="getList"
    />

    <!-- 移动端库存列表 -->
    <MobileStockList
      v-if="isMobile"
      :data="stockList"
      :loading="loading"
      @view="handleView"
      @update="handleUpdate"
      @delete="handleDelete"
    />

    <!-- 桌面端数据表格 -->
    <DataTable
      v-else
      :data="stockList"
      :loading="loading"
      @selection-change="handleSelectionChange"
      @update="handleUpdate"
      @delete="handleDelete"
    />

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 表单对话框 -->
    <FormDialog
      :visible.sync="open"
      :title="title"
      :form="form"
      :rules="rules"
      :product-options="productOptions"
      :warehouse-options="warehouseOptions"
      :is-mobile="isMobile"
      @submit="submitForm"
      @cancel="cancel"
      @product-change="handleProductChange"
      ref="formDialog"
    />
  </div>
</template>

<script>
import { listStock, getStock, delStock, addStock, updateStock } from "@/api/inventory/stock";
import { optionselect } from "@/api/system/warehouse";
import { listProduct } from "@/api/product/info";
import { 
  SearchForm, 
  ActionBar, 
  DataTable, 
  MobileStockList, 
  FormDialog 
} from './components';
import stockDataProcessingMixin from './mixins/stockDataProcessing.js';

export default {
  name: "Stock",
  components: {
    SearchForm,
    ActionBar,
    DataTable,
    MobileStockList,
    FormDialog
  },
  mixins: [stockDataProcessingMixin],
  dicts: ['inventory_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库存表格数据
      stockList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 仓库选项
      warehouseOptions: [],
      // 物品选项
      productOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: null,
        productCode: null,
        warehouseId: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productId: [
          { required: true, message: "物品不能为空", trigger: "change" }
        ],
        warehouseId: [
          { required: true, message: "仓库不能为空", trigger: "change" }
        ],
        quantity: [
          { required: true, message: "库存数量不能为空", trigger: "blur" }
        ]
      },
      // 移动端搜索折叠面板
      mobileSearchVisible: ['search']
    };
  },
  computed: {
    /** 是否为移动端 */
    isMobile() {
      return this.$store.getters.device === 'mobile'
    }
  },
  created() {
    this.getList();
    this.getWarehouseOptions();
    this.getProductOptions();
    // 自动弹窗详情
    if (this.$route.query.id) {
      this.openStockDetailById(this.$route.query.id);
    }
  },
  watch: {
    '$route.query.id'(newId) {
      if (newId) {
        this.openStockDetailById(newId);
      }
    }
  },
  methods: {
    /** 查询库存列表 */
    getList() {
      this.loading = true;
      listStock(this.queryParams).then(response => {
        this.stockList = this.processStockList(response.rows || []);
        this.total = response.total || 0;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
        this.handleApiError(error, '查询库存列表失败');
      });
    },
    /** 获取仓库选项 */
    getWarehouseOptions() {
      optionselect().then(response => {
        this.warehouseOptions = this.processWarehouseOptions(response.data || response.rows || []);
      }).catch(error => {
        this.handleApiError(error, '获取仓库选项失败');
      });
    },
    /** 获取物品选项 */
    getProductOptions() {
      listProduct().then(response => {
        this.productOptions = this.processProductOptions(response.rows || []);
      }).catch(error => {
        this.handleApiError(error, '获取物品选项失败');
      });
    },
    // 物品选择事件
    handleProductChange(value) {
      // 可以在这里处理物品选择后的逻辑
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        inventoryId: null,
        productId: null,
        warehouseId: null,
        quantity: 0,
        minQuantity: 0,
        maxQuantity: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.inventoryId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加库存信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const inventoryId = row.inventoryId || this.ids[0];
      getStock(inventoryId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改库存信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.formDialog.validate(valid => {
        if (valid) {
          // 使用混入中的验证方法
          const validation = this.validateStockForm(this.form);
          if (!validation.valid) {
            this.$message.error(validation.errors[0]);
            return;
          }

          if (this.form.inventoryId != null) {
            updateStock(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              this.handleApiError(error, "修改失败");
            });
          } else {
            addStock(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              this.handleApiError(error, "新增失败");
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // 如果传入了row参数，说明是单行删除；否则是批量删除
      const inventoryIds = row ? row.inventoryId : this.ids;
      
      // 检查是否有选中的数据
      if (!inventoryIds || (Array.isArray(inventoryIds) && inventoryIds.length === 0)) {
        this.$modal.msgError("请选择要删除的数据");
        return;
      }
      
      const confirmText = row 
        ? `是否确认删除库存编号为"${inventoryIds}"的数据项？`
        : `是否确认删除选中的${this.ids.length}条数据？`;
        
      this.$modal.confirm(confirmText).then(() => {
        return delStock(inventoryIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出当前筛选条件下的库存数据？').then(() => {
        const exportData = this.formatExportData(this.stockList);
        const filename = this.generateExportFileName();
        
        this.download('api/v1/inventory/stocks/export', {
          ...this.queryParams
        }, filename);
      }).catch(() => {});
    },
    /** 报表按钮操作 */
    handleReport() {
      this.$router.push({ path: "/report/stock/index" });
    },
    /** 预警报表按钮操作 */
    handleAlertReport() {
      this.$router.push({ path: "/report/alert/index" });
    },
    /** 阈值设置按钮操作 */
    handleThreshold() {
      this.$router.push({ path: "/inventory/batch/threshold" });
    },
    /** 高级分析按钮操作 */
    handleAnalysis() {
      this.$confirm('请选择分析类型', '高级分析', {
        confirmButtonText: '周转率分析',
        cancelButtonText: '价值分析',
        type: 'info'
      }).then(() => {
        // 周转率分析
        this.$router.push({ path: "/report/analysis/turnover" });
      }).catch(() => {
        // 价值分析
        this.$router.push({ path: "/report/analysis/value" });
      });
    },
    /** 物品库存按钮操作 */
    handleProductStock() {
      this.$router.push({ path: "/inventory/stock/product" });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push({ path: "/inventory/stock/detail/" + row.inventoryId });
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.$modal.msgError("审核功能待实现");
    },
    /** 打印按钮操作 */
    handlePrint(row) {
      // 创建打印窗口
      const printWindow = window.open('', '_blank', 'width=800,height=600');

      // 生成打印内容
      const printContent = this.generateStockPrintHTML(row);

      printWindow.document.write(printContent);
      printWindow.document.close();

      // 等待内容加载完成后打印
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
        }, 500);
      };
    },

    /** 生成库存打印HTML */
    generateStockPrintHTML(stockData) {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>库存信息打印</title>
          <style>
            body { font-family: "Microsoft YaHei", SimSun, sans-serif; font-size: 12pt; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .title { font-size: 18pt; font-weight: bold; margin-bottom: 10px; }
            .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .info-table th, .info-table td { border: 1px solid #000; padding: 8px; text-align: left; }
            .info-table th { background-color: #f5f5f5; font-weight: bold; }
            .footer { margin-top: 30px; text-align: right; }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">库存信息单</div>
            <div>打印时间：${new Date().toLocaleString()}</div>
          </div>

          <table class="info-table">
            <tr>
              <th width="15%">物品编码</th>
              <td width="35%">${stockData.productCode || ''}</td>
              <th width="15%">物品名称</th>
              <td width="35%">${stockData.productName || ''}</td>
            </tr>
            <tr>
              <th>仓库名称</th>
              <td>${stockData.warehouseName || ''}</td>
              <th>库位</th>
              <td>${stockData.locationName || ''}</td>
            </tr>
            <tr>
              <th>当前库存</th>
              <td>${stockData.quantity || 0}</td>
              <th>单位</th>
              <td>${stockData.unit || ''}</td>
            </tr>
            <tr>
              <th>单价</th>
              <td>${stockData.price || 0}</td>
              <th>总价值</th>
              <td>${(stockData.quantity * stockData.price || 0).toFixed(2)}</td>
            </tr>
            <tr>
              <th>最低库存</th>
              <td>${stockData.minQuantity || 0}</td>
              <th>最高库存</th>
              <td>${stockData.maxQuantity || 0}</td>
            </tr>
            <tr>
              <th>状态</th>
              <td colspan="3">${this.getDictLabel(this.dict.type.inventory_status, stockData.status)}</td>
            </tr>
          </table>

          <div class="footer">
            <div>打印人：${this.$store.state.user.name}</div>
          </div>
        </body>
        </html>
      `;
    },
    /** 自动弹窗详情 */
    openStockDetailById(id) {
      if (!id) return;
      this.loading = true;
      this.$api.getStock(id).then(res => {
        this.form = res.data;
        this.title = '库存详情';
        this.open = true;
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 分页样式 */
.el-pagination {
  text-align: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
}

/* 加载状态优化 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}
</style>
