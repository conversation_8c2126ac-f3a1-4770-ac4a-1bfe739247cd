<template>
  <div class="print-container">
    <!-- 打印预览界面 -->
    <div class="print-header" v-if="!isPrinting">
      <el-button type="primary" icon="el-icon-printer" size="small" @click="handlePrint">
        打印出库单
      </el-button>
      <el-button type="default" icon="el-icon-setting" size="small" @click="handlePrintSettings">
        打印设置
      </el-button>
      <el-button type="default" icon="el-icon-close" size="small" @click="handleClose">
        关闭
      </el-button>
    </div>
    
    <!-- 打印设置对话框 -->
    <el-dialog title="打印设置" :visible.sync="printSettingsVisible" width="500px" append-to-body>
      <el-form ref="printSettingsForm" :model="printSettings" label-width="100px">
        <el-form-item label="页面边距">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-input v-model.number="printSettings.marginTop" size="small">
                <template slot="append">mm</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model.number="printSettings.marginRight" size="small">
                <template slot="append">mm</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model.number="printSettings.marginBottom" size="small">
                <template slot="append">mm</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model.number="printSettings.marginLeft" size="small">
                <template slot="append">mm</template>
              </el-input>
            </el-col>
          </el-row>
          <div class="margin-labels">
            <span>上</span>
            <span>右</span>
            <span>下</span>
            <span>左</span>
          </div>
        </el-form-item>
        
        <el-form-item label="字体大小">
          <el-slider v-model="printSettings.fontSize" :min="8" :max="20" show-input></el-slider>
        </el-form-item>
        
        <el-form-item label="页面方向">
          <el-radio-group v-model="printSettings.orientation">
            <el-radio label="portrait">纵向</el-radio>
            <el-radio label="landscape">横向</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="纸张大小">
          <el-select v-model="printSettings.paperSize" placeholder="请选择纸张大小">
            <el-option label="A4" value="A4"></el-option>
            <el-option label="A5" value="A5"></el-option>
            <el-option label="B5" value="B5"></el-option>
            <el-option label="Letter" value="letter"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="printSettingsVisible = false">取 消</el-button>
        <el-button type="primary" @click="savePrintSettings">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 打印内容 -->
    <div class="print-content" id="printContent">
      <div class="out-header">
        <h2 class="title">出库单</h2>
        <div class="header-info">
          <div class="header-item">
            <span class="label">出库单号：</span>
            <span class="value">{{ outData.outCode }}</span>
          </div>
          <div class="header-item">
            <span class="label">出库日期：</span>
            <span class="value">{{ parseTime(outData.outTime) }}</span>
          </div>
          <div class="header-item">
            <span class="label">状态：</span>
            <span class="value">
              <dict-tag :options="dict.type.inventory_out_status" :value="outData.status" />
            </span>
          </div>
        </div>
      </div>
      
      <div class="out-info">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">仓库名称：</span>
              <span class="value">{{ outData.warehouseName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">出库类型：</span>
              <span class="value">
                <dict-tag :options="dict.type.inventory_out_type" :value="outData.outType" />
              </span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">制单人：</span>
              <span class="value">{{ outData.createByName || outData.createBy }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">审核人：</span>
              <span class="value">{{ outData.auditByName || outData.auditBy || '未审核' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <span class="label">备注：</span>
              <span class="value">{{ outData.remark }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <div class="out-details">
        <h3>出库物品信息</h3>
        <el-table :data="outData.details" class="detail-table" show-summary>
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column label="物品编码" prop="productCode" />
          <el-table-column label="物品名称" prop="productName" />
          <el-table-column label="出库数量" prop="quantity" />
          <el-table-column label="单价" prop="price" :formatter="formatAmount" />
          <el-table-column label="金额" prop="amount" :formatter="formatAmount" />
          <el-table-column label="备注" prop="remark" />
        </el-table>
      </div>
      
      <div class="out-footer">
        <div style="display: flex; margin-bottom: 15px;">
          <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
            <span style="font-weight: bold; min-width: 100px;">仓库管理员：</span>
            <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;"></div>
          </div>
          <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
            <span style="font-weight: bold; min-width: 100px;">审核人签字：</span>
            <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;"></div>
          </div>
          <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
            <span style="font-weight: bold; min-width: 100px;">日期：</span>
            <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getInventoryOut } from "@/api/inventory/out";
import { getBatchUserRealNames } from "@/utils/userUtils";

export default {
  name: "OutPrint",
  dicts: ['inventory_out_type', 'inventory_out_status'],
  data() {
    return {
      loading: false,
      isPrinting: false,
      printSettingsVisible: false,
      outData: {
        outCode: "",
        warehouseName: "",
        outTime: null,
        outType: "",
        status: "",
        createBy: "",
        createByName: "",
        auditBy: "",
        auditByName: "",
        remark: "",
        details: []
      },
      outId: null,
      printSettings: {
        marginTop: 15,
        marginRight: 15,
        marginBottom: 15,
        marginLeft: 15,
        fontSize: 12,
        orientation: 'portrait',
        paperSize: 'A4'
      }
    };
  },
  created() {
    this.outId = this.$route.params.outId || this.$route.params.id || this.$route.query.id;
    console.log('出库单打印页面初始化，出库单ID:', this.outId);
    if (!this.outId) {
      this.$message.error('缺少出库单ID参数');
      return;
    }
    this.getOutData();
  },
  methods: {
    /** 获取出库单信息 */
    getOutData() {
      this.loading = true;
      getInventoryOut(this.outId).then(response => {
        this.outData = response.data;
        
        // 获取用户真实姓名
        const userNames = [
          this.outData.createBy,
          this.outData.auditBy
        ].filter(name => name); // 过滤空值

        if (userNames.length > 0) {
          getBatchUserRealNames(userNames).then(nameMap => {
            // 更新用户真实姓名
            if (this.outData.createBy && nameMap[this.outData.createBy]) {
              this.outData.createByName = nameMap[this.outData.createBy];
            }
            if (this.outData.auditBy && nameMap[this.outData.auditBy]) {
              this.outData.auditByName = nameMap[this.outData.auditBy];
            }
          });
        }
        
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.$message.error("获取出库单信息失败");
      });
    },
    
    /** 处理打印设置 */
    handlePrintSettings() {
      this.printSettingsVisible = true;
    },
    
    /** 保存打印设置 */
    savePrintSettings() {
      // 保存到本地存储
      localStorage.setItem('outPrintSettings', JSON.stringify(this.printSettings));
      this.printSettingsVisible = false;
      this.$message.success('打印设置已保存');
    },
    
    /** 加载打印设置 */
    loadPrintSettings() {
      const savedSettings = localStorage.getItem('outPrintSettings');
      if (savedSettings) {
        this.printSettings = JSON.parse(savedSettings);
      }
    },
    
    /** 格式化金额 */
    formatAmount(row, column, cellValue) {
      if (!cellValue) return '0.00';
      return parseFloat(cellValue).toFixed(2);
    },
    
    /** 打印 */
    handlePrint() {
      this.isPrinting = true;
      
      // 确保数据已加载
      if (!this.outData.outCode) {
        this.$message.warning('数据还在加载中，请稍后再试');
        this.isPrinting = false;
        return;
      }
      
      this.$nextTick(() => {
        // 创建新窗口进行打印
        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');
        
        // 生成打印内容
        const printContent = this.generatePrintHTML();
        
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        // 等待内容加载完成后打印
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            // 打印完成后不自动关闭窗口，让用户手动关闭
            this.isPrinting = false;
          }, 500);
        };
      });
    },
    
    /** 生成打印页面HTML */
    generatePrintHTML() {
      const details = this.outData.details || [];
      let detailsHTML = '';
      
      details.forEach((item, index) => {
        detailsHTML += `
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${index + 1}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.productCode || ''}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.productName || ''}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.quantity || ''}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${this.formatAmount(null, null, item.price)}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${this.formatAmount(null, null, item.amount)}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.remark || ''}</td>
          </tr>
        `;
      });
      
      // 根据设置确定页面方向和纸张大小
      let pageSizeStyle = '';
      if (this.printSettings.orientation === 'landscape') {
        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;
      } else {
        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;
      }
      
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>出库单打印</title>
          <style>
            @page { 
              ${pageSizeStyle}
              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; 
            }
            
            * {
              box-sizing: border-box;
            }
            
            html, body { 
              font-family: "Microsoft YaHei", SimSun, sans-serif; 
              font-size: ${this.printSettings.fontSize}pt; 
              color: #000; 
              background: #fff; 
              margin: 0; 
              padding: ${this.printSettings.marginTop/3}mm;
              width: 100%;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }
            
            .container {
              width: 100%;
              max-width: 1000px;
              margin: 0 auto;
            }
            
            .print-container {
              padding: 20px;
              background-color: #fff;
              color: #000;
            }
            
            .print-content {
              max-width: 1000px;
              margin: 0 auto;
              font-family: "SimSun", "宋体", serif;
              color: #000;
            }
            
            .out-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #000;
              padding-bottom: 10px;
            }
            
            .out-header .title {
              font-size: ${this.printSettings.fontSize + 8}pt;
              font-weight: bold;
              margin: 0 0 20px 0;
            }
            
            .header-info {
              display: flex;
              justify-content: space-between;
            }
            
            .header-item {
              display: flex;
              align-items: center;
            }
            
            .header-item .label {
              font-weight: bold;
              margin-right: 5px;
            }
            
            .out-info {
              margin-bottom: 30px;
            }
            
            .info-item {
              margin-bottom: 15px;
              display: flex;
              align-items: flex-start;
            }
            
            .info-item .label {
              font-weight: bold;
              min-width: 100px;
              flex-shrink: 0;
            }
            
            .out-details h3,
            .approval-info h3 {
              font-size: ${this.printSettings.fontSize + 4}pt;
              margin: 0 0 15px 0;
              border-left: 4px solid #409EFF;
              padding-left: 10px;
            }
            
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 30px;
              table-layout: fixed;
            }
            
            th {
              background-color: #f5f5f5;
              color: #000;
              font-weight: bold;
              border: 1px solid #000;
              padding: 8px;
              text-align: center;
            }
            
            td {
              border: 1px solid #000;
              padding: 8px;
              text-align: center;
              word-wrap: break-word;
              word-break: break-all;
            }
            
            .out-footer {
              margin: 30px 0;
            }
            
            .footer-item {
              margin-bottom: 15px;
            }
            
            .signature-line {
              display: inline-block;
              width: 80px;
              height: 1px;
              background-color: #000;
              margin-left: 10px;
            }
          </style>
        </head>
        <body>
          <div class="print-container">
            <div class="print-content">
              <div class="out-header">
                <h2 class="title">出库单</h2>
                <div class="header-info">
                  <div class="header-item">
                    <span class="label">出库单号：</span>
                    <span class="value">${this.outData.outCode || ''}</span>
                  </div>
                  <div class="header-item">
                    <span class="label">出库日期：</span>
                    <span class="value">${this.parseTime(this.outData.outTime) || ''}</span>
                  </div>
                  <div class="header-item">
                    <span class="label">状态：</span>
                    <span class="value">${this.getStatusName(this.outData.status) || ''}</span>
                  </div>
                </div>
              </div>
              
              <div class="out-info">
                <div style="display: flex; margin-bottom: 15px;">
                  <div style="width: 50%; display: flex;">
                    <span style="font-weight: bold; min-width: 100px;">仓库名称：</span>
                    <span>${this.outData.warehouseName || ''}</span>
                  </div>
                  <div style="width: 50%; display: flex;">
                    <span style="font-weight: bold; min-width: 100px;">出库类型：</span>
                    <span>${this.getOutTypeName(this.outData.outType) || ''}</span>
                  </div>
                </div>
                <div style="display: flex; margin-bottom: 15px;">
                  <div style="width: 50%; display: flex;">
                    <span style="font-weight: bold; min-width: 100px;">制单人：</span>
                    <span>${this.outData.createByName || this.outData.createBy || ''}</span>
                  </div>
                  <div style="width: 50%; display: flex;">
                    <span style="font-weight: bold; min-width: 100px;">审核人：</span>
                    <span>${this.outData.auditByName || this.outData.auditBy || '未审核'}</span>
                  </div>
                </div>
                
                <div style="display: flex; margin-bottom: 15px;">
                  <div style="width: 100%; display: flex;">
                    <span style="font-weight: bold; min-width: 100px;">备注：</span>
                    <span>${this.outData.remark || ''}</span>
                  </div>
                </div>
              </div>
              
              <div class="out-details">
                <h3>出库物品信息</h3>
                <table>
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>物品编码</th>
                      <th>物品名称</th>
                      <th>出库数量</th>
                      <th>单价</th>
                      <th>金额</th>
                      <th>备注</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${detailsHTML}
                  </tbody>
                </table>
              </div>
              
              <div class="out-footer">
                <div style="display: flex; margin-bottom: 15px;">
                  <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
                    <span style="font-weight: bold; min-width: 100px;">仓库管理员：</span>
                    <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;"></div>
                  </div>
                  <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
                    <span style="font-weight: bold; min-width: 100px;">审核人签字：</span>
                    <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;"></div>
                  </div>
                  <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
                    <span style="font-weight: bold; min-width: 100px;">日期：</span>
                    <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </body>
        </html>
      `;
    },
    
    /** 获取状态名称 */
    getStatusName(status) {
      // 使用与屏幕预览相同的字典映射方式
      const statusDict = this.dict.type.inventory_out_status || [];
      const statusItem = statusDict.find(item => item.value === status);
      return statusItem ? statusItem.label : '';
    },
    
    /** 获取出库类型名称 */
    getOutTypeName(outType) {
      // 使用与屏幕预览相同的字典映射方式
      const typeDict = this.dict.type.inventory_out_type || [];
      const typeItem = typeDict.find(item => item.value === outType);
      return typeItem ? typeItem.label : '';
    },
    
    /** 关闭 */
    handleClose() {
      this.$router.go(-1);
    }
  },
  mounted() {
    // 加载打印设置
    this.loadPrintSettings();
  }
};
</script>

<style scoped>
.print-container {
  padding: 20px;
  background-color: #fff;
  color: #000;
}

.print-header {
  margin-bottom: 20px;
  text-align: center;
}

.print-content {
  max-width: 1000px;
  margin: 0 auto;
  font-family: "SimSun", "宋体", serif;
  color: #000;
}

.out-header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #000;
  padding-bottom: 10px;
}

.out-header .title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 20px 0;
}

.header-info {
  display: flex;
  justify-content: space-between;
}

.header-item {
  display: flex;
  align-items: center;
}

.header-item .label {
  font-weight: bold;
}

.out-info {
  margin-bottom: 30px;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.info-item .label {
  font-weight: bold;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-table {
  margin-bottom: 30px;
}

.detail-table ::v-deep .el-table__header th {
  background-color: #f5f5f5;
  color: #000;
}

.detail-table ::v-deep .el-table__row td {
  color: #000;
}

.out-details h3,
.approval-info h3 {
  font-size: 18px;
  margin: 0 0 15px 0;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.out-footer {
  margin: 30px 0;
}

.footer-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-end;
  height: 30px;
}

.footer-item .label {
  font-weight: bold;
  min-width: 100px;
  flex-shrink: 0;
}

.signature-line {
  display: inline-block;
  width: 80px;
  height: 1px;
  background-color: #000;
  margin-left: 5px;
  position: relative;
  bottom: 3px;
}

.margin-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}
</style>