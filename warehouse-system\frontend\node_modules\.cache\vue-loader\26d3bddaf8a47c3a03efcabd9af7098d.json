{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\stock\\index.vue?vue&type=style&index=0&id=97346088&scoped=true&lang=css", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\stock\\index.vue", "mtime": 1756537609373}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1755901395158}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1755901427908}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1755901408157}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hcHAtY29udGFpbmVyIHsKICBwYWRkaW5nOiAyMHB4Owp9CgovKiDliIbpobXmoLflvI8gKi8KLmVsLXBhZ2luYXRpb24gewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBtYXJnaW4tdG9wOiAyMHB4Owp9CgovKiDlk43lupTlvI/orr7orqEgKi8KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7CiAgLmFwcC1jb250YWluZXIgewogICAgcGFkZGluZzogMTBweDsKICB9Cn0KCi8qIOWKoOi9veeKtuaAgeS8mOWMliAqLwouZWwtbG9hZGluZy1tYXNrIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqdA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inventory/stock", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索表单 -->\n    <SearchForm\n      :query-params=\"queryParams\"\n      :warehouse-options=\"warehouseOptions\"\n      :dict-options=\"dict.type.inventory_status\"\n      :show-search=\"showSearch\"\n      :is-mobile=\"isMobile\"\n      @query=\"handleQuery\"\n      @reset=\"resetQuery\"\n      @update:queryParams=\"queryParams = { ...queryParams, ...$event }\"\n    />\n\n    <!-- 操作按钮栏 -->\n    <ActionBar\n      :single=\"single\"\n      :multiple=\"multiple\"\n      :show-search.sync=\"showSearch\"\n      :is-mobile=\"isMobile\"\n      @add=\"handleAdd\"\n      @update=\"handleUpdate\"\n      @delete=\"handleDelete\"\n      @export=\"handleExport\"\n      @report=\"handleReport\"\n      @alert-report=\"handleAlertReport\"\n      @threshold=\"handleThreshold\"\n      @analysis=\"handleAnalysis\"\n      @product-stock=\"handleProductStock\"\n      @refresh=\"getList\"\n    />\n\n    <!-- 移动端库存列表 -->\n    <MobileStockList\n      v-if=\"isMobile\"\n      :data=\"stockList\"\n      :loading=\"loading\"\n      @view=\"handleView\"\n      @update=\"handleUpdate\"\n      @delete=\"handleDelete\"\n    />\n\n    <!-- 桌面端数据表格 -->\n    <DataTable\n      v-else\n      :data=\"stockList\"\n      :loading=\"loading\"\n      @selection-change=\"handleSelectionChange\"\n      @update=\"handleUpdate\"\n      @delete=\"handleDelete\"\n    />\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 表单对话框 -->\n    <FormDialog\n      :visible.sync=\"open\"\n      :title=\"title\"\n      :form=\"form\"\n      :rules=\"rules\"\n      :product-options=\"productOptions\"\n      :warehouse-options=\"warehouseOptions\"\n      :is-mobile=\"isMobile\"\n      @submit=\"submitForm\"\n      @cancel=\"cancel\"\n      @product-change=\"handleProductChange\"\n      ref=\"formDialog\"\n    />\n  </div>\n</template>\n\n<script>\nimport { listStock, getStock, delStock, addStock, updateStock } from \"@/api/inventory/stock\";\nimport { optionselect } from \"@/api/system/warehouse\";\nimport { listProduct } from \"@/api/product/info\";\nimport { \n  SearchForm, \n  ActionBar, \n  DataTable, \n  MobileStockList, \n  FormDialog \n} from './components';\nimport stockDataProcessingMixin from './mixins/stockDataProcessing.js';\n\nexport default {\n  name: \"Stock\",\n  components: {\n    SearchForm,\n    ActionBar,\n    DataTable,\n    MobileStockList,\n    FormDialog\n  },\n  mixins: [stockDataProcessingMixin],\n  dicts: ['inventory_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 库存表格数据\n      stockList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 仓库选项\n      warehouseOptions: [],\n      // 物品选项\n      productOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        productName: null,\n        productCode: null,\n        warehouseId: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        productId: [\n          { required: true, message: \"物品不能为空\", trigger: \"change\" }\n        ],\n        warehouseId: [\n          { required: true, message: \"仓库不能为空\", trigger: \"change\" }\n        ],\n        quantity: [\n          { required: true, message: \"库存数量不能为空\", trigger: \"blur\" }\n        ]\n      },\n      // 移动端搜索折叠面板\n      mobileSearchVisible: ['search']\n    };\n  },\n  computed: {\n    /** 是否为移动端 */\n    isMobile() {\n      return this.$store.getters.device === 'mobile'\n    }\n  },\n  created() {\n    this.getList();\n    this.getWarehouseOptions();\n    this.getProductOptions();\n    // 自动弹窗详情\n    if (this.$route.query.id) {\n      this.openStockDetailById(this.$route.query.id);\n    }\n  },\n  watch: {\n    '$route.query.id'(newId) {\n      if (newId) {\n        this.openStockDetailById(newId);\n      }\n    }\n  },\n  methods: {\n    /** 查询库存列表 */\n    getList() {\n      this.loading = true;\n      listStock(this.queryParams).then(response => {\n        this.stockList = this.processStockList(response.rows || []);\n        this.total = response.total || 0;\n        this.loading = false;\n      }).catch(error => {\n        this.loading = false;\n        this.handleApiError(error, '查询库存列表失败');\n      });\n    },\n    /** 获取仓库选项 */\n    getWarehouseOptions() {\n      optionselect().then(response => {\n        this.warehouseOptions = this.processWarehouseOptions(response.data || response.rows || []);\n      }).catch(error => {\n        this.handleApiError(error, '获取仓库选项失败');\n      });\n    },\n    /** 获取物品选项 */\n    getProductOptions() {\n      listProduct().then(response => {\n        this.productOptions = this.processProductOptions(response.rows || []);\n      }).catch(error => {\n        this.handleApiError(error, '获取物品选项失败');\n      });\n    },\n    // 物品选择事件\n    handleProductChange(value) {\n      // 可以在这里处理物品选择后的逻辑\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        inventoryId: null,\n        productId: null,\n        warehouseId: null,\n        quantity: 0,\n        minQuantity: 0,\n        maxQuantity: 0,\n        status: \"0\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.inventoryId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加库存信息\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const inventoryId = row.inventoryId || this.ids[0];\n      getStock(inventoryId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改库存信息\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs.formDialog.validate(valid => {\n        if (valid) {\n          // 使用混入中的验证方法\n          const validation = this.validateStockForm(this.form);\n          if (!validation.valid) {\n            this.$message.error(validation.errors[0]);\n            return;\n          }\n\n          if (this.form.inventoryId != null) {\n            updateStock(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            }).catch(error => {\n              this.handleApiError(error, \"修改失败\");\n            });\n          } else {\n            addStock(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            }).catch(error => {\n              this.handleApiError(error, \"新增失败\");\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      // 如果传入了row参数，说明是单行删除；否则是批量删除\n      const inventoryIds = row ? row.inventoryId : this.ids;\n      \n      // 检查是否有选中的数据\n      if (!inventoryIds || (Array.isArray(inventoryIds) && inventoryIds.length === 0)) {\n        this.$modal.msgError(\"请选择要删除的数据\");\n        return;\n      }\n      \n      const confirmText = row \n        ? `是否确认删除库存编号为\"${inventoryIds}\"的数据项？`\n        : `是否确认删除选中的${this.ids.length}条数据？`;\n        \n      this.$modal.confirm(confirmText).then(() => {\n        return delStock(inventoryIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.$modal.confirm('是否确认导出当前筛选条件下的库存数据？').then(() => {\n        const exportData = this.formatExportData(this.stockList);\n        const filename = this.generateExportFileName();\n        \n        this.download('api/v1/inventory/stocks/export', {\n          ...this.queryParams\n        }, filename);\n      }).catch(() => {});\n    },\n    /** 报表按钮操作 */\n    handleReport() {\n      this.$router.push({ path: \"/report/stock/index\" });\n    },\n    /** 预警报表按钮操作 */\n    handleAlertReport() {\n      this.$router.push({ path: \"/report/alert/index\" });\n    },\n    /** 阈值设置按钮操作 */\n    handleThreshold() {\n      this.$router.push({ path: \"/inventory/batch/threshold\" });\n    },\n    /** 高级分析按钮操作 */\n    handleAnalysis() {\n      this.$confirm('请选择分析类型', '高级分析', {\n        confirmButtonText: '周转率分析',\n        cancelButtonText: '价值分析',\n        type: 'info'\n      }).then(() => {\n        // 周转率分析\n        this.$router.push({ path: \"/report/analysis/turnover\" });\n      }).catch(() => {\n        // 价值分析\n        this.$router.push({ path: \"/report/analysis/value\" });\n      });\n    },\n    /** 物品库存按钮操作 */\n    handleProductStock() {\n      this.$router.push({ path: \"/inventory/stock/product\" });\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      this.$router.push({ path: \"/inventory/stock/detail/\" + row.inventoryId });\n    },\n    /** 审核按钮操作 */\n    handleAudit(row) {\n      this.$modal.msgError(\"审核功能待实现\");\n    },\n    /** 打印按钮操作 */\n    handlePrint(row) {\n      // 创建打印窗口\n      const printWindow = window.open('', '_blank', 'width=800,height=600');\n\n      // 生成打印内容\n      const printContent = this.generateStockPrintHTML(row);\n\n      printWindow.document.write(printContent);\n      printWindow.document.close();\n\n      // 等待内容加载完成后打印\n      printWindow.onload = () => {\n        setTimeout(() => {\n          printWindow.print();\n        }, 500);\n      };\n    },\n\n    /** 生成库存打印HTML */\n    generateStockPrintHTML(stockData) {\n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <title>库存信息打印</title>\n          <style>\n            body { font-family: \"Microsoft YaHei\", SimSun, sans-serif; font-size: 12pt; margin: 20px; }\n            .header { text-align: center; margin-bottom: 30px; }\n            .title { font-size: 18pt; font-weight: bold; margin-bottom: 10px; }\n            .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n            .info-table th, .info-table td { border: 1px solid #000; padding: 8px; text-align: left; }\n            .info-table th { background-color: #f5f5f5; font-weight: bold; }\n            .footer { margin-top: 30px; text-align: right; }\n            @media print {\n              body { margin: 0; }\n              .no-print { display: none; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <div class=\"title\">库存信息单</div>\n            <div>打印时间：${new Date().toLocaleString()}</div>\n          </div>\n\n          <table class=\"info-table\">\n            <tr>\n              <th width=\"15%\">物品编码</th>\n              <td width=\"35%\">${stockData.productCode || ''}</td>\n              <th width=\"15%\">物品名称</th>\n              <td width=\"35%\">${stockData.productName || ''}</td>\n            </tr>\n            <tr>\n              <th>仓库名称</th>\n              <td>${stockData.warehouseName || ''}</td>\n              <th>库位</th>\n              <td>${stockData.locationName || ''}</td>\n            </tr>\n            <tr>\n              <th>当前库存</th>\n              <td>${stockData.quantity || 0}</td>\n              <th>单位</th>\n              <td>${stockData.unit || ''}</td>\n            </tr>\n            <tr>\n              <th>单价</th>\n              <td>${stockData.price || 0}</td>\n              <th>总价值</th>\n              <td>${(stockData.quantity * stockData.price || 0).toFixed(2)}</td>\n            </tr>\n            <tr>\n              <th>最低库存</th>\n              <td>${stockData.minQuantity || 0}</td>\n              <th>最高库存</th>\n              <td>${stockData.maxQuantity || 0}</td>\n            </tr>\n            <tr>\n              <th>状态</th>\n              <td colspan=\"3\">${this.getDictLabel(this.dict.type.inventory_status, stockData.status)}</td>\n            </tr>\n          </table>\n\n          <div class=\"footer\">\n            <div>打印人：${this.$store.state.user.name}</div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    /** 自动弹窗详情 */\n    openStockDetailById(id) {\n      if (!id) return;\n      this.loading = true;\n      this.$api.getStock(id).then(res => {\n        this.form = res.data;\n        this.title = '库存详情';\n        this.open = true;\n      }).finally(() => {\n        this.loading = false;\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n\n/* 分页样式 */\n.el-pagination {\n  text-align: center;\n  margin-top: 20px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .app-container {\n    padding: 10px;\n  }\n}\n\n/* 加载状态优化 */\n.el-loading-mask {\n  background-color: rgba(255, 255, 255, 0.8);\n}\n</style>\n"]}]}