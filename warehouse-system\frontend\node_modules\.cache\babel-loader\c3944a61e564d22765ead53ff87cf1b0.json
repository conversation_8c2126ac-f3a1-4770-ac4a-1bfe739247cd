{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\transfer\\print.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\transfer\\print.vue", "mtime": 1756537534758}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\babel.config.js", "mtime": 1747299429443}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_transfer", "require", "_userUtils", "name", "dicts", "data", "loading", "isPrinting", "printSettingsVisible", "transferData", "transferCode", "fromWarehouseName", "toWarehouseName", "transferTime", "status", "createBy", "createByName", "auditBy", "auditByName", "remark", "details", "transferId", "printSettings", "marginTop", "marginRight", "marginBottom", "marginLeft", "fontSize", "orientation", "paperSize", "created", "$route", "params", "id", "query", "console", "log", "$message", "error", "getTransferData", "methods", "_this", "getInventoryTransfer", "then", "response", "userNames", "filter", "length", "getBatchUserRealNames", "nameMap", "catch", "handlePrintSettings", "savePrintSettings", "localStorage", "setItem", "JSON", "stringify", "success", "loadPrintSettings", "savedSettings", "getItem", "parse", "handlePrint", "_this2", "warning", "$nextTick", "printWindow", "window", "open", "printContent", "generatePrintHTML", "document", "write", "close", "onload", "setTimeout", "print", "detailsHTML", "for<PERSON>ach", "item", "index", "concat", "productCode", "productName", "quantity", "pageSizeStyle", "parseTime", "getStatusName", "statusDict", "dict", "type", "inventory_transfer_status", "statusItem", "find", "value", "label", "handleClose", "$router", "go", "mounted"], "sources": ["src/views/inventory/transfer/print.vue"], "sourcesContent": ["<template>\n  <div class=\"print-container\">\n    <!-- 打印预览界面 -->\n    <div class=\"print-header\" v-if=\"!isPrinting\">\n      <el-button type=\"primary\" icon=\"el-icon-printer\" size=\"small\" @click=\"handlePrint\">\n        打印调拨单\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-setting\" size=\"small\" @click=\"handlePrintSettings\">\n        打印设置\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-close\" size=\"small\" @click=\"handleClose\">\n        关闭\n      </el-button>\n    </div>\n    \n    <!-- 打印设置对话框 -->\n    <el-dialog title=\"打印设置\" :visible.sync=\"printSettingsVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"printSettingsForm\" :model=\"printSettings\" label-width=\"100px\">\n        <el-form-item label=\"页面边距\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginTop\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginRight\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginBottom\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginLeft\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n          </el-row>\n          <div class=\"margin-labels\">\n            <span>上</span>\n            <span>右</span>\n            <span>下</span>\n            <span>左</span>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"字体大小\">\n          <el-slider v-model=\"printSettings.fontSize\" :min=\"8\" :max=\"20\" show-input></el-slider>\n        </el-form-item>\n        \n        <el-form-item label=\"页面方向\">\n          <el-radio-group v-model=\"printSettings.orientation\">\n            <el-radio label=\"portrait\">纵向</el-radio>\n            <el-radio label=\"landscape\">横向</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"纸张大小\">\n          <el-select v-model=\"printSettings.paperSize\" placeholder=\"请选择纸张大小\">\n            <el-option label=\"A4\" value=\"A4\"></el-option>\n            <el-option label=\"A5\" value=\"A5\"></el-option>\n            <el-option label=\"B5\" value=\"B5\"></el-option>\n            <el-option label=\"Letter\" value=\"letter\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"printSettingsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"savePrintSettings\">确 定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 打印内容 -->\n    <div class=\"print-content\" id=\"printContent\">\n      <div class=\"transfer-header\">\n        <h2 class=\"title\">库存调拨单</h2>\n        <div class=\"header-info\">\n          <div class=\"header-item\">\n            <span class=\"label\">调拨单号：</span>\n            <span class=\"value\">{{ transferData.transferCode }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">调拨日期：</span>\n            <span class=\"value\">{{ parseTime(transferData.transferTime) }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">状态：</span>\n            <span class=\"value\">\n              <dict-tag :options=\"dict.type.inventory_transfer_status\" :value=\"transferData.status\" />\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"transfer-info\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">调出仓库：</span>\n              <span class=\"value\">{{ transferData.fromWarehouseName }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">调入仓库：</span>\n              <span class=\"value\">{{ transferData.toWarehouseName }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">制单人：</span>\n              <span class=\"value\">{{ transferData.createByName || transferData.createBy }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">审核人：</span>\n              <span class=\"value\">{{ transferData.auditByName || transferData.auditBy || '未审核' }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <div class=\"info-item\">\n              <span class=\"label\">备注：</span>\n              <span class=\"value\">{{ transferData.remark }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <div class=\"transfer-details\">\n        <h3>调拨物品信息</h3>\n        <el-table :data=\"transferData.details\" class=\"detail-table\" show-summary>\n          <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n          <el-table-column label=\"物品编码\" prop=\"productCode\" />\n          <el-table-column label=\"物品名称\" prop=\"productName\" />\n          <el-table-column label=\"调拨数量\" prop=\"quantity\" />\n          <el-table-column label=\"备注\" prop=\"remark\" />\n        </el-table>\n      </div>\n      \n      <div class=\"transfer-footer\">\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 120px;\">调出仓库负责人：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 120px;\">调入仓库负责人：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getInventoryTransfer } from \"@/api/inventory/transfer\";\nimport { getBatchUserRealNames } from \"@/utils/userUtils\";\n\nexport default {\n  name: \"TransferPrint\",\n  dicts: ['inventory_transfer_status'],\n  data() {\n    return {\n      loading: false,\n      isPrinting: false,\n      printSettingsVisible: false,\n      transferData: {\n        transferCode: \"\",\n        fromWarehouseName: \"\",\n        toWarehouseName: \"\",\n        transferTime: null,\n        status: \"\",\n        createBy: \"\",\n        createByName: \"\",\n        auditBy: \"\",\n        auditByName: \"\",\n        remark: \"\",\n        details: []\n      },\n      transferId: null,\n      printSettings: {\n        marginTop: 15,\n        marginRight: 15,\n        marginBottom: 15,\n        marginLeft: 15,\n        fontSize: 12,\n        orientation: 'portrait',\n        paperSize: 'A4'\n      }\n    };\n  },\n  created() {\n    this.transferId = this.$route.params.transferId || this.$route.params.id || this.$route.query.id;\n    console.log('调拨单打印页面初始化，调拨单ID:', this.transferId);\n    if (!this.transferId) {\n      this.$message.error('缺少调拨单ID参数');\n      return;\n    }\n    this.getTransferData();\n  },\n  methods: {\n    /** 获取调拨单信息 */\n    getTransferData() {\n      this.loading = true;\n      getInventoryTransfer(this.transferId).then(response => {\n        this.transferData = response.data;\n        \n        // 获取用户真实姓名\n        const userNames = [\n          this.transferData.createBy,\n          this.transferData.auditBy\n        ].filter(name => name); // 过滤空值\n\n        if (userNames.length > 0) {\n          getBatchUserRealNames(userNames).then(nameMap => {\n            // 更新用户真实姓名\n            if (this.transferData.createBy && nameMap[this.transferData.createBy]) {\n              this.transferData.createByName = nameMap[this.transferData.createBy];\n            }\n            if (this.transferData.auditBy && nameMap[this.transferData.auditBy]) {\n              this.transferData.auditByName = nameMap[this.transferData.auditBy];\n            }\n          });\n        }\n        \n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n        this.$message.error(\"获取调拨单信息失败\");\n      });\n    },\n    \n    /** 处理打印设置 */\n    handlePrintSettings() {\n      this.printSettingsVisible = true;\n    },\n    \n    /** 保存打印设置 */\n    savePrintSettings() {\n      // 保存到本地存储\n      localStorage.setItem('transferPrintSettings', JSON.stringify(this.printSettings));\n      this.printSettingsVisible = false;\n      this.$message.success('打印设置已保存');\n    },\n    \n    /** 加载打印设置 */\n    loadPrintSettings() {\n      const savedSettings = localStorage.getItem('transferPrintSettings');\n      if (savedSettings) {\n        this.printSettings = JSON.parse(savedSettings);\n      }\n    },\n    \n    /** 打印 */\n    handlePrint() {\n      this.isPrinting = true;\n      \n      // 确保数据已加载\n      if (!this.transferData.transferCode) {\n        this.$message.warning('数据还在加载中，请稍后再试');\n        this.isPrinting = false;\n        return;\n      }\n      \n      this.$nextTick(() => {\n        // 创建新窗口进行打印\n        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');\n        \n        // 生成打印内容\n        const printContent = this.generatePrintHTML();\n        \n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        \n        // 等待内容加载完成后打印\n        printWindow.onload = () => {\n          setTimeout(() => {\n            printWindow.print();\n            // 打印完成后不自动关闭窗口，让用户手动关闭\n            this.isPrinting = false;\n          }, 500);\n        };\n      });\n    },\n    \n    /** 生成打印页面HTML */\n    generatePrintHTML() {\n      const details = this.transferData.details || [];\n      let detailsHTML = '';\n      \n      details.forEach((item, index) => {\n        detailsHTML += `\n          <tr>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${index + 1}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productCode || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productName || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.quantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.remark || ''}</td>\n          </tr>\n        `;\n      });\n      \n      // 根据设置确定页面方向和纸张大小\n      let pageSizeStyle = '';\n      if (this.printSettings.orientation === 'landscape') {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;\n      } else {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;\n      }\n      \n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>库存调拨单打印</title>\n          <style>\n            @page { \n              ${pageSizeStyle}\n              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; \n            }\n            \n            * {\n              box-sizing: border-box;\n            }\n            \n            html, body { \n              font-family: \"Microsoft YaHei\", SimSun, sans-serif; \n              font-size: ${this.printSettings.fontSize}pt; \n              color: #000; \n              background: #fff; \n              margin: 0; \n              padding: ${this.printSettings.marginTop/3}mm;\n              width: 100%;\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n            \n            .container {\n              width: 100%;\n              max-width: 1000px;\n              margin: 0 auto;\n            }\n            \n            .print-container {\n              padding: 20px;\n              background-color: #fff;\n              color: #000;\n            }\n            \n            .print-content {\n              max-width: 1000px;\n              margin: 0 auto;\n              font-family: \"SimSun\", \"宋体\", serif;\n              color: #000;\n            }\n            \n            .transfer-header {\n              text-align: center;\n              margin-bottom: 30px;\n              border-bottom: 2px solid #000;\n              padding-bottom: 10px;\n            }\n            \n            .transfer-header .title {\n              font-size: ${this.printSettings.fontSize + 8}pt;\n              font-weight: bold;\n              margin: 0 0 20px 0;\n            }\n            \n            .header-info {\n              display: flex;\n              justify-content: space-between;\n            }\n            \n            .header-item {\n              display: flex;\n              align-items: center;\n            }\n            \n            .header-item .label {\n              font-weight: bold;\n              margin-right: 5px;\n            }\n            \n            .transfer-info {\n              margin-bottom: 30px;\n            }\n            \n            .info-item {\n              margin-bottom: 15px;\n              display: flex;\n              align-items: flex-start;\n            }\n            \n            .info-item .label {\n              font-weight: bold;\n              min-width: 100px;\n              flex-shrink: 0;\n            }\n            \n            .transfer-details h3,\n            .approval-info h3 {\n              font-size: ${this.printSettings.fontSize + 4}pt;\n              margin: 0 0 15px 0;\n              border-left: 4px solid #409EFF;\n              padding-left: 10px;\n            }\n            \n            table {\n              width: 100%;\n              border-collapse: collapse;\n              margin-bottom: 30px;\n              table-layout: fixed;\n            }\n            \n            th {\n              background-color: #f5f5f5;\n              color: #000;\n              font-weight: bold;\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n            }\n            \n            td {\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n            \n            .transfer-footer {\n              margin: 30px 0;\n            }\n            \n            .footer-item {\n              margin-bottom: 15px;\n            }\n            \n            .signature-line {\n              display: inline-block;\n              width: 80px;\n              height: 1px;\n              background-color: #000;\n              margin-left: 10px;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"print-container\">\n            <div class=\"print-content\">\n              <div class=\"transfer-header\">\n                <h2 class=\"title\">库存调拨单</h2>\n                <div class=\"header-info\">\n                  <div class=\"header-item\">\n                    <span class=\"label\">调拨单号：</span>\n                    <span class=\"value\">${this.transferData.transferCode || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">调拨日期：</span>\n                    <span class=\"value\">${this.parseTime(this.transferData.transferTime) || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">状态：</span>\n                    <span class=\"value\">${this.getStatusName(this.transferData.status) || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"transfer-info\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">调出仓库：</span>\n                    <span>${this.transferData.fromWarehouseName || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">调入仓库：</span>\n                    <span>${this.transferData.toWarehouseName || ''}</span>\n                  </div>\n                </div>\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">制单人：</span>\n                    <span>${this.transferData.createByName || this.transferData.createBy || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人：</span>\n                    <span>${this.transferData.auditByName || this.transferData.auditBy || '未审核'}</span>\n                  </div>\n                </div>\n                \n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 100%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">备注：</span>\n                    <span>${this.transferData.remark || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"transfer-details\">\n                <h3>调拨物品信息</h3>\n                <table>\n                  <thead>\n                    <tr>\n                      <th>序号</th>\n                      <th>物品编码</th>\n                      <th>物品名称</th>\n                      <th>调拨数量</th>\n                      <th>备注</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    ${detailsHTML}\n                  </tbody>\n                </table>\n              </div>\n              \n              <div class=\"transfer-footer\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 120px;\">调出仓库负责人：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 120px;\">调入仓库负责人：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    \n    /** 获取状态名称 */\n    getStatusName(status) {\n      // 使用与屏幕预览相同的字典映射方式\n      const statusDict = this.dict.type.inventory_transfer_status || [];\n      const statusItem = statusDict.find(item => item.value === status);\n      return statusItem ? statusItem.label : '';\n    },\n    \n    /** 关闭 */\n    handleClose() {\n      this.$router.go(-1);\n    }\n  },\n  mounted() {\n    // 加载打印设置\n    this.loadPrintSettings();\n  }\n};\n</script>\n\n<style scoped>\n.print-container {\n  padding: 20px;\n  background-color: #fff;\n  color: #000;\n}\n\n.print-header {\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.print-content {\n  max-width: 1000px;\n  margin: 0 auto;\n  font-family: \"SimSun\", \"宋体\", serif;\n  color: #000;\n}\n\n.transfer-header {\n  text-align: center;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #000;\n  padding-bottom: 10px;\n}\n\n.transfer-header .title {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0 0 20px 0;\n}\n\n.header-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.header-item {\n  display: flex;\n  align-items: center;\n}\n\n.header-item .label {\n  font-weight: bold;\n}\n\n.transfer-info {\n  margin-bottom: 30px;\n}\n\n.info-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.info-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.detail-table {\n  margin-bottom: 30px;\n}\n\n.detail-table ::v-deep .el-table__header th {\n  background-color: #f5f5f5;\n  color: #000;\n}\n\n.detail-table ::v-deep .el-table__row td {\n  color: #000;\n}\n\n.transfer-details h3,\n.approval-info h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n  border-left: 4px solid #409EFF;\n  padding-left: 10px;\n}\n\n.transfer-footer {\n  margin: 30px 0;\n}\n\n.footer-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-end;\n  height: 30px;\n}\n\n.footer-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.signature-line {\n  display: inline-block;\n  width: 80px;\n  height: 1px;\n  background-color: #000;\n  margin-left: 5px;\n  position: relative;\n  bottom: 3px;\n}\n\n.margin-labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;AA0KA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,oBAAA;MACAC,YAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,YAAA;QACAC,MAAA;QACAC,QAAA;QACAC,YAAA;QACAC,OAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACAC,UAAA;MACAC,aAAA;QACAC,SAAA;QACAC,WAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAT,UAAA,QAAAU,MAAA,CAAAC,MAAA,CAAAX,UAAA,SAAAU,MAAA,CAAAC,MAAA,CAAAC,EAAA,SAAAF,MAAA,CAAAG,KAAA,CAAAD,EAAA;IACAE,OAAA,CAAAC,GAAA,2BAAAf,UAAA;IACA,UAAAA,UAAA;MACA,KAAAgB,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACA,cACAD,eAAA,WAAAA,gBAAA;MAAA,IAAAE,KAAA;MACA,KAAAnC,OAAA;MACA,IAAAoC,8BAAA,OAAArB,UAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAhC,YAAA,GAAAmC,QAAA,CAAAvC,IAAA;;QAEA;QACA,IAAAwC,SAAA,IACAJ,KAAA,CAAAhC,YAAA,CAAAM,QAAA,EACA0B,KAAA,CAAAhC,YAAA,CAAAQ,OAAA,CACA,CAAA6B,MAAA,WAAA3C,IAAA;UAAA,OAAAA,IAAA;QAAA;;QAEA,IAAA0C,SAAA,CAAAE,MAAA;UACA,IAAAC,gCAAA,EAAAH,SAAA,EAAAF,IAAA,WAAAM,OAAA;YACA;YACA,IAAAR,KAAA,CAAAhC,YAAA,CAAAM,QAAA,IAAAkC,OAAA,CAAAR,KAAA,CAAAhC,YAAA,CAAAM,QAAA;cACA0B,KAAA,CAAAhC,YAAA,CAAAO,YAAA,GAAAiC,OAAA,CAAAR,KAAA,CAAAhC,YAAA,CAAAM,QAAA;YACA;YACA,IAAA0B,KAAA,CAAAhC,YAAA,CAAAQ,OAAA,IAAAgC,OAAA,CAAAR,KAAA,CAAAhC,YAAA,CAAAQ,OAAA;cACAwB,KAAA,CAAAhC,YAAA,CAAAS,WAAA,GAAA+B,OAAA,CAAAR,KAAA,CAAAhC,YAAA,CAAAQ,OAAA;YACA;UACA;QACA;QAEAwB,KAAA,CAAAnC,OAAA;MACA,GAAA4C,KAAA;QACAT,KAAA,CAAAnC,OAAA;QACAmC,KAAA,CAAAJ,QAAA,CAAAC,KAAA;MACA;IACA;IAEA,aACAa,mBAAA,WAAAA,oBAAA;MACA,KAAA3C,oBAAA;IACA;IAEA,aACA4C,iBAAA,WAAAA,kBAAA;MACA;MACAC,YAAA,CAAAC,OAAA,0BAAAC,IAAA,CAAAC,SAAA,MAAAlC,aAAA;MACA,KAAAd,oBAAA;MACA,KAAA6B,QAAA,CAAAoB,OAAA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAA;MACA,IAAAC,aAAA,GAAAN,YAAA,CAAAO,OAAA;MACA,IAAAD,aAAA;QACA,KAAArC,aAAA,GAAAiC,IAAA,CAAAM,KAAA,CAAAF,aAAA;MACA;IACA;IAEA,SACAG,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAxD,UAAA;;MAEA;MACA,UAAAE,YAAA,CAAAC,YAAA;QACA,KAAA2B,QAAA,CAAA2B,OAAA;QACA,KAAAzD,UAAA;QACA;MACA;MAEA,KAAA0D,SAAA;QACA;QACA,IAAAC,WAAA,GAAAC,MAAA,CAAAC,IAAA;;QAEA;QACA,IAAAC,YAAA,GAAAN,MAAA,CAAAO,iBAAA;QAEAJ,WAAA,CAAAK,QAAA,CAAAC,KAAA,CAAAH,YAAA;QACAH,WAAA,CAAAK,QAAA,CAAAE,KAAA;;QAEA;QACAP,WAAA,CAAAQ,MAAA;UACAC,UAAA;YACAT,WAAA,CAAAU,KAAA;YACA;YACAb,MAAA,CAAAxD,UAAA;UACA;QACA;MACA;IACA;IAEA,iBACA+D,iBAAA,WAAAA,kBAAA;MACA,IAAAlD,OAAA,QAAAX,YAAA,CAAAW,OAAA;MACA,IAAAyD,WAAA;MAEAzD,OAAA,CAAA0D,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACAH,WAAA,6GAAAI,MAAA,CAEAD,KAAA,qGAAAC,MAAA,CACAF,IAAA,CAAAG,WAAA,uGAAAD,MAAA,CACAF,IAAA,CAAAI,WAAA,uGAAAF,MAAA,CACAF,IAAA,CAAAK,QAAA,uGAAAH,MAAA,CACAF,IAAA,CAAA5D,MAAA,2CAEA;MACA;;MAEA;MACA,IAAAkE,aAAA;MACA,SAAA/D,aAAA,CAAAM,WAAA;QACAyD,aAAA,YAAAJ,MAAA,MAAA3D,aAAA,CAAAO,SAAA;MACA;QACAwD,aAAA,YAAAJ,MAAA,MAAA3D,aAAA,CAAAO,SAAA;MACA;MAEA,2TAAAoD,MAAA,CASAI,aAAA,8BAAAJ,MAAA,CACA,KAAA3D,aAAA,CAAAC,SAAA,SAAA0D,MAAA,MAAA3D,aAAA,CAAAE,WAAA,SAAAyD,MAAA,MAAA3D,aAAA,CAAAG,YAAA,SAAAwD,MAAA,MAAA3D,aAAA,CAAAI,UAAA,wPAAAuD,MAAA,CASA,KAAA3D,aAAA,CAAAK,QAAA,6HAAAsD,MAAA,CAIA,KAAA3D,aAAA,CAAAC,SAAA,s8BAAA0D,MAAA,CAiCA,KAAA3D,aAAA,CAAAK,QAAA,m/BAAAsD,MAAA,CAsCA,KAAA3D,aAAA,CAAAK,QAAA,yqDAAAsD,MAAA,CAuDA,KAAAxE,YAAA,CAAAC,YAAA,yNAAAuE,MAAA,CAIA,KAAAK,SAAA,MAAA7E,YAAA,CAAAI,YAAA,8MAAAoE,MAAA,CAIA,KAAAM,aAAA,MAAA9E,YAAA,CAAAK,MAAA,waAAAmE,MAAA,CASA,KAAAxE,YAAA,CAAAE,iBAAA,uPAAAsE,MAAA,CAIA,KAAAxE,YAAA,CAAAG,eAAA,8UAAAqE,MAAA,CAMA,KAAAxE,YAAA,CAAAO,YAAA,SAAAP,YAAA,CAAAM,QAAA,iPAAAkE,MAAA,CAIA,KAAAxE,YAAA,CAAAS,WAAA,SAAAT,YAAA,CAAAQ,OAAA,8VAAAgE,MAAA,CAOA,KAAAxE,YAAA,CAAAU,MAAA,qpBAAA8D,MAAA,CAkBAJ,WAAA;IA0BA;IAEA,aACAU,aAAA,WAAAA,cAAAzE,MAAA;MACA;MACA,IAAA0E,UAAA,QAAAC,IAAA,CAAAC,IAAA,CAAAC,yBAAA;MACA,IAAAC,UAAA,GAAAJ,UAAA,CAAAK,IAAA,WAAAd,IAAA;QAAA,OAAAA,IAAA,CAAAe,KAAA,KAAAhF,MAAA;MAAA;MACA,OAAA8E,UAAA,GAAAA,UAAA,CAAAG,KAAA;IACA;IAEA,SACAC,WAAA,WAAAA,YAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAzC,iBAAA;EACA;AACA", "ignoreList": []}]}