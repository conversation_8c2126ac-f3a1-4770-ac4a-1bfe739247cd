@echo off
chcp 65001 >nul
echo ====================================
echo    ��ԣ�ֿ����ϵͳ - ˫ģʽ����
echo ====================================
echo.

:menu
echo ��ѡ������ģʽ��
echo 1. HTTPģʽ (�˿�: ǰ��8081, ���8080)
echo 2. HTTPSģʽ (�˿�: ǰ��8081, ���8443)
echo 3. ˫�˿�ģʽ (ͬʱ֧��HTTP 8080��HTTPS 8443)
echo 4. �˳�
echo.
set /p choice=������ѡ�� (1-4): 

if "%choice%"=="1" goto http_mode
if "%choice%"=="2" goto https_mode
if "%choice%"=="3" goto dual_mode
if "%choice%"=="4" goto exit
echo ��Чѡ������������
goto menu

:http_mode
echo.
echo 启动HTTP模式...
echo 前端: http://localhost:8082
echo 后端: http://localhost:8080
echo.

cd /d "C:\CKGLXT\warehouse-system\backend\wanyu-admin"
start "后端HTTP服务" cmd /k "java -jar target\wanyu-admin.jar --spring.profiles.active=dev,http"

cd /d "C:\CKGLXT\warehouse-system\frontend"
echo VUE_APP_HTTPS_ENABLED=false > .env.local
start "前端开发服务" cmd /k "npm run dev"

echo HTTP模式启动完成！
goto end

:https_mode
echo.
echo 启动HTTPS模式...
echo 前端: https://localhost:8081
echo 后端: https://localhost:8443
echo.
echo 检查SSL证书...
cd /d "C:\CKGLXT\warehouse-system\frontend"
if not exist "certs\localhost-key.pem" (
    echo 正在生成SSL证书...
    node generate-ssl-cert.js
    if errorlevel 1 (
        echo SSL证书生成失败，请手动生成或使用HTTP模式
        pause
        goto menu
    )
)

cd /d "C:\CKGLXT\warehouse-system\backend\wanyu-admin"
start "后端HTTPS服务" cmd /k "java -jar target\wanyu-admin.jar --spring.profiles.active=dev,https"

cd /d "C:\CKGLXT\warehouse-system\frontend"
echo VUE_APP_HTTPS_ENABLED=true > .env.local
start "前端开发服务" cmd /k "npm run dev"

echo HTTPS模式启动完成！
goto end

:dual_mode
echo.
echo ?? ����˫�˿�ģʽ...
echo ǰ��: https://localhost:8081
echo ���HTTP: http://localhost:8080
echo ���HTTPS: https://localhost:8443
echo.

cd /d "C:\CKGLXT\warehouse-system\backend\wanyu-admin"
start "���˫�˿ڷ���" cmd /k "java -jar target\wanyu-admin.jar --spring.profiles.active=dev,https"

cd /d "C:\CKGLXT\warehouse-system\frontend"
echo VUE_APP_HTTPS_ENABLED = true > .env.local
start "ǰ�˿�������" cmd /k "npm run dev"

echo ? ˫�˿�ģʽ������ɣ�
goto end

:end
echo.
echo ?? ���ʵ�ַ��
if "%choice%"=="1" (
    echo   ǰ��: http://localhost:8081
    echo   ���: http://localhost:8080
) else if "%choice%"=="2" (
    echo   ǰ��: https://localhost:8081
    echo   ���: https://localhost:8443
) else if "%choice%"=="3" (
    echo   ǰ��: https://localhost:8081
    echo   ���HTTP: http://localhost:8080
    echo   ���HTTPS: https://localhost:8443
)
echo.
echo ?? ��ʾ��
echo   - �����������뽫localhost�滻Ϊ����IP��ַ
echo   - ��Ctrl+C��ֹͣ��Ӧ����
echo   - �����޸Ķ˿ڣ���༭�����ļ�
echo.
pause

:exit
echo �˳������ű�
exit /b 0
