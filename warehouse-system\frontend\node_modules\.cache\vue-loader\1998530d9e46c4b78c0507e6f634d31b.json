{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\stock\\index.vue?vue&type=template&id=97346088&scoped=true", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\stock\\index.vue", "mtime": 1756537609373}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1755901428442}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}