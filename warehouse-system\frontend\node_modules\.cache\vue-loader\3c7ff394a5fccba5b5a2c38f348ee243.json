{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\log\\system\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\log\\system\\index.vue", "mtime": 1756537793460}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgTG9nTWFuYWdlbWVudCBmcm9tICJAL2NvbXBvbmVudHMvTG9nTWFuYWdlbWVudCI7DQppbXBvcnQgeyBsaXN0T3BlckxvZywgZGVsT3BlckxvZywgY2xlYW5PcGVyTG9nLCBleHBvcnRPcGVyTG9nIH0gZnJvbSAiQC9hcGkvbW9uaXRvci9vcGVybG9nIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiU3lzdGVtTG9nIiwNCiAgY29tcG9uZW50czogew0KICAgIExvZ01hbmFnZW1lbnQNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOihqOagvOaVsOaNrg0KICAgICAgc3lzdGVtTG9nTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDml6XmnJ/ojIPlm7QNCiAgICAgIGRhdGVSYW5nZTogW10sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGxvZ1R5cGU6ICJTWVNURU0iLCAgLy8g6buY6K6k5p+l6K+i57O757uf5pel5b+XDQogICAgICAgIG1vZHVsZU5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgY3JlYXRlQnk6IHVuZGVmaW5lZCwNCiAgICAgICAgb3BlcmF0aW9uVHlwZTogdW5kZWZpbmVkLA0KICAgICAgICBvcGVyYXRpb25TdGF0dXM6IHVuZGVmaW5lZA0KICAgICAgfSwNCiAgICAgIC8vIOe7n+iuoeaVsOaNrg0KICAgICAgc3RhdGlzdGljc0RhdGE6IFtdLA0KICAgICAgLy8g6LaL5Yq/5pWw5o2uDQogICAgICB0cmVuZERhdGE6IFtdLA0KICAgICAgdHJlbmRQZXJpb2Q6ICc3ZCcsDQogICAgICAvLyDlm77ooajphY3nva4NCiAgICAgIGNoYXJ0Q29uZmlnOiB7DQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycNCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgZGF0YTogWyfns7vnu5/mk43kvZwnLCAn5Lia5Yqh5pON5L2cJywgJ+aVsOaNruWPmOabtCddDQogICAgICAgIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbXQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScNCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXNOYW1lOiAn5pON5L2c5pWw6YePJywNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+ezu+e7n+aTjeS9nCcsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhS2V5OiAnc3lzdGVtTG9ncycsDQogICAgICAgICAgICBzdGFjazogJ+aAu+mHjycsDQogICAgICAgICAgICBhcmVhU3R5bGU6IHsNCiAgICAgICAgICAgICAgc3RhcnRDb2xvcjogJyM4MEZGQTUnLA0KICAgICAgICAgICAgICBlbmRDb2xvcjogJyMwMDhGNTInDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc21vb3RoOiB0cnVlDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5Lia5Yqh5pON5L2cJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIGRhdGFLZXk6ICdidXNpbmVzc0xvZ3MnLA0KICAgICAgICAgICAgc3RhY2s6ICfmgLvph48nLA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICAgIHN0YXJ0Q29sb3I6ICcjRkZBMEEwJywNCiAgICAgICAgICAgICAgZW5kQ29sb3I6ICcjOEYwMDAwJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+aVsOaNruWPmOabtCcsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhS2V5OiAnZGF0YUNoYW5nZUxvZ3MnLA0KICAgICAgICAgICAgc3RhY2s6ICfmgLvph48nLA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICAgIHN0YXJ0Q29sb3I6ICcjQTBBMEZGJywNCiAgICAgICAgICAgICAgZW5kQ29sb3I6ICcjMDAwMDhGJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIC8vIOW/q+mAn+etm+mAiQ0KICAgICAgcXVpY2tGaWx0ZXJzOiBbDQogICAgICAgIHsga2V5OiAndG9kYXknLCBsYWJlbDogJ+S7iuaXpScsIGljb246ICdlbC1pY29uLWRhdGUnIH0sDQogICAgICAgIHsga2V5OiAnd2VlaycsIGxhYmVsOiAn5pys5ZGoJywgaWNvbjogJ2VsLWljb24tZGF0ZScgfSwNCiAgICAgICAgeyBrZXk6ICdtb250aCcsIGxhYmVsOiAn5pys5pyIJywgaWNvbjogJ2VsLWljb24tZGF0ZScgfSwNCiAgICAgICAgeyBrZXk6ICdzeXN0ZW0nLCBsYWJlbDogJ+ezu+e7n+aTjeS9nCcsIGljb246ICdlbC1pY29uLXNldHRpbmcnIH0sDQogICAgICAgIHsga2V5OiAnYnVzaW5lc3MnLCBsYWJlbDogJ+S4muWKoeaTjeS9nCcsIGljb246ICdlbC1pY29uLXMtb3JkZXInIH0sDQogICAgICAgIHsga2V5OiAnZGF0YScsIGxhYmVsOiAn5pWw5o2u5pON5L2cJywgaWNvbjogJ2VsLWljb24tcy1kYXRhJyB9DQogICAgICBdLA0KICAgICAgLy8g5Li76KaB5pON5L2cDQogICAgICBtYWluQWN0aW9uczogWw0KICAgICAgICB7DQogICAgICAgICAga2V5OiAnZXhwb3J0JywNCiAgICAgICAgICBsYWJlbDogJ+WvvOWHukV4Y2VsJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycsDQogICAgICAgICAgaWNvbjogJ2VsLWljb24tZG93bmxvYWQnLA0KICAgICAgICAgIHBlcm1pc3Npb246ICdsb2c6b3BlcmF0aW9uOmV4cG9ydCcNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIC8vIOaJuemHj+aTjeS9nA0KICAgICAgYmF0Y2hBY3Rpb25zOiBbDQogICAgICAgIHsNCiAgICAgICAgICBrZXk6ICdiYXRjaERlbGV0ZScsDQogICAgICAgICAgbGFiZWw6ICfmibnph4/liKDpmaQnLA0KICAgICAgICAgIGljb246ICdlbC1pY29uLWRlbGV0ZScsDQogICAgICAgICAgcGVybWlzc2lvbjogJ2xvZzpvcGVyYXRpb246cmVtb3ZlJw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgLy8g6aKd5aSW5pON5L2cDQogICAgICBleHRyYUFjdGlvbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIGtleTogJ2NsZWFuJywNCiAgICAgICAgICBsYWJlbDogJ+a4heepuuaXpeW/lycsDQogICAgICAgICAgdHlwZTogJ2RhbmdlcicsDQogICAgICAgICAgaWNvbjogJ2VsLWljb24tZGVsZXRlJywNCiAgICAgICAgICBwZXJtaXNzaW9uOiAnbG9nOm9wZXJhdGlvbjpyZW1vdmUnDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICAvLyDml6XmnJ/pgInmi6nlmajphY3nva4NCiAgICAgIHBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgc2hvcnRjdXRzOiBbew0KICAgICAgICAgIHRleHQ6ICfmnIDov5HkuIDlkagnLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogNyk7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSwgew0KICAgICAgICAgIHRleHQ6ICfmnIDov5HkuIDkuKrmnIgnLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogMzApOw0KICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKTsNCiAgICAgICAgICB9DQogICAgICAgIH0sIHsNCiAgICAgICAgICB0ZXh0OiAn5pyA6L+R5LiJ5Liq5pyIJywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDkwKTsNCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSk7DQogICAgICAgICAgfQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5pbml0RGF0YSgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOWIneWni+WMluaVsOaNriAqLw0KICAgIGFzeW5jIGluaXREYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoWw0KICAgICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpLA0KICAgICAgICAgIHRoaXMuZ2V0VHJlbmREYXRhKCkNCiAgICAgICAgXSk7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yid5aeL5YyW5pWw5o2u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOafpeivouezu+e7n+aXpeW/lyAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgY29uc3QgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2Uoey4uLnRoaXMucXVlcnlQYXJhbXN9LCB0aGlzLmRhdGVSYW5nZSk7DQogICAgICBjb25zb2xlLmxvZygn5a6e6ZmF6K+35rGC5Y+C5pWwOicsIHBhcmFtcyk7IC8vIOaJk+WNsOafpeivouWPguaVsA0KDQogICAgICAvLyDkvb/nlKjmk43kvZzml6Xlv5dBUEnojrflj5bnnJ/lrp7mlbDmja4NCiAgICAgIGxpc3RPcGVyTG9nKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCdBUEnlk43lupTmlbDmja46JywgcmVzcG9uc2UpOyAvLyDmiZPljbDlrozmlbTlk43lupQNCiAgICAgICAgLy8g5pig5bCE5a2X5q615ZCN56ewDQogICAgICAgIGNvbnN0IG1hcHBlZERhdGEgPSAocmVzcG9uc2Uucm93cyB8fCBbXSkubWFwKGl0ZW0gPT4gKHsNCiAgICAgICAgICBsb2dJZDogaXRlbS5vcGVySWQsDQogICAgICAgICAgbG9nVHlwZTogaXRlbS5idXNpbmVzc1R5cGUgPT09IDAgPyAnU1lTVEVNJyA6ICdCVVNJTkVTUycsDQogICAgICAgICAgbW9kdWxlTmFtZTogaXRlbS50aXRsZSwNCiAgICAgICAgICBvcGVyYXRpb25UeXBlOiB0aGlzLm1hcE9wZXJhdGlvblR5cGUoaXRlbS5idXNpbmVzc1R5cGUpLA0KICAgICAgICAgIG9wZXJhdGlvbk5hbWU6IGl0ZW0ubWV0aG9kLA0KICAgICAgICAgIG9wZXJhdG9yOiBpdGVtLm9wZXJOYW1lLA0KICAgICAgICAgIG9wZXJhdGlvblN0YXR1czogaXRlbS5zdGF0dXMsDQogICAgICAgICAgY29zdFRpbWU6IGl0ZW0uY29zdFRpbWUsDQogICAgICAgICAgb3BlcmF0aW9uVGltZTogaXRlbS5vcGVyVGltZSwNCiAgICAgICAgICBvcGVyYXRvcklwOiBpdGVtLm9wZXJJcCwNCiAgICAgICAgICBvcGVyYXRvckxvY2F0aW9uOiBpdGVtLm9wZXJMb2NhdGlvbiwNCiAgICAgICAgICByZXF1ZXN0TWV0aG9kOiBpdGVtLnJlcXVlc3RNZXRob2QsDQogICAgICAgICAgcmVxdWVzdFVybDogaXRlbS5vcGVyVXJsLA0KICAgICAgICAgIHJlcXVlc3RQYXJhbTogaXRlbS5vcGVyUGFyYW0sDQogICAgICAgICAganNvblJlc3VsdDogaXRlbS5qc29uUmVzdWx0LA0KICAgICAgICAgIGVycm9yTXNnOiBpdGVtLmVycm9yTXNnDQogICAgICAgIH0pKTsNCg0KICAgICAgICB0aGlzLnN5c3RlbUxvZ0xpc3QgPSBtYXBwZWREYXRhOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwgfHwgMDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSeivt+axguWksei0pTonLCBlcnJvcik7IC8vIOaJk+WNsOmUmeivr+ivpuaDhQ0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5p+l6K+i57O757uf5pel5b+X5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOaYoOWwhOaTjeS9nOexu+WeiyAqLw0KICAgIG1hcE9wZXJhdGlvblR5cGUoYnVzaW5lc3NUeXBlKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAwOiAnT1RIRVInLA0KICAgICAgICAxOiAnSU5TRVJUJywNCiAgICAgICAgMjogJ1VQREFURScsDQogICAgICAgIDM6ICdERUxFVEUnLA0KICAgICAgICA0OiAnR1JBTlQnLA0KICAgICAgICA1OiAnRVhQT1JUJywNCiAgICAgICAgNjogJ0lNUE9SVCcsDQogICAgICAgIDc6ICdGT1JDRScsDQogICAgICAgIDg6ICdHRU5DT0RFJywNCiAgICAgICAgOTogJ0NMRUFOJw0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW2J1c2luZXNzVHlwZV0gfHwgJ09USEVSJzsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlue7n+iuoeaVsOaNriAqLw0KICAgIGdldFN0YXRpc3RpY3MoKSB7DQogICAgICAvLyDkvb/nlKjmqKHmi5/mlbDmja7vvIzlm6DkuLrmk43kvZzml6Xlv5dBUEnmsqHmnInnu5/orqHmjqXlj6MNCiAgICAgIHRoaXMuc3RhdGlzdGljc0RhdGEgPSBbDQogICAgICAgIHsgdGl0bGU6ICfmgLvml6Xlv5fmlbAnLCB2YWx1ZTogdGhpcy5zeXN0ZW1Mb2dMaXN0Lmxlbmd0aCwgaWNvbjogJ2VsLWljb24tcy1kYXRhJywgY29sb3I6ICcjNDA5RUZGJyB9LA0KICAgICAgICB7IHRpdGxlOiAn5LuK5pel5pel5b+XJywgdmFsdWU6IDAsIGljb246ICdlbC1pY29uLWRhdGUnLCBjb2xvcjogJyM2N0MyM0EnIH0sDQogICAgICAgIHsgdGl0bGU6ICfns7vnu5/mk43kvZwnLCB2YWx1ZTogMCwgaWNvbjogJ2VsLWljb24tc2V0dGluZycsIGNvbG9yOiAnI0U2QTIzQycgfSwNCiAgICAgICAgeyB0aXRsZTogJ+S4muWKoeaTjeS9nCcsIHZhbHVlOiAwLCBpY29uOiAnZWwtaWNvbi1zLW9yZGVyJywgY29sb3I6ICcjRjU2QzZDJyB9DQogICAgICBdOw0KICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W6LaL5Yq/5pWw5o2uICovDQogICAgZ2V0VHJlbmREYXRhKCkgew0KICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2u77yM5Zug5Li65pON5L2c5pel5b+XQVBJ5rKh5pyJ6LaL5Yq/5o6l5Y+jDQogICAgICBjb25zdCBtb2NrRGF0YSA9IFtdOw0KICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOw0KDQogICAgICBmb3IgKGxldCBpID0gNjsgaSA+PSAwOyBpLS0pIHsNCiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRvZGF5KTsNCiAgICAgICAgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpIC0gaSk7DQogICAgICAgIGNvbnN0IGRhdGVTdHIgPSBkYXRlLnRvSVNPU3RyaW5nKCkuc3Vic3RyaW5nKDAsIDEwKTsNCg0KICAgICAgICBtb2NrRGF0YS5wdXNoKHsNCiAgICAgICAgICBkYXRlOiBkYXRlU3RyLA0KICAgICAgICAgIHN5c3RlbUxvZ3M6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDUwKSArIDEwLA0KICAgICAgICAgIGJ1c2luZXNzTG9nczogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMzApICsgNSwNCiAgICAgICAgICBkYXRhQ2hhbmdlTG9nczogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMjApICsgMg0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy50cmVuZERhdGEgPSBtb2NrRGF0YTsNCiAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKTsNCiAgICB9LA0KDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQoNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCg0KICAgIC8qKiDliLfmlrDmlbDmja4gKi8NCiAgICBoYW5kbGVSZWZyZXNoKCkgew0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKTsNCiAgICAgIHRoaXMuZ2V0VHJlbmREYXRhKCk7DQogICAgfSwNCg0KICAgIC8qKiDlpJrpgInmoYbpgInkuK3mlbDmja4gKi8NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm9wZXJJZCB8fCBpdGVtLmxvZ0lkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KDQogICAgLyoqIOivpue7huaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVZpZXcocm93KSB7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsNCiAgICB9LA0KDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGxvZ0lkcyA9IHJvdyA/IFtyb3cubG9nSWRdIDogdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpgInkuK3nmoTns7vnu5/ml6Xlv5fmlbDmja7pobnvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g5L2/55So5pON5L2c5pel5b+X5Yig6ZmkQVBJDQogICAgICAgIGRlbE9wZXJMb2cobG9nSWRzLmpvaW4oJywnKSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIH0pOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5riF56m65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQ2xlYW4oKSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmuIXnqbrmiYDmnInns7vnu5/ml6Xlv5fmlbDmja7pobnvvJ/mraTmk43kvZzkuI3lj6/mgaLlpI3vvIEnKS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g5L2/55So5pON5L2c5pel5b+X5riF56m6QVBJDQogICAgICAgIGNsZWFuT3BlckxvZygpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heepuuaIkOWKnyIpOw0KICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5riF56m657O757uf5pel5b+X5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5riF56m65aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgIH0pOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65b2T5YmN562b6YCJ5p2h5Lu25LiL55qE57O757uf5pel5b+X5pWw5o2u77yfJykudGhlbigoKSA9PiB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHsuLi50aGlzLnF1ZXJ5UGFyYW1zfSwgdGhpcy5kYXRlUmFuZ2UpOw0KICAgICAgICBkZWxldGUgcGFyYW1zLnBhZ2VOdW07DQogICAgICAgIGRlbGV0ZSBwYXJhbXMucGFnZVNpemU7DQoNCiAgICAgICAgZXhwb3J0T3BlckxvZyhwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuZG93bmxvYWRGaWxlKHJlc3BvbnNlLCBg57O757uf5pel5b+XXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlr7zlh7rmiJDlip8iKTsNCiAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WvvOWHuuezu+e7n+aXpeW/l+Wksei0pTonLCBlcnJvcik7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuWvvOWHuuWksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICB9KTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOS4i+i9veaWh+S7tiAqLw0KICAgIGRvd25sb2FkRmlsZShkYXRhLCBmaWxlTmFtZSkgew0KICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtkYXRhXSwgeyB0eXBlOiAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnNwcmVhZHNoZWV0bWwuc2hlZXQnIH0pOw0KICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsNCiAgICAgIGxpbmsuaHJlZiA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOw0KICAgICAgbGluay5kb3dubG9hZCA9IGZpbGVOYW1lOw0KICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTsNCiAgICAgIGxpbmsuY2xpY2soKTsNCiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7DQogICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTChsaW5rLmhyZWYpOw0KICAgIH0sDQoNCiAgICAvKiog5b+r6YCf562b6YCJ5aSE55CGICovDQogICAgaGFuZGxlUXVpY2tGaWx0ZXIoZmlsdGVyS2V5KSB7DQogICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCBzdGFydE9mV2VlayA9IG5ldyBEYXRlKHRvZGF5LmdldEZ1bGxZZWFyKCksIHRvZGF5LmdldE1vbnRoKCksIHRvZGF5LmdldERhdGUoKSAtIHRvZGF5LmdldERheSgpKTsNCiAgICAgIGNvbnN0IHN0YXJ0T2ZNb250aCA9IG5ldyBEYXRlKHRvZGF5LmdldEZ1bGxZZWFyKCksIHRvZGF5LmdldE1vbnRoKCksIDEpOw0KICAgICAgDQogICAgICBzd2l0Y2ggKGZpbHRlcktleSkgew0KICAgICAgICBjYXNlICd0b2RheSc6DQogICAgICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbDQogICAgICAgICAgICB0aGlzLnBhcnNlVGltZSh0b2RheSwgJ3t5fS17bX0te2R9JyksDQogICAgICAgICAgICB0aGlzLnBhcnNlVGltZSh0b2RheSwgJ3t5fS17bX0te2R9JykNCiAgICAgICAgICBdOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICd3ZWVrJzoNCiAgICAgICAgICB0aGlzLmRhdGVSYW5nZSA9IFsNCiAgICAgICAgICAgIHRoaXMucGFyc2VUaW1lKHN0YXJ0T2ZXZWVrLCAne3l9LXttfS17ZH0nKSwNCiAgICAgICAgICAgIHRoaXMucGFyc2VUaW1lKHRvZGF5LCAne3l9LXttfS17ZH0nKQ0KICAgICAgICAgIF07DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ21vbnRoJzoNCiAgICAgICAgICB0aGlzLmRhdGVSYW5nZSA9IFsNCiAgICAgICAgICAgIHRoaXMucGFyc2VUaW1lKHN0YXJ0T2ZNb250aCwgJ3t5fS17bX0te2R9JyksDQogICAgICAgICAgICB0aGlzLnBhcnNlVGltZSh0b2RheSwgJ3t5fS17bX0te2R9JykNCiAgICAgICAgICBdOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdzeXN0ZW0nOg0KICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubG9nVHlwZSA9ICdTWVNURU0nOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdidXNpbmVzcyc6DQogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5sb2dUeXBlID0gJ0JVU0lORVNTJzsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAnZGF0YSc6DQogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5sb2dUeXBlID0gJ0RBVEEnOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIGJyZWFrOw0KICAgICAgfQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQoNCiAgICAvKiog5Li76KaB5pON5L2c5aSE55CGICovDQogICAgaGFuZGxlTWFpbkFjdGlvbihhY3Rpb25LZXkpIHsNCiAgICAgIHN3aXRjaCAoYWN0aW9uS2V5KSB7DQogICAgICAgIGNhc2UgJ2V4cG9ydCc6DQogICAgICAgICAgdGhpcy5oYW5kbGVFeHBvcnQoKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICBicmVhazsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOaJuemHj+aTjeS9nOWkhOeQhiAqLw0KICAgIGhhbmRsZUJhdGNoQWN0aW9uKGFjdGlvbktleSkgew0KICAgICAgc3dpdGNoIChhY3Rpb25LZXkpIHsNCiAgICAgICAgY2FzZSAnYmF0Y2hEZWxldGUnOg0KICAgICAgICAgIHRoaXMuaGFuZGxlRGVsZXRlKCk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlkajmnJ/lj5jljJblpITnkIYgKi8NCiAgICBoYW5kbGVQZXJpb2RDaGFuZ2UocGVyaW9kKSB7DQogICAgICB0aGlzLnRyZW5kUGVyaW9kID0gcGVyaW9kOw0KICAgICAgdGhpcy5nZXRUcmVuZERhdGEoKTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluaXpeW/l+exu+Wei+WQjeensCAqLw0KICAgIGdldExvZ1R5cGVOYW1lKHR5cGUpIHsNCiAgICAgIGNvbnN0IG5hbWVNYXAgPSB7DQogICAgICAgICdTWVNURU0nOiAn57O757uf5pON5L2cJywNCiAgICAgICAgJ0JVU0lORVNTJzogJ+S4muWKoeaTjeS9nCcsDQogICAgICAgICdEQVRBJzogJ+aVsOaNruWPmOabtCcNCiAgICAgIH07DQogICAgICByZXR1cm4gbmFtZU1hcFt0eXBlXSB8fCB0eXBlOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5pel5b+X57G75Z6L5qCH562+57G75Z6LICovDQogICAgZ2V0TG9nVHlwZVRhZ1R5cGUodHlwZSkgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgJ1NZU1RFTSc6ICdwcmltYXJ5JywNCiAgICAgICAgJ0JVU0lORVNTJzogJ3N1Y2Nlc3MnLA0KICAgICAgICAnREFUQSc6ICd3YXJuaW5nJw0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICdpbmZvJzsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluaTjeS9nOexu+Wei+WQjeensCAqLw0KICAgIGdldE9wZXJhdGlvblR5cGVOYW1lKHR5cGUpIHsNCiAgICAgIGNvbnN0IG5hbWVNYXAgPSB7DQogICAgICAgICdJTlNFUlQnOiAn5paw5aKeJywNCiAgICAgICAgJ1VQREFURSc6ICfkv67mlLknLA0KICAgICAgICAnREVMRVRFJzogJ+WIoOmZpCcsDQogICAgICAgICdTRUxFQ1QnOiAn5p+l6K+iJywNCiAgICAgICAgJ0VYUE9SVCc6ICflr7zlh7onLA0KICAgICAgICAnSU1QT1JUJzogJ+WvvOWFpScsDQogICAgICAgICdHUkFOVCc6ICfmjojmnYMnLA0KICAgICAgICAnT1RIRVInOiAn5YW25LuWJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBuYW1lTWFwW3R5cGVdIHx8IHR5cGU7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bmk43kvZznsbvlnovmoIfnrb7nsbvlnosgKi8NCiAgICBnZXRPcGVyYXRpb25UeXBlVGFnVHlwZSh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAnSU5TRVJUJzogJ3N1Y2Nlc3MnLA0KICAgICAgICAnVVBEQVRFJzogJ3dhcm5pbmcnLA0KICAgICAgICAnREVMRVRFJzogJ2RhbmdlcicsDQogICAgICAgICdTRUxFQ1QnOiAncHJpbWFyeScsDQogICAgICAgICdFWFBPUlQnOiAnaW5mbycsDQogICAgICAgICdJTVBPUlQnOiAnaW5mbycsDQogICAgICAgICdHUkFOVCc6ICd3YXJuaW5nJywNCiAgICAgICAgJ09USEVSJzogJ2luZm8nDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgJ2luZm8nOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "index.vue", "sourceRoot": "src/views/log/system", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计信息卡片 -->\r\n    <LogManagement\r\n      :statistics-data=\"statisticsData\"\r\n      :show-trend=\"true\"\r\n      trend-title=\"系统操作趋势\"\r\n      :trend-data=\"trendData\"\r\n      :trend-period=\"trendPeriod\"\r\n      :chart-config=\"chartConfig\"\r\n      :quick-filters=\"quickFilters\"\r\n      :main-actions=\"mainActions\"\r\n      :batch-actions=\"batchActions\"\r\n      :extra-actions=\"extraActions\"\r\n      :show-search.sync=\"showSearch\"\r\n      @period-change=\"handlePeriodChange\"\r\n      @quick-filter=\"handleQuickFilter\"\r\n      @main-action=\"handleMainAction\"\r\n      @batch-action=\"handleBatchAction\"\r\n      @refresh=\"handleRefresh\"\r\n    />\r\n\r\n    <!-- 高级搜索表单 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-form-item label=\"日志类型\" prop=\"logType\">\r\n        <el-select v-model=\"queryParams.logType\" placeholder=\"请选择日志类型\" clearable>\r\n          <el-option label=\"系统操作\" value=\"SYSTEM\" />\r\n          <el-option label=\"业务操作\" value=\"BUSINESS\" />\r\n          <el-option label=\"数据变更\" value=\"DATA\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作模块\" prop=\"moduleName\">\r\n        <el-input\r\n          v-model=\"queryParams.moduleName\"\r\n          placeholder=\"请输入操作模块\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"操作人员\" prop=\"operator\">\r\n        <el-input\r\n          v-model=\"queryParams.operator\"\r\n          placeholder=\"请输入操作人员\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"操作类型\" prop=\"operationType\">\r\n        <el-select v-model=\"queryParams.operationType\" placeholder=\"操作类型\" clearable>\r\n          <el-option label=\"新增\" value=\"INSERT\" />\r\n          <el-option label=\"修改\" value=\"UPDATE\" />\r\n          <el-option label=\"删除\" value=\"DELETE\" />\r\n          <el-option label=\"查询\" value=\"SELECT\" />\r\n          <el-option label=\"导出\" value=\"EXPORT\" />\r\n          <el-option label=\"导入\" value=\"IMPORT\" />\r\n          <el-option label=\"授权\" value=\"GRANT\" />\r\n          <el-option label=\"其他\" value=\"OTHER\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作状态\" prop=\"operationStatus\">\r\n        <el-select v-model=\"queryParams.operationStatus\" placeholder=\"操作状态\" clearable>\r\n          <el-option label=\"成功\" value=\"0\" />\r\n          <el-option label=\"失败\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          :picker-options=\"pickerOptions\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['log:operation:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          @click=\"handleClean\"\r\n          v-hasPermi=\"['log:operation:remove']\"\r\n        >清空</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['log:operation:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 数据表格 -->\r\n    <el-table v-loading=\"loading\" :data=\"systemLogList\" @selection-change=\"handleSelectionChange\" empty-text=\"暂无数据\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"日志编号\" align=\"center\" prop=\"logId\" width=\"80\" />\r\n      <el-table-column label=\"日志类型\" align=\"center\" prop=\"logType\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getLogTypeTagType(scope.row.logType)\">\r\n            {{ getLogTypeName(scope.row.logType) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作模块\" align=\"center\" prop=\"moduleName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationType\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getOperationTypeTagType(scope.row.operationType)\">\r\n            {{ getOperationTypeName(scope.row.operationType) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作名称\" align=\"center\" prop=\"operationName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作人员\" align=\"center\" prop=\"operator\" width=\"120\" />\r\n      <el-table-column label=\"操作状态\" align=\"center\" prop=\"operationStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.operationStatus === '0' ? 'success' : 'danger'\">\r\n            {{ scope.row.operationStatus === '0' ? '成功' : '失败' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"耗时(ms)\" align=\"center\" prop=\"costTime\" width=\"100\" />\r\n      <el-table-column label=\"操作时间\" align=\"center\" prop=\"operationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.operationTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['log:system:detail']\"\r\n          >详细</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 系统日志详细 -->\r\n    <el-dialog title=\"系统日志详细\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\" size=\"mini\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"日志类型：\">\r\n              <el-tag :type=\"getLogTypeTagType(form.logType)\">\r\n                {{ getLogTypeName(form.logType) }}\r\n              </el-tag>\r\n            </el-form-item>\r\n            <el-form-item label=\"操作模块：\">{{ form.moduleName }}</el-form-item>\r\n            <el-form-item label=\"操作类型：\">\r\n              <el-tag :type=\"getOperationTypeTagType(form.operationType)\">\r\n                {{ getOperationTypeName(form.operationType) }}\r\n              </el-tag>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作人员：\">{{ form.createBy }}</el-form-item>\r\n            <el-form-item label=\"操作IP：\">{{ form.operatorIp }}</el-form-item>\r\n            <el-form-item label=\"操作时间：\">{{ parseTime(form.createTime) }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"操作名称：\">{{ form.operationName }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"请求地址：\">{{ form.requestUrl }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"请求方式：\">{{ form.requestMethod }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"耗时：\">{{ form.costTime }}ms</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"请求参数：\">\r\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.requestParams\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"返回结果：\">\r\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.responseResult\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作状态：\">\r\n              <el-tag :type=\"form.operationStatus === '0' ? 'success' : 'danger'\">\r\n                {{ form.operationStatus === '0' ? '成功' : '失败' }}\r\n              </el-tag>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" v-if=\"form.operationStatus === '1'\">\r\n            <el-form-item label=\"错误信息：\">\r\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.errorMsg\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"open = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport LogManagement from \"@/components/LogManagement\";\r\nimport { listOperLog, delOperLog, cleanOperLog, exportOperLog } from \"@/api/monitor/operlog\";\r\n\r\nexport default {\r\n  name: \"SystemLog\",\r\n  components: {\r\n    LogManagement\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      systemLogList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        logType: \"SYSTEM\",  // 默认查询系统日志\r\n        moduleName: undefined,\r\n        createBy: undefined,\r\n        operationType: undefined,\r\n        operationStatus: undefined\r\n      },\r\n      // 统计数据\r\n      statisticsData: [],\r\n      // 趋势数据\r\n      trendData: [],\r\n      trendPeriod: '7d',\r\n      // 图表配置\r\n      chartConfig: {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          data: ['系统操作', '业务操作', '数据变更']\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: []\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        yAxisName: '操作数量',\r\n        series: [\r\n          {\r\n            name: '系统操作',\r\n            type: 'line',\r\n            dataKey: 'systemLogs',\r\n            stack: '总量',\r\n            areaStyle: {\r\n              startColor: '#80FFA5',\r\n              endColor: '#008F52'\r\n            },\r\n            smooth: true\r\n          },\r\n          {\r\n            name: '业务操作',\r\n            type: 'line',\r\n            dataKey: 'businessLogs',\r\n            stack: '总量',\r\n            areaStyle: {\r\n              startColor: '#FFA0A0',\r\n              endColor: '#8F0000'\r\n            },\r\n            smooth: true\r\n          },\r\n          {\r\n            name: '数据变更',\r\n            type: 'line',\r\n            dataKey: 'dataChangeLogs',\r\n            stack: '总量',\r\n            areaStyle: {\r\n              startColor: '#A0A0FF',\r\n              endColor: '#00008F'\r\n            },\r\n            smooth: true\r\n          }\r\n        ]\r\n      },\r\n      // 快速筛选\r\n      quickFilters: [\r\n        { key: 'today', label: '今日', icon: 'el-icon-date' },\r\n        { key: 'week', label: '本周', icon: 'el-icon-date' },\r\n        { key: 'month', label: '本月', icon: 'el-icon-date' },\r\n        { key: 'system', label: '系统操作', icon: 'el-icon-setting' },\r\n        { key: 'business', label: '业务操作', icon: 'el-icon-s-order' },\r\n        { key: 'data', label: '数据操作', icon: 'el-icon-s-data' }\r\n      ],\r\n      // 主要操作\r\n      mainActions: [\r\n        {\r\n          key: 'export',\r\n          label: '导出Excel',\r\n          type: 'warning',\r\n          icon: 'el-icon-download',\r\n          permission: 'log:operation:export'\r\n        }\r\n      ],\r\n      // 批量操作\r\n      batchActions: [\r\n        {\r\n          key: 'batchDelete',\r\n          label: '批量删除',\r\n          icon: 'el-icon-delete',\r\n          permission: 'log:operation:remove'\r\n        }\r\n      ],\r\n      // 额外操作\r\n      extraActions: [\r\n        {\r\n          key: 'clean',\r\n          label: '清空日志',\r\n          type: 'danger',\r\n          icon: 'el-icon-delete',\r\n          permission: 'log:operation:remove'\r\n        }\r\n      ],\r\n      // 日期选择器配置\r\n      pickerOptions: {\r\n        shortcuts: [{\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近三个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    /** 初始化数据 */\r\n    async initData() {\r\n      try {\r\n        await Promise.all([\r\n          this.getStatistics(),\r\n          this.getTrendData()\r\n        ]);\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('初始化数据失败:', error);\r\n      }\r\n    },\r\n\r\n    /** 查询系统日志 */\r\n    getList() {\r\n      this.loading = true;\r\n      const params = this.addDateRange({...this.queryParams}, this.dateRange);\r\n      console.log('实际请求参数:', params); // 打印查询参数\r\n\r\n      // 使用操作日志API获取真实数据\r\n      listOperLog(params).then(response => {\r\n        console.log('API响应数据:', response); // 打印完整响应\r\n        // 映射字段名称\r\n        const mappedData = (response.rows || []).map(item => ({\r\n          logId: item.operId,\r\n          logType: item.businessType === 0 ? 'SYSTEM' : 'BUSINESS',\r\n          moduleName: item.title,\r\n          operationType: this.mapOperationType(item.businessType),\r\n          operationName: item.method,\r\n          operator: item.operName,\r\n          operationStatus: item.status,\r\n          costTime: item.costTime,\r\n          operationTime: item.operTime,\r\n          operatorIp: item.operIp,\r\n          operatorLocation: item.operLocation,\r\n          requestMethod: item.requestMethod,\r\n          requestUrl: item.operUrl,\r\n          requestParam: item.operParam,\r\n          jsonResult: item.jsonResult,\r\n          errorMsg: item.errorMsg\r\n        }));\r\n\r\n        this.systemLogList = mappedData;\r\n        this.total = response.total || 0;\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('API请求失败:', error); // 打印错误详情\r\n        this.loading = false;\r\n        this.$message.error('查询系统日志失败，请稍后重试');\r\n      });\r\n    },\r\n\r\n    /** 映射操作类型 */\r\n    mapOperationType(businessType) {\r\n      const typeMap = {\r\n        0: 'OTHER',\r\n        1: 'INSERT',\r\n        2: 'UPDATE',\r\n        3: 'DELETE',\r\n        4: 'GRANT',\r\n        5: 'EXPORT',\r\n        6: 'IMPORT',\r\n        7: 'FORCE',\r\n        8: 'GENCODE',\r\n        9: 'CLEAN'\r\n      };\r\n      return typeMap[businessType] || 'OTHER';\r\n    },\r\n\r\n    /** 获取统计数据 */\r\n    getStatistics() {\r\n      // 使用模拟数据，因为操作日志API没有统计接口\r\n      this.statisticsData = [\r\n        { title: '总日志数', value: this.systemLogList.length, icon: 'el-icon-s-data', color: '#409EFF' },\r\n        { title: '今日日志', value: 0, icon: 'el-icon-date', color: '#67C23A' },\r\n        { title: '系统操作', value: 0, icon: 'el-icon-setting', color: '#E6A23C' },\r\n        { title: '业务操作', value: 0, icon: 'el-icon-s-order', color: '#F56C6C' }\r\n      ];\r\n      return Promise.resolve();\r\n    },\r\n\r\n    /** 获取趋势数据 */\r\n    getTrendData() {\r\n      // 使用模拟数据，因为操作日志API没有趋势接口\r\n      const mockData = [];\r\n      const today = new Date();\r\n\r\n      for (let i = 6; i >= 0; i--) {\r\n        const date = new Date(today);\r\n        date.setDate(date.getDate() - i);\r\n        const dateStr = date.toISOString().substring(0, 10);\r\n\r\n        mockData.push({\r\n          date: dateStr,\r\n          systemLogs: Math.floor(Math.random() * 50) + 10,\r\n          businessLogs: Math.floor(Math.random() * 30) + 5,\r\n          dataChangeLogs: Math.floor(Math.random() * 20) + 2\r\n        });\r\n      }\r\n\r\n      this.trendData = mockData;\r\n      return Promise.resolve();\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 刷新数据 */\r\n    handleRefresh() {\r\n      this.getList();\r\n      this.getStatistics();\r\n      this.getTrendData();\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.operId || item.logId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true;\r\n      this.form = { ...row };\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const logIds = row ? [row.logId] : this.ids;\r\n      this.$modal.confirm('是否确认删除选中的系统日志数据项？').then(() => {\r\n        // 使用操作日志删除API\r\n        delOperLog(logIds.join(',')).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 清空按钮操作 */\r\n    handleClean() {\r\n      this.$modal.confirm('是否确认清空所有系统日志数据项？此操作不可恢复！').then(() => {\r\n        // 使用操作日志清空API\r\n        cleanOperLog().then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"清空成功\");\r\n        }).catch(error => {\r\n          console.error('清空系统日志失败:', error);\r\n          this.$modal.msgError(\"清空失败，请稍后重试\");\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出当前筛选条件下的系统日志数据？').then(() => {\r\n        const params = this.addDateRange({...this.queryParams}, this.dateRange);\r\n        delete params.pageNum;\r\n        delete params.pageSize;\r\n\r\n        exportOperLog(params).then(response => {\r\n          this.downloadFile(response, `系统日志_${new Date().getTime()}.xlsx`);\r\n          this.$modal.msgSuccess(\"导出成功\");\r\n        }).catch(error => {\r\n          console.error('导出系统日志失败:', error);\r\n          this.$modal.msgError(\"导出失败，请稍后重试\");\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 下载文件 */\r\n    downloadFile(data, fileName) {\r\n      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\r\n      const link = document.createElement('a');\r\n      link.href = window.URL.createObjectURL(blob);\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(link.href);\r\n    },\r\n\r\n    /** 快速筛选处理 */\r\n    handleQuickFilter(filterKey) {\r\n      const today = new Date();\r\n      const startOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());\r\n      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\r\n      \r\n      switch (filterKey) {\r\n        case 'today':\r\n          this.dateRange = [\r\n            this.parseTime(today, '{y}-{m}-{d}'),\r\n            this.parseTime(today, '{y}-{m}-{d}')\r\n          ];\r\n          break;\r\n        case 'week':\r\n          this.dateRange = [\r\n            this.parseTime(startOfWeek, '{y}-{m}-{d}'),\r\n            this.parseTime(today, '{y}-{m}-{d}')\r\n          ];\r\n          break;\r\n        case 'month':\r\n          this.dateRange = [\r\n            this.parseTime(startOfMonth, '{y}-{m}-{d}'),\r\n            this.parseTime(today, '{y}-{m}-{d}')\r\n          ];\r\n          break;\r\n        case 'system':\r\n          this.queryParams.logType = 'SYSTEM';\r\n          break;\r\n        case 'business':\r\n          this.queryParams.logType = 'BUSINESS';\r\n          break;\r\n        case 'data':\r\n          this.queryParams.logType = 'DATA';\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 主要操作处理 */\r\n    handleMainAction(actionKey) {\r\n      switch (actionKey) {\r\n        case 'export':\r\n          this.handleExport();\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 批量操作处理 */\r\n    handleBatchAction(actionKey) {\r\n      switch (actionKey) {\r\n        case 'batchDelete':\r\n          this.handleDelete();\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 周期变化处理 */\r\n    handlePeriodChange(period) {\r\n      this.trendPeriod = period;\r\n      this.getTrendData();\r\n    },\r\n\r\n    /** 获取日志类型名称 */\r\n    getLogTypeName(type) {\r\n      const nameMap = {\r\n        'SYSTEM': '系统操作',\r\n        'BUSINESS': '业务操作',\r\n        'DATA': '数据变更'\r\n      };\r\n      return nameMap[type] || type;\r\n    },\r\n\r\n    /** 获取日志类型标签类型 */\r\n    getLogTypeTagType(type) {\r\n      const typeMap = {\r\n        'SYSTEM': 'primary',\r\n        'BUSINESS': 'success',\r\n        'DATA': 'warning'\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    /** 获取操作类型名称 */\r\n    getOperationTypeName(type) {\r\n      const nameMap = {\r\n        'INSERT': '新增',\r\n        'UPDATE': '修改',\r\n        'DELETE': '删除',\r\n        'SELECT': '查询',\r\n        'EXPORT': '导出',\r\n        'IMPORT': '导入',\r\n        'GRANT': '授权',\r\n        'OTHER': '其他'\r\n      };\r\n      return nameMap[type] || type;\r\n    },\r\n\r\n    /** 获取操作类型标签类型 */\r\n    getOperationTypeTagType(type) {\r\n      const typeMap = {\r\n        'INSERT': 'success',\r\n        'UPDATE': 'warning',\r\n        'DELETE': 'danger',\r\n        'SELECT': 'primary',\r\n        'EXPORT': 'info',\r\n        'IMPORT': 'info',\r\n        'GRANT': 'warning',\r\n        'OTHER': 'info'\r\n      };\r\n      return typeMap[type] || 'info';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n</style>"]}]}