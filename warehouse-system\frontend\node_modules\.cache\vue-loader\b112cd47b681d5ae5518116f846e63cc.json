{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\out\\print.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\out\\print.vue", "mtime": 1756537509311}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEludmVudG9yeU91dCB9IGZyb20gIkAvYXBpL2ludmVudG9yeS9vdXQiOwppbXBvcnQgeyBnZXRCYXRjaFVzZXJSZWFsTmFtZXMgfSBmcm9tICJAL3V0aWxzL3VzZXJVdGlscyI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIk91dFByaW50IiwKICBkaWN0czogWydpbnZlbnRvcnlfb3V0X3R5cGUnLCAnaW52ZW50b3J5X291dF9zdGF0dXMnXSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGlzUHJpbnRpbmc6IGZhbHNlLAogICAgICBwcmludFNldHRpbmdzVmlzaWJsZTogZmFsc2UsCiAgICAgIG91dERhdGE6IHsKICAgICAgICBvdXRDb2RlOiAiIiwKICAgICAgICB3YXJlaG91c2VOYW1lOiAiIiwKICAgICAgICBvdXRUaW1lOiBudWxsLAogICAgICAgIG91dFR5cGU6ICIiLAogICAgICAgIHN0YXR1czogIiIsCiAgICAgICAgY3JlYXRlQnk6ICIiLAogICAgICAgIGNyZWF0ZUJ5TmFtZTogIiIsCiAgICAgICAgYXVkaXRCeTogIiIsCiAgICAgICAgYXVkaXRCeU5hbWU6ICIiLAogICAgICAgIHJlbWFyazogIiIsCiAgICAgICAgZGV0YWlsczogW10KICAgICAgfSwKICAgICAgb3V0SWQ6IG51bGwsCiAgICAgIHByaW50U2V0dGluZ3M6IHsKICAgICAgICBtYXJnaW5Ub3A6IDE1LAogICAgICAgIG1hcmdpblJpZ2h0OiAxNSwKICAgICAgICBtYXJnaW5Cb3R0b206IDE1LAogICAgICAgIG1hcmdpbkxlZnQ6IDE1LAogICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICBvcmllbnRhdGlvbjogJ3BvcnRyYWl0JywKICAgICAgICBwYXBlclNpemU6ICdBNCcKICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLm91dElkID0gdGhpcy4kcm91dGUucGFyYW1zLm91dElkIHx8IHRoaXMuJHJvdXRlLnBhcmFtcy5pZCB8fCB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsKICAgIGNvbnNvbGUubG9nKCflh7rlupPljZXmiZPljbDpobXpnaLliJ3lp4vljJbvvIzlh7rlupPljZVJRDonLCB0aGlzLm91dElkKTsKICAgIGlmICghdGhpcy5vdXRJZCkgewogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvLrlsJHlh7rlupPljZVJROWPguaVsCcpOwogICAgICByZXR1cm47CiAgICB9CiAgICB0aGlzLmdldE91dERhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDojrflj5blh7rlupPljZXkv6Hmga8gKi8KICAgIGdldE91dERhdGEoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGdldEludmVudG9yeU91dCh0aGlzLm91dElkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLm91dERhdGEgPSByZXNwb25zZS5kYXRhOwogICAgICAgIAogICAgICAgIC8vIOiOt+WPlueUqOaIt+ecn+WunuWnk+WQjQogICAgICAgIGNvbnN0IHVzZXJOYW1lcyA9IFsKICAgICAgICAgIHRoaXMub3V0RGF0YS5jcmVhdGVCeSwKICAgICAgICAgIHRoaXMub3V0RGF0YS5hdWRpdEJ5CiAgICAgICAgXS5maWx0ZXIobmFtZSA9PiBuYW1lKTsgLy8g6L+H5ruk56m65YC8CgogICAgICAgIGlmICh1c2VyTmFtZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgZ2V0QmF0Y2hVc2VyUmVhbE5hbWVzKHVzZXJOYW1lcykudGhlbihuYW1lTWFwID0+IHsKICAgICAgICAgICAgLy8g5pu05paw55So5oi355yf5a6e5aeT5ZCNCiAgICAgICAgICAgIGlmICh0aGlzLm91dERhdGEuY3JlYXRlQnkgJiYgbmFtZU1hcFt0aGlzLm91dERhdGEuY3JlYXRlQnldKSB7CiAgICAgICAgICAgICAgdGhpcy5vdXREYXRhLmNyZWF0ZUJ5TmFtZSA9IG5hbWVNYXBbdGhpcy5vdXREYXRhLmNyZWF0ZUJ5XTsKICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAodGhpcy5vdXREYXRhLmF1ZGl0QnkgJiYgbmFtZU1hcFt0aGlzLm91dERhdGEuYXVkaXRCeV0pIHsKICAgICAgICAgICAgICB0aGlzLm91dERhdGEuYXVkaXRCeU5hbWUgPSBuYW1lTWFwW3RoaXMub3V0RGF0YS5hdWRpdEJ5XTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICAgIAogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+W5Ye65bqT5Y2V5L+h5oGv5aSx6LSlIik7CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLyoqIOWkhOeQhuaJk+WNsOiuvue9riAqLwogICAgaGFuZGxlUHJpbnRTZXR0aW5ncygpIHsKICAgICAgdGhpcy5wcmludFNldHRpbmdzVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgCiAgICAvKiog5L+d5a2Y5omT5Y2w6K6+572uICovCiAgICBzYXZlUHJpbnRTZXR0aW5ncygpIHsKICAgICAgLy8g5L+d5a2Y5Yiw5pys5Zyw5a2Y5YKoCiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdvdXRQcmludFNldHRpbmdzJywgSlNPTi5zdHJpbmdpZnkodGhpcy5wcmludFNldHRpbmdzKSk7CiAgICAgIHRoaXMucHJpbnRTZXR0aW5nc1Zpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmiZPljbDorr7nva7lt7Lkv53lrZgnKTsKICAgIH0sCiAgICAKICAgIC8qKiDliqDovb3miZPljbDorr7nva4gKi8KICAgIGxvYWRQcmludFNldHRpbmdzKCkgewogICAgICBjb25zdCBzYXZlZFNldHRpbmdzID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ291dFByaW50U2V0dGluZ3MnKTsKICAgICAgaWYgKHNhdmVkU2V0dGluZ3MpIHsKICAgICAgICB0aGlzLnByaW50U2V0dGluZ3MgPSBKU09OLnBhcnNlKHNhdmVkU2V0dGluZ3MpOwogICAgICB9CiAgICB9LAogICAgCiAgICAvKiog5qC85byP5YyW6YeR6aKdICovCiAgICBmb3JtYXRBbW91bnQocm93LCBjb2x1bW4sIGNlbGxWYWx1ZSkgewogICAgICBpZiAoIWNlbGxWYWx1ZSkgcmV0dXJuICcwLjAwJzsKICAgICAgcmV0dXJuIHBhcnNlRmxvYXQoY2VsbFZhbHVlKS50b0ZpeGVkKDIpOwogICAgfSwKICAgIAogICAgLyoqIOaJk+WNsCAqLwogICAgaGFuZGxlUHJpbnQoKSB7CiAgICAgIHRoaXMuaXNQcmludGluZyA9IHRydWU7CiAgICAgIAogICAgICAvLyDnoa7kv53mlbDmja7lt7LliqDovb0KICAgICAgaWYgKCF0aGlzLm91dERhdGEub3V0Q29kZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pWw5o2u6L+Y5Zyo5Yqg6L295Lit77yM6K+356iN5ZCO5YaN6K+VJyk7CiAgICAgICAgdGhpcy5pc1ByaW50aW5nID0gZmFsc2U7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIAogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgLy8g5Yib5bu65paw56qX5Y+j6L+b6KGM5omT5Y2wCiAgICAgICAgY29uc3QgcHJpbnRXaW5kb3cgPSB3aW5kb3cub3BlbignJywgJ19ibGFuaycsICd3aWR0aD0xMDAwLGhlaWdodD04MDAsc2Nyb2xsYmFycz15ZXMscmVzaXphYmxlPXllcycpOwogICAgICAgIAogICAgICAgIC8vIOeUn+aIkOaJk+WNsOWGheWuuQogICAgICAgIGNvbnN0IHByaW50Q29udGVudCA9IHRoaXMuZ2VuZXJhdGVQcmludEhUTUwoKTsKICAgICAgICAKICAgICAgICBwcmludFdpbmRvdy5kb2N1bWVudC53cml0ZShwcmludENvbnRlbnQpOwogICAgICAgIHByaW50V2luZG93LmRvY3VtZW50LmNsb3NlKCk7CiAgICAgICAgCiAgICAgICAgLy8g562J5b6F5YaF5a655Yqg6L295a6M5oiQ5ZCO5omT5Y2wCiAgICAgICAgcHJpbnRXaW5kb3cub25sb2FkID0gKCkgPT4gewogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgIHByaW50V2luZG93LnByaW50KCk7CiAgICAgICAgICAgIC8vIOaJk+WNsOWujOaIkOWQjuS4jeiHquWKqOWFs+mXreeql+WPo++8jOiuqeeUqOaIt+aJi+WKqOWFs+mXrQogICAgICAgICAgICB0aGlzLmlzUHJpbnRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0sIDUwMCk7CiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9LAogICAgCiAgICAvKiog55Sf5oiQ5omT5Y2w6aG16Z2iSFRNTCAqLwogICAgZ2VuZXJhdGVQcmludEhUTUwoKSB7CiAgICAgIGNvbnN0IGRldGFpbHMgPSB0aGlzLm91dERhdGEuZGV0YWlscyB8fCBbXTsKICAgICAgbGV0IGRldGFpbHNIVE1MID0gJyc7CiAgICAgIAogICAgICBkZXRhaWxzLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7CiAgICAgICAgZGV0YWlsc0hUTUwgKz0gYAogICAgICAgICAgPHRyPgogICAgICAgICAgICA8dGQgc3R5bGU9ImJvcmRlcjogMXB4IHNvbGlkICMwMDA7IHBhZGRpbmc6IDhweDsgdGV4dC1hbGlnbjogY2VudGVyOyI+JHtpbmRleCArIDF9PC90ZD4KICAgICAgICAgICAgPHRkIHN0eWxlPSJib3JkZXI6IDFweCBzb2xpZCAjMDAwOyBwYWRkaW5nOiA4cHg7IHRleHQtYWxpZ246IGNlbnRlcjsiPiR7aXRlbS5wcm9kdWN0Q29kZSB8fCAnJ308L3RkPgogICAgICAgICAgICA8dGQgc3R5bGU9ImJvcmRlcjogMXB4IHNvbGlkICMwMDA7IHBhZGRpbmc6IDhweDsgdGV4dC1hbGlnbjogY2VudGVyOyI+JHtpdGVtLnByb2R1Y3ROYW1lIHx8ICcnfTwvdGQ+CiAgICAgICAgICAgIDx0ZCBzdHlsZT0iYm9yZGVyOiAxcHggc29saWQgIzAwMDsgcGFkZGluZzogOHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7Ij4ke2l0ZW0ucXVhbnRpdHkgfHwgJyd9PC90ZD4KICAgICAgICAgICAgPHRkIHN0eWxlPSJib3JkZXI6IDFweCBzb2xpZCAjMDAwOyBwYWRkaW5nOiA4cHg7IHRleHQtYWxpZ246IGNlbnRlcjsiPiR7dGhpcy5mb3JtYXRBbW91bnQobnVsbCwgbnVsbCwgaXRlbS5wcmljZSl9PC90ZD4KICAgICAgICAgICAgPHRkIHN0eWxlPSJib3JkZXI6IDFweCBzb2xpZCAjMDAwOyBwYWRkaW5nOiA4cHg7IHRleHQtYWxpZ246IGNlbnRlcjsiPiR7dGhpcy5mb3JtYXRBbW91bnQobnVsbCwgbnVsbCwgaXRlbS5hbW91bnQpfTwvdGQ+CiAgICAgICAgICAgIDx0ZCBzdHlsZT0iYm9yZGVyOiAxcHggc29saWQgIzAwMDsgcGFkZGluZzogOHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7Ij4ke2l0ZW0ucmVtYXJrIHx8ICcnfTwvdGQ+CiAgICAgICAgICA8L3RyPgogICAgICAgIGA7CiAgICAgIH0pOwogICAgICAKICAgICAgLy8g5qC55o2u6K6+572u56Gu5a6a6aG16Z2i5pa55ZCR5ZKM57q45byg5aSn5bCPCiAgICAgIGxldCBwYWdlU2l6ZVN0eWxlID0gJyc7CiAgICAgIGlmICh0aGlzLnByaW50U2V0dGluZ3Mub3JpZW50YXRpb24gPT09ICdsYW5kc2NhcGUnKSB7CiAgICAgICAgcGFnZVNpemVTdHlsZSA9IGBzaXplOiAke3RoaXMucHJpbnRTZXR0aW5ncy5wYXBlclNpemV9IGxhbmRzY2FwZTtgOwogICAgICB9IGVsc2UgewogICAgICAgIHBhZ2VTaXplU3R5bGUgPSBgc2l6ZTogJHt0aGlzLnByaW50U2V0dGluZ3MucGFwZXJTaXplfSBwb3J0cmFpdDtgOwogICAgICB9CiAgICAgIAogICAgICByZXR1cm4gYAogICAgICAgIDwhRE9DVFlQRSBodG1sPgogICAgICAgIDxodG1sPgogICAgICAgIDxoZWFkPgogICAgICAgICAgPG1ldGEgY2hhcnNldD0iVVRGLTgiPgogICAgICAgICAgPG1ldGEgbmFtZT0idmlld3BvcnQiIGNvbnRlbnQ9IndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xLjAiPgogICAgICAgICAgPHRpdGxlPuWHuuW6k+WNleaJk+WNsDwvdGl0bGU+CiAgICAgICAgICA8c3R5bGU+CiAgICAgICAgICAgIEBwYWdlIHsgCiAgICAgICAgICAgICAgJHtwYWdlU2l6ZVN0eWxlfQogICAgICAgICAgICAgIG1hcmdpbjogJHt0aGlzLnByaW50U2V0dGluZ3MubWFyZ2luVG9wfW1tICR7dGhpcy5wcmludFNldHRpbmdzLm1hcmdpblJpZ2h0fW1tICR7dGhpcy5wcmludFNldHRpbmdzLm1hcmdpbkJvdHRvbX1tbSAke3RoaXMucHJpbnRTZXR0aW5ncy5tYXJnaW5MZWZ0fW1tOyAKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgKiB7CiAgICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgaHRtbCwgYm9keSB7IAogICAgICAgICAgICAgIGZvbnQtZmFtaWx5OiAiTWljcm9zb2Z0IFlhSGVpIiwgU2ltU3VuLCBzYW5zLXNlcmlmOyAKICAgICAgICAgICAgICBmb250LXNpemU6ICR7dGhpcy5wcmludFNldHRpbmdzLmZvbnRTaXplfXB0OyAKICAgICAgICAgICAgICBjb2xvcjogIzAwMDsgCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsgCiAgICAgICAgICAgICAgbWFyZ2luOiAwOyAKICAgICAgICAgICAgICBwYWRkaW5nOiAke3RoaXMucHJpbnRTZXR0aW5ncy5tYXJnaW5Ub3AvM31tbTsKICAgICAgICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICAgICAgICAtd2Via2l0LXByaW50LWNvbG9yLWFkanVzdDogZXhhY3Q7CiAgICAgICAgICAgICAgcHJpbnQtY29sb3ItYWRqdXN0OiBleGFjdDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmNvbnRhaW5lciB7CiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgICAgICAgbWF4LXdpZHRoOiAxMDAwcHg7CiAgICAgICAgICAgICAgbWFyZ2luOiAwIGF1dG87CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5wcmludC1jb250YWluZXIgewogICAgICAgICAgICAgIHBhZGRpbmc6IDIwcHg7CiAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICAgICAgICAgICAgICBjb2xvcjogIzAwMDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLnByaW50LWNvbnRlbnQgewogICAgICAgICAgICAgIG1heC13aWR0aDogMTAwMHB4OwogICAgICAgICAgICAgIG1hcmdpbjogMCBhdXRvOwogICAgICAgICAgICAgIGZvbnQtZmFtaWx5OiAiU2ltU3VuIiwgIuWui+S9kyIsIHNlcmlmOwogICAgICAgICAgICAgIGNvbG9yOiAjMDAwOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAub3V0LWhlYWRlciB7CiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7CiAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMwMDA7CiAgICAgICAgICAgICAgcGFkZGluZy1ib3R0b206IDEwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5vdXQtaGVhZGVyIC50aXRsZSB7CiAgICAgICAgICAgICAgZm9udC1zaXplOiAke3RoaXMucHJpbnRTZXR0aW5ncy5mb250U2l6ZSArIDh9cHQ7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgICAgbWFyZ2luOiAwIDAgMjBweCAwOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaGVhZGVyLWluZm8gewogICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaGVhZGVyLWl0ZW0gewogICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmhlYWRlci1pdGVtIC5sYWJlbCB7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5vdXQtaW5mbyB7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmluZm8taXRlbSB7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDsKICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaW5mby1pdGVtIC5sYWJlbCB7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgICAgbWluLXdpZHRoOiAxMDBweDsKICAgICAgICAgICAgICBmbGV4LXNocmluazogMDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLm91dC1kZXRhaWxzIGgzLAogICAgICAgICAgICAuYXBwcm92YWwtaW5mbyBoMyB7CiAgICAgICAgICAgICAgZm9udC1zaXplOiAke3RoaXMucHJpbnRTZXR0aW5ncy5mb250U2l6ZSArIDR9cHQ7CiAgICAgICAgICAgICAgbWFyZ2luOiAwIDAgMTVweCAwOwogICAgICAgICAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzQwOUVGRjsKICAgICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDEwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIHRhYmxlIHsKICAgICAgICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICAgICAgICBib3JkZXItY29sbGFwc2U6IGNvbGxhcHNlOwogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7CiAgICAgICAgICAgICAgdGFibGUtbGF5b3V0OiBmaXhlZDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgdGggewogICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7CiAgICAgICAgICAgICAgY29sb3I6ICMwMDA7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzAwMDsKICAgICAgICAgICAgICBwYWRkaW5nOiA4cHg7CiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICB0ZCB7CiAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzAwMDsKICAgICAgICAgICAgICBwYWRkaW5nOiA4cHg7CiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICAgIHdvcmQtd3JhcDogYnJlYWstd29yZDsKICAgICAgICAgICAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5vdXQtZm9vdGVyIHsKICAgICAgICAgICAgICBtYXJnaW46IDMwcHggMDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmZvb3Rlci1pdGVtIHsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuc2lnbmF0dXJlLWxpbmUgewogICAgICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgICAgICAgICB3aWR0aDogODBweDsKICAgICAgICAgICAgICBoZWlnaHQ6IDFweDsKICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDAwOwogICAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4OwogICAgICAgICAgICB9CiAgICAgICAgICA8L3N0eWxlPgogICAgICAgIDwvaGVhZD4KICAgICAgICA8Ym9keT4KICAgICAgICAgIDxkaXYgY2xhc3M9InByaW50LWNvbnRhaW5lciI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InByaW50LWNvbnRlbnQiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im91dC1oZWFkZXIiPgogICAgICAgICAgICAgICAgPGgyIGNsYXNzPSJ0aXRsZSI+5Ye65bqT5Y2VPC9oMj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImhlYWRlci1pbmZvIj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyLWl0ZW0iPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJsYWJlbCI+5Ye65bqT5Y2V5Y+377yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJ2YWx1ZSI+JHt0aGlzLm91dERhdGEub3V0Q29kZSB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItaXRlbSI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImxhYmVsIj7lh7rlupPml6XmnJ/vvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InZhbHVlIj4ke3RoaXMucGFyc2VUaW1lKHRoaXMub3V0RGF0YS5vdXRUaW1lKSB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItaXRlbSI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImxhYmVsIj7nirbmgIHvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InZhbHVlIj4ke3RoaXMuZ2V0U3RhdHVzTmFtZSh0aGlzLm91dERhdGEuc3RhdHVzKSB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ib3V0LWluZm8iPgogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsgbWFyZ2luLWJvdHRvbTogMTVweDsiPgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogNTAlOyBkaXNwbGF5OiBmbGV4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEwMHB4OyI+5LuT5bqT5ZCN56ew77yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuPiR7dGhpcy5vdXREYXRhLndhcmVob3VzZU5hbWUgfHwgJyd9PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0id2lkdGg6IDUwJTsgZGlzcGxheTogZmxleDsiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWluLXdpZHRoOiAxMDBweDsiPuWHuuW6k+exu+Wei++8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8c3Bhbj4ke3RoaXMuZ2V0T3V0VHlwZU5hbWUodGhpcy5vdXREYXRhLm91dFR5cGUpIHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGZsZXg7IG1hcmdpbi1ib3R0b206IDE1cHg7Ij4KICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0id2lkdGg6IDUwJTsgZGlzcGxheTogZmxleDsiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWluLXdpZHRoOiAxMDBweDsiPuWItuWNleS6uu+8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8c3Bhbj4ke3RoaXMub3V0RGF0YS5jcmVhdGVCeU5hbWUgfHwgdGhpcy5vdXREYXRhLmNyZWF0ZUJ5IHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiA1MCU7IGRpc3BsYXk6IGZsZXg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7lrqHmoLjkurrvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4+JHt0aGlzLm91dERhdGEuYXVkaXRCeU5hbWUgfHwgdGhpcy5vdXREYXRhLmF1ZGl0QnkgfHwgJ+acquWuoeaguCd9PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiAxMDAlOyBkaXNwbGF5OiBmbGV4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEwMHB4OyI+5aSH5rOo77yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuPiR7dGhpcy5vdXREYXRhLnJlbWFyayB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ib3V0LWRldGFpbHMiPgogICAgICAgICAgICAgICAgPGgzPuWHuuW6k+eJqeWTgeS/oeaBrzwvaDM+CiAgICAgICAgICAgICAgICA8dGFibGU+CiAgICAgICAgICAgICAgICAgIDx0aGVhZD4KICAgICAgICAgICAgICAgICAgICA8dHI+CiAgICAgICAgICAgICAgICAgICAgICA8dGg+5bqP5Y+3PC90aD4KICAgICAgICAgICAgICAgICAgICAgIDx0aD7nianlk4HnvJbnoIE8L3RoPgogICAgICAgICAgICAgICAgICAgICAgPHRoPueJqeWTgeWQjeensDwvdGg+CiAgICAgICAgICAgICAgICAgICAgICA8dGg+5Ye65bqT5pWw6YePPC90aD4KICAgICAgICAgICAgICAgICAgICAgIDx0aD7ljZXku7c8L3RoPgogICAgICAgICAgICAgICAgICAgICAgPHRoPumHkeminTwvdGg+CiAgICAgICAgICAgICAgICAgICAgICA8dGg+5aSH5rOoPC90aD4KICAgICAgICAgICAgICAgICAgICA8L3RyPgogICAgICAgICAgICAgICAgICA8L3RoZWFkPgogICAgICAgICAgICAgICAgICA8dGJvZHk+CiAgICAgICAgICAgICAgICAgICAgJHtkZXRhaWxzSFRNTH0KICAgICAgICAgICAgICAgICAgPC90Ym9keT4KICAgICAgICAgICAgICAgIDwvdGFibGU+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ib3V0LWZvb3RlciI+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiAzMy4zMyU7IGRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBmbGV4LWVuZDsgaGVpZ2h0OiAzMHB4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEwMHB4OyI+5LuT5bqT566h55CG5ZGY77yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGlubGluZS1ibG9jazsgd2lkdGg6IDgwcHg7IGhlaWdodDogMXB4OyBiYWNrZ3JvdW5kLWNvbG9yOiAjMDAwOyBtYXJnaW4tbGVmdDogMTBweDsgcG9zaXRpb246IHJlbGF0aXZlOyBib3R0b206IDNweDsiPjwvZGl2PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0id2lkdGg6IDMzLjMzJTsgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGZsZXgtZW5kOyBoZWlnaHQ6IDMwcHg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7lrqHmoLjkurrnrb7lrZfvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogaW5saW5lLWJsb2NrOyB3aWR0aDogODBweDsgaGVpZ2h0OiAxcHg7IGJhY2tncm91bmQtY29sb3I6ICMwMDA7IG1hcmdpbi1sZWZ0OiAxMHB4OyBwb3NpdGlvbjogcmVsYXRpdmU7IGJvdHRvbTogM3B4OyI+PC9kaXY+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogMzMuMzMlOyBkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogZmxleC1lbmQ7IGhlaWdodDogMzBweDsiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWluLXdpZHRoOiAxMDBweDsiPuaXpeacn++8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBpbmxpbmUtYmxvY2s7IHdpZHRoOiA4MHB4OyBoZWlnaHQ6IDFweDsgYmFja2dyb3VuZC1jb2xvcjogIzAwMDsgbWFyZ2luLWxlZnQ6IDEwcHg7IHBvc2l0aW9uOiByZWxhdGl2ZTsgYm90dG9tOiAzcHg7Ij48L2Rpdj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2JvZHk+CiAgICAgICAgPC9odG1sPgogICAgICBgOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlueKtuaAgeWQjeensCAqLwogICAgZ2V0U3RhdHVzTmFtZShzdGF0dXMpIHsKICAgICAgLy8g5L2/55So5LiO5bGP5bmV6aKE6KeI55u45ZCM55qE5a2X5YW45pig5bCE5pa55byPCiAgICAgIGNvbnN0IHN0YXR1c0RpY3QgPSB0aGlzLmRpY3QudHlwZS5pbnZlbnRvcnlfb3V0X3N0YXR1cyB8fCBbXTsKICAgICAgY29uc3Qgc3RhdHVzSXRlbSA9IHN0YXR1c0RpY3QuZmluZChpdGVtID0+IGl0ZW0udmFsdWUgPT09IHN0YXR1cyk7CiAgICAgIHJldHVybiBzdGF0dXNJdGVtID8gc3RhdHVzSXRlbS5sYWJlbCA6ICcnOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPluWHuuW6k+exu+Wei+WQjeensCAqLwogICAgZ2V0T3V0VHlwZU5hbWUob3V0VHlwZSkgewogICAgICAvLyDkvb/nlKjkuI7lsY/luZXpooTop4jnm7jlkIznmoTlrZflhbjmmKDlsITmlrnlvI8KICAgICAgY29uc3QgdHlwZURpY3QgPSB0aGlzLmRpY3QudHlwZS5pbnZlbnRvcnlfb3V0X3R5cGUgfHwgW107CiAgICAgIGNvbnN0IHR5cGVJdGVtID0gdHlwZURpY3QuZmluZChpdGVtID0+IGl0ZW0udmFsdWUgPT09IG91dFR5cGUpOwogICAgICByZXR1cm4gdHlwZUl0ZW0gPyB0eXBlSXRlbS5sYWJlbCA6ICcnOwogICAgfSwKICAgIAogICAgLyoqIOWFs+mXrSAqLwogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgLy8g5Yqg6L295omT5Y2w6K6+572uCiAgICB0aGlzLmxvYWRQcmludFNldHRpbmdzKCk7CiAgfQp9Owo="}, {"version": 3, "sources": ["print.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8KA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "print.vue", "sourceRoot": "src/views/inventory/out", "sourcesContent": ["<template>\n  <div class=\"print-container\">\n    <!-- 打印预览界面 -->\n    <div class=\"print-header\" v-if=\"!isPrinting\">\n      <el-button type=\"primary\" icon=\"el-icon-printer\" size=\"small\" @click=\"handlePrint\">\n        打印出库单\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-setting\" size=\"small\" @click=\"handlePrintSettings\">\n        打印设置\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-close\" size=\"small\" @click=\"handleClose\">\n        关闭\n      </el-button>\n    </div>\n    \n    <!-- 打印设置对话框 -->\n    <el-dialog title=\"打印设置\" :visible.sync=\"printSettingsVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"printSettingsForm\" :model=\"printSettings\" label-width=\"100px\">\n        <el-form-item label=\"页面边距\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginTop\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginRight\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginBottom\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginLeft\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n          </el-row>\n          <div class=\"margin-labels\">\n            <span>上</span>\n            <span>右</span>\n            <span>下</span>\n            <span>左</span>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"字体大小\">\n          <el-slider v-model=\"printSettings.fontSize\" :min=\"8\" :max=\"20\" show-input></el-slider>\n        </el-form-item>\n        \n        <el-form-item label=\"页面方向\">\n          <el-radio-group v-model=\"printSettings.orientation\">\n            <el-radio label=\"portrait\">纵向</el-radio>\n            <el-radio label=\"landscape\">横向</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"纸张大小\">\n          <el-select v-model=\"printSettings.paperSize\" placeholder=\"请选择纸张大小\">\n            <el-option label=\"A4\" value=\"A4\"></el-option>\n            <el-option label=\"A5\" value=\"A5\"></el-option>\n            <el-option label=\"B5\" value=\"B5\"></el-option>\n            <el-option label=\"Letter\" value=\"letter\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"printSettingsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"savePrintSettings\">确 定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 打印内容 -->\n    <div class=\"print-content\" id=\"printContent\">\n      <div class=\"out-header\">\n        <h2 class=\"title\">出库单</h2>\n        <div class=\"header-info\">\n          <div class=\"header-item\">\n            <span class=\"label\">出库单号：</span>\n            <span class=\"value\">{{ outData.outCode }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">出库日期：</span>\n            <span class=\"value\">{{ parseTime(outData.outTime) }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">状态：</span>\n            <span class=\"value\">\n              <dict-tag :options=\"dict.type.inventory_out_status\" :value=\"outData.status\" />\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"out-info\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">仓库名称：</span>\n              <span class=\"value\">{{ outData.warehouseName }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">出库类型：</span>\n              <span class=\"value\">\n                <dict-tag :options=\"dict.type.inventory_out_type\" :value=\"outData.outType\" />\n              </span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">制单人：</span>\n              <span class=\"value\">{{ outData.createByName || outData.createBy }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">审核人：</span>\n              <span class=\"value\">{{ outData.auditByName || outData.auditBy || '未审核' }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <div class=\"info-item\">\n              <span class=\"label\">备注：</span>\n              <span class=\"value\">{{ outData.remark }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <div class=\"out-details\">\n        <h3>出库物品信息</h3>\n        <el-table :data=\"outData.details\" class=\"detail-table\" show-summary>\n          <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n          <el-table-column label=\"物品编码\" prop=\"productCode\" />\n          <el-table-column label=\"物品名称\" prop=\"productName\" />\n          <el-table-column label=\"出库数量\" prop=\"quantity\" />\n          <el-table-column label=\"单价\" prop=\"price\" :formatter=\"formatAmount\" />\n          <el-table-column label=\"金额\" prop=\"amount\" :formatter=\"formatAmount\" />\n          <el-table-column label=\"备注\" prop=\"remark\" />\n        </el-table>\n      </div>\n      \n      <div class=\"out-footer\">\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">仓库管理员：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">日期：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getInventoryOut } from \"@/api/inventory/out\";\nimport { getBatchUserRealNames } from \"@/utils/userUtils\";\n\nexport default {\n  name: \"OutPrint\",\n  dicts: ['inventory_out_type', 'inventory_out_status'],\n  data() {\n    return {\n      loading: false,\n      isPrinting: false,\n      printSettingsVisible: false,\n      outData: {\n        outCode: \"\",\n        warehouseName: \"\",\n        outTime: null,\n        outType: \"\",\n        status: \"\",\n        createBy: \"\",\n        createByName: \"\",\n        auditBy: \"\",\n        auditByName: \"\",\n        remark: \"\",\n        details: []\n      },\n      outId: null,\n      printSettings: {\n        marginTop: 15,\n        marginRight: 15,\n        marginBottom: 15,\n        marginLeft: 15,\n        fontSize: 12,\n        orientation: 'portrait',\n        paperSize: 'A4'\n      }\n    };\n  },\n  created() {\n    this.outId = this.$route.params.outId || this.$route.params.id || this.$route.query.id;\n    console.log('出库单打印页面初始化，出库单ID:', this.outId);\n    if (!this.outId) {\n      this.$message.error('缺少出库单ID参数');\n      return;\n    }\n    this.getOutData();\n  },\n  methods: {\n    /** 获取出库单信息 */\n    getOutData() {\n      this.loading = true;\n      getInventoryOut(this.outId).then(response => {\n        this.outData = response.data;\n        \n        // 获取用户真实姓名\n        const userNames = [\n          this.outData.createBy,\n          this.outData.auditBy\n        ].filter(name => name); // 过滤空值\n\n        if (userNames.length > 0) {\n          getBatchUserRealNames(userNames).then(nameMap => {\n            // 更新用户真实姓名\n            if (this.outData.createBy && nameMap[this.outData.createBy]) {\n              this.outData.createByName = nameMap[this.outData.createBy];\n            }\n            if (this.outData.auditBy && nameMap[this.outData.auditBy]) {\n              this.outData.auditByName = nameMap[this.outData.auditBy];\n            }\n          });\n        }\n        \n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n        this.$message.error(\"获取出库单信息失败\");\n      });\n    },\n    \n    /** 处理打印设置 */\n    handlePrintSettings() {\n      this.printSettingsVisible = true;\n    },\n    \n    /** 保存打印设置 */\n    savePrintSettings() {\n      // 保存到本地存储\n      localStorage.setItem('outPrintSettings', JSON.stringify(this.printSettings));\n      this.printSettingsVisible = false;\n      this.$message.success('打印设置已保存');\n    },\n    \n    /** 加载打印设置 */\n    loadPrintSettings() {\n      const savedSettings = localStorage.getItem('outPrintSettings');\n      if (savedSettings) {\n        this.printSettings = JSON.parse(savedSettings);\n      }\n    },\n    \n    /** 格式化金额 */\n    formatAmount(row, column, cellValue) {\n      if (!cellValue) return '0.00';\n      return parseFloat(cellValue).toFixed(2);\n    },\n    \n    /** 打印 */\n    handlePrint() {\n      this.isPrinting = true;\n      \n      // 确保数据已加载\n      if (!this.outData.outCode) {\n        this.$message.warning('数据还在加载中，请稍后再试');\n        this.isPrinting = false;\n        return;\n      }\n      \n      this.$nextTick(() => {\n        // 创建新窗口进行打印\n        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');\n        \n        // 生成打印内容\n        const printContent = this.generatePrintHTML();\n        \n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        \n        // 等待内容加载完成后打印\n        printWindow.onload = () => {\n          setTimeout(() => {\n            printWindow.print();\n            // 打印完成后不自动关闭窗口，让用户手动关闭\n            this.isPrinting = false;\n          }, 500);\n        };\n      });\n    },\n    \n    /** 生成打印页面HTML */\n    generatePrintHTML() {\n      const details = this.outData.details || [];\n      let detailsHTML = '';\n      \n      details.forEach((item, index) => {\n        detailsHTML += `\n          <tr>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${index + 1}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productCode || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productName || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.quantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${this.formatAmount(null, null, item.price)}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${this.formatAmount(null, null, item.amount)}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.remark || ''}</td>\n          </tr>\n        `;\n      });\n      \n      // 根据设置确定页面方向和纸张大小\n      let pageSizeStyle = '';\n      if (this.printSettings.orientation === 'landscape') {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;\n      } else {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;\n      }\n      \n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>出库单打印</title>\n          <style>\n            @page { \n              ${pageSizeStyle}\n              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; \n            }\n            \n            * {\n              box-sizing: border-box;\n            }\n            \n            html, body { \n              font-family: \"Microsoft YaHei\", SimSun, sans-serif; \n              font-size: ${this.printSettings.fontSize}pt; \n              color: #000; \n              background: #fff; \n              margin: 0; \n              padding: ${this.printSettings.marginTop/3}mm;\n              width: 100%;\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n            \n            .container {\n              width: 100%;\n              max-width: 1000px;\n              margin: 0 auto;\n            }\n            \n            .print-container {\n              padding: 20px;\n              background-color: #fff;\n              color: #000;\n            }\n            \n            .print-content {\n              max-width: 1000px;\n              margin: 0 auto;\n              font-family: \"SimSun\", \"宋体\", serif;\n              color: #000;\n            }\n            \n            .out-header {\n              text-align: center;\n              margin-bottom: 30px;\n              border-bottom: 2px solid #000;\n              padding-bottom: 10px;\n            }\n            \n            .out-header .title {\n              font-size: ${this.printSettings.fontSize + 8}pt;\n              font-weight: bold;\n              margin: 0 0 20px 0;\n            }\n            \n            .header-info {\n              display: flex;\n              justify-content: space-between;\n            }\n            \n            .header-item {\n              display: flex;\n              align-items: center;\n            }\n            \n            .header-item .label {\n              font-weight: bold;\n              margin-right: 5px;\n            }\n            \n            .out-info {\n              margin-bottom: 30px;\n            }\n            \n            .info-item {\n              margin-bottom: 15px;\n              display: flex;\n              align-items: flex-start;\n            }\n            \n            .info-item .label {\n              font-weight: bold;\n              min-width: 100px;\n              flex-shrink: 0;\n            }\n            \n            .out-details h3,\n            .approval-info h3 {\n              font-size: ${this.printSettings.fontSize + 4}pt;\n              margin: 0 0 15px 0;\n              border-left: 4px solid #409EFF;\n              padding-left: 10px;\n            }\n            \n            table {\n              width: 100%;\n              border-collapse: collapse;\n              margin-bottom: 30px;\n              table-layout: fixed;\n            }\n            \n            th {\n              background-color: #f5f5f5;\n              color: #000;\n              font-weight: bold;\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n            }\n            \n            td {\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n            \n            .out-footer {\n              margin: 30px 0;\n            }\n            \n            .footer-item {\n              margin-bottom: 15px;\n            }\n            \n            .signature-line {\n              display: inline-block;\n              width: 80px;\n              height: 1px;\n              background-color: #000;\n              margin-left: 10px;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"print-container\">\n            <div class=\"print-content\">\n              <div class=\"out-header\">\n                <h2 class=\"title\">出库单</h2>\n                <div class=\"header-info\">\n                  <div class=\"header-item\">\n                    <span class=\"label\">出库单号：</span>\n                    <span class=\"value\">${this.outData.outCode || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">出库日期：</span>\n                    <span class=\"value\">${this.parseTime(this.outData.outTime) || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">状态：</span>\n                    <span class=\"value\">${this.getStatusName(this.outData.status) || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"out-info\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库名称：</span>\n                    <span>${this.outData.warehouseName || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">出库类型：</span>\n                    <span>${this.getOutTypeName(this.outData.outType) || ''}</span>\n                  </div>\n                </div>\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">制单人：</span>\n                    <span>${this.outData.createByName || this.outData.createBy || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人：</span>\n                    <span>${this.outData.auditByName || this.outData.auditBy || '未审核'}</span>\n                  </div>\n                </div>\n                \n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 100%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">备注：</span>\n                    <span>${this.outData.remark || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"out-details\">\n                <h3>出库物品信息</h3>\n                <table>\n                  <thead>\n                    <tr>\n                      <th>序号</th>\n                      <th>物品编码</th>\n                      <th>物品名称</th>\n                      <th>出库数量</th>\n                      <th>单价</th>\n                      <th>金额</th>\n                      <th>备注</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    ${detailsHTML}\n                  </tbody>\n                </table>\n              </div>\n              \n              <div class=\"out-footer\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库管理员：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">日期：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    \n    /** 获取状态名称 */\n    getStatusName(status) {\n      // 使用与屏幕预览相同的字典映射方式\n      const statusDict = this.dict.type.inventory_out_status || [];\n      const statusItem = statusDict.find(item => item.value === status);\n      return statusItem ? statusItem.label : '';\n    },\n    \n    /** 获取出库类型名称 */\n    getOutTypeName(outType) {\n      // 使用与屏幕预览相同的字典映射方式\n      const typeDict = this.dict.type.inventory_out_type || [];\n      const typeItem = typeDict.find(item => item.value === outType);\n      return typeItem ? typeItem.label : '';\n    },\n    \n    /** 关闭 */\n    handleClose() {\n      this.$router.go(-1);\n    }\n  },\n  mounted() {\n    // 加载打印设置\n    this.loadPrintSettings();\n  }\n};\n</script>\n\n<style scoped>\n.print-container {\n  padding: 20px;\n  background-color: #fff;\n  color: #000;\n}\n\n.print-header {\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.print-content {\n  max-width: 1000px;\n  margin: 0 auto;\n  font-family: \"SimSun\", \"宋体\", serif;\n  color: #000;\n}\n\n.out-header {\n  text-align: center;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #000;\n  padding-bottom: 10px;\n}\n\n.out-header .title {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0 0 20px 0;\n}\n\n.header-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.header-item {\n  display: flex;\n  align-items: center;\n}\n\n.header-item .label {\n  font-weight: bold;\n}\n\n.out-info {\n  margin-bottom: 30px;\n}\n\n.info-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.info-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.detail-table {\n  margin-bottom: 30px;\n}\n\n.detail-table ::v-deep .el-table__header th {\n  background-color: #f5f5f5;\n  color: #000;\n}\n\n.detail-table ::v-deep .el-table__row td {\n  color: #000;\n}\n\n.out-details h3,\n.approval-info h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n  border-left: 4px solid #409EFF;\n  padding-left: 10px;\n}\n\n.out-footer {\n  margin: 30px 0;\n}\n\n.footer-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-end;\n  height: 30px;\n}\n\n.footer-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.signature-line {\n  display: inline-block;\n  width: 80px;\n  height: 1px;\n  background-color: #000;\n  margin-left: 5px;\n  position: relative;\n  bottom: 3px;\n}\n\n.margin-labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n</style>"]}]}