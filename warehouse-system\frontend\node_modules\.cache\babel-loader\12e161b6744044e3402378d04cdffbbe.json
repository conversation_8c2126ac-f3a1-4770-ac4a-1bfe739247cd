{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\log\\system\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\log\\system\\index.vue", "mtime": 1756537793460}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\babel.config.js", "mtime": 1747299429443}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_LogManagement", "_interopRequireDefault", "require", "_operlog", "name", "components", "LogManagement", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "systemLogList", "title", "open", "date<PERSON><PERSON><PERSON>", "form", "queryParams", "pageNum", "pageSize", "logType", "moduleName", "undefined", "createBy", "operationType", "operationStatus", "statisticsData", "trendData", "trendPeriod", "chartConfig", "tooltip", "trigger", "legend", "xAxis", "type", "yAxis", "yAxisName", "series", "dataKey", "stack", "areaStyle", "startColor", "endColor", "smooth", "quickFilters", "key", "label", "icon", "mainActions", "permission", "batchActions", "extraActions", "pickerOptions", "shortcuts", "text", "onClick", "picker", "end", "Date", "start", "setTime", "getTime", "$emit", "created", "initData", "methods", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "_t", "w", "_context", "p", "n", "Promise", "all", "getStatistics", "getTrendData", "getList", "v", "console", "error", "a", "_this2", "params", "addDateRange", "_objectSpread2", "log", "listOperLog", "then", "response", "mappedData", "rows", "map", "item", "logId", "operId", "businessType", "mapOperationType", "operationName", "method", "operator", "operName", "status", "costTime", "operationTime", "operTime", "operatorIp", "operIp", "operatorLocation", "operLocation", "requestMethod", "requestUrl", "operUrl", "requestParam", "operParam", "jsonResult", "errorMsg", "catch", "$message", "typeMap", "value", "length", "color", "resolve", "mockData", "today", "i", "date", "setDate", "getDate", "dateStr", "toISOString", "substring", "push", "systemLogs", "Math", "floor", "random", "businessLogs", "dataChangeLogs", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleRefresh", "handleSelectionChange", "selection", "handleView", "row", "handleDelete", "_this3", "logIds", "$modal", "confirm", "delOperLog", "join", "msgSuccess", "handleClean", "_this4", "cleanOperLog", "msgError", "handleExport", "_this5", "exportOperLog", "downloadFile", "concat", "fileName", "blob", "Blob", "link", "document", "createElement", "href", "window", "URL", "createObjectURL", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleQuickFilter", "<PERSON><PERSON><PERSON>", "startOfWeek", "getFullYear", "getMonth", "getDay", "startOfMonth", "parseTime", "handleMainAction", "action<PERSON>ey", "handleBatchAction", "handlePeriodChange", "period", "getLogTypeName", "nameMap", "getLogTypeTagType", "getOperationTypeName", "getOperationTypeTagType"], "sources": ["src/views/log/system/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计信息卡片 -->\r\n    <LogManagement\r\n      :statistics-data=\"statisticsData\"\r\n      :show-trend=\"true\"\r\n      trend-title=\"系统操作趋势\"\r\n      :trend-data=\"trendData\"\r\n      :trend-period=\"trendPeriod\"\r\n      :chart-config=\"chartConfig\"\r\n      :quick-filters=\"quickFilters\"\r\n      :main-actions=\"mainActions\"\r\n      :batch-actions=\"batchActions\"\r\n      :extra-actions=\"extraActions\"\r\n      :show-search.sync=\"showSearch\"\r\n      @period-change=\"handlePeriodChange\"\r\n      @quick-filter=\"handleQuickFilter\"\r\n      @main-action=\"handleMainAction\"\r\n      @batch-action=\"handleBatchAction\"\r\n      @refresh=\"handleRefresh\"\r\n    />\r\n\r\n    <!-- 高级搜索表单 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-form-item label=\"日志类型\" prop=\"logType\">\r\n        <el-select v-model=\"queryParams.logType\" placeholder=\"请选择日志类型\" clearable>\r\n          <el-option label=\"系统操作\" value=\"SYSTEM\" />\r\n          <el-option label=\"业务操作\" value=\"BUSINESS\" />\r\n          <el-option label=\"数据变更\" value=\"DATA\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作模块\" prop=\"moduleName\">\r\n        <el-input\r\n          v-model=\"queryParams.moduleName\"\r\n          placeholder=\"请输入操作模块\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"操作人员\" prop=\"operator\">\r\n        <el-input\r\n          v-model=\"queryParams.operator\"\r\n          placeholder=\"请输入操作人员\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"操作类型\" prop=\"operationType\">\r\n        <el-select v-model=\"queryParams.operationType\" placeholder=\"操作类型\" clearable>\r\n          <el-option label=\"新增\" value=\"INSERT\" />\r\n          <el-option label=\"修改\" value=\"UPDATE\" />\r\n          <el-option label=\"删除\" value=\"DELETE\" />\r\n          <el-option label=\"查询\" value=\"SELECT\" />\r\n          <el-option label=\"导出\" value=\"EXPORT\" />\r\n          <el-option label=\"导入\" value=\"IMPORT\" />\r\n          <el-option label=\"授权\" value=\"GRANT\" />\r\n          <el-option label=\"其他\" value=\"OTHER\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作状态\" prop=\"operationStatus\">\r\n        <el-select v-model=\"queryParams.operationStatus\" placeholder=\"操作状态\" clearable>\r\n          <el-option label=\"成功\" value=\"0\" />\r\n          <el-option label=\"失败\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          :picker-options=\"pickerOptions\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['log:operation:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          @click=\"handleClean\"\r\n          v-hasPermi=\"['log:operation:remove']\"\r\n        >清空</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['log:operation:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 数据表格 -->\r\n    <el-table v-loading=\"loading\" :data=\"systemLogList\" @selection-change=\"handleSelectionChange\" empty-text=\"暂无数据\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"日志编号\" align=\"center\" prop=\"logId\" width=\"80\" />\r\n      <el-table-column label=\"日志类型\" align=\"center\" prop=\"logType\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getLogTypeTagType(scope.row.logType)\">\r\n            {{ getLogTypeName(scope.row.logType) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作模块\" align=\"center\" prop=\"moduleName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationType\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getOperationTypeTagType(scope.row.operationType)\">\r\n            {{ getOperationTypeName(scope.row.operationType) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作名称\" align=\"center\" prop=\"operationName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作人员\" align=\"center\" prop=\"operator\" width=\"120\" />\r\n      <el-table-column label=\"操作状态\" align=\"center\" prop=\"operationStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.operationStatus === '0' ? 'success' : 'danger'\">\r\n            {{ scope.row.operationStatus === '0' ? '成功' : '失败' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"耗时(ms)\" align=\"center\" prop=\"costTime\" width=\"100\" />\r\n      <el-table-column label=\"操作时间\" align=\"center\" prop=\"operationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.operationTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['log:system:detail']\"\r\n          >详细</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 系统日志详细 -->\r\n    <el-dialog title=\"系统日志详细\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\" size=\"mini\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"日志类型：\">\r\n              <el-tag :type=\"getLogTypeTagType(form.logType)\">\r\n                {{ getLogTypeName(form.logType) }}\r\n              </el-tag>\r\n            </el-form-item>\r\n            <el-form-item label=\"操作模块：\">{{ form.moduleName }}</el-form-item>\r\n            <el-form-item label=\"操作类型：\">\r\n              <el-tag :type=\"getOperationTypeTagType(form.operationType)\">\r\n                {{ getOperationTypeName(form.operationType) }}\r\n              </el-tag>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作人员：\">{{ form.createBy }}</el-form-item>\r\n            <el-form-item label=\"操作IP：\">{{ form.operatorIp }}</el-form-item>\r\n            <el-form-item label=\"操作时间：\">{{ parseTime(form.createTime) }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"操作名称：\">{{ form.operationName }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"请求地址：\">{{ form.requestUrl }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"请求方式：\">{{ form.requestMethod }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"耗时：\">{{ form.costTime }}ms</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"请求参数：\">\r\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.requestParams\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"返回结果：\">\r\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.responseResult\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作状态：\">\r\n              <el-tag :type=\"form.operationStatus === '0' ? 'success' : 'danger'\">\r\n                {{ form.operationStatus === '0' ? '成功' : '失败' }}\r\n              </el-tag>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" v-if=\"form.operationStatus === '1'\">\r\n            <el-form-item label=\"错误信息：\">\r\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.errorMsg\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"open = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport LogManagement from \"@/components/LogManagement\";\r\nimport { listOperLog, delOperLog, cleanOperLog, exportOperLog } from \"@/api/monitor/operlog\";\r\n\r\nexport default {\r\n  name: \"SystemLog\",\r\n  components: {\r\n    LogManagement\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      systemLogList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        logType: \"SYSTEM\",  // 默认查询系统日志\r\n        moduleName: undefined,\r\n        createBy: undefined,\r\n        operationType: undefined,\r\n        operationStatus: undefined\r\n      },\r\n      // 统计数据\r\n      statisticsData: [],\r\n      // 趋势数据\r\n      trendData: [],\r\n      trendPeriod: '7d',\r\n      // 图表配置\r\n      chartConfig: {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          data: ['系统操作', '业务操作', '数据变更']\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: []\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        yAxisName: '操作数量',\r\n        series: [\r\n          {\r\n            name: '系统操作',\r\n            type: 'line',\r\n            dataKey: 'systemLogs',\r\n            stack: '总量',\r\n            areaStyle: {\r\n              startColor: '#80FFA5',\r\n              endColor: '#008F52'\r\n            },\r\n            smooth: true\r\n          },\r\n          {\r\n            name: '业务操作',\r\n            type: 'line',\r\n            dataKey: 'businessLogs',\r\n            stack: '总量',\r\n            areaStyle: {\r\n              startColor: '#FFA0A0',\r\n              endColor: '#8F0000'\r\n            },\r\n            smooth: true\r\n          },\r\n          {\r\n            name: '数据变更',\r\n            type: 'line',\r\n            dataKey: 'dataChangeLogs',\r\n            stack: '总量',\r\n            areaStyle: {\r\n              startColor: '#A0A0FF',\r\n              endColor: '#00008F'\r\n            },\r\n            smooth: true\r\n          }\r\n        ]\r\n      },\r\n      // 快速筛选\r\n      quickFilters: [\r\n        { key: 'today', label: '今日', icon: 'el-icon-date' },\r\n        { key: 'week', label: '本周', icon: 'el-icon-date' },\r\n        { key: 'month', label: '本月', icon: 'el-icon-date' },\r\n        { key: 'system', label: '系统操作', icon: 'el-icon-setting' },\r\n        { key: 'business', label: '业务操作', icon: 'el-icon-s-order' },\r\n        { key: 'data', label: '数据操作', icon: 'el-icon-s-data' }\r\n      ],\r\n      // 主要操作\r\n      mainActions: [\r\n        {\r\n          key: 'export',\r\n          label: '导出Excel',\r\n          type: 'warning',\r\n          icon: 'el-icon-download',\r\n          permission: 'log:operation:export'\r\n        }\r\n      ],\r\n      // 批量操作\r\n      batchActions: [\r\n        {\r\n          key: 'batchDelete',\r\n          label: '批量删除',\r\n          icon: 'el-icon-delete',\r\n          permission: 'log:operation:remove'\r\n        }\r\n      ],\r\n      // 额外操作\r\n      extraActions: [\r\n        {\r\n          key: 'clean',\r\n          label: '清空日志',\r\n          type: 'danger',\r\n          icon: 'el-icon-delete',\r\n          permission: 'log:operation:remove'\r\n        }\r\n      ],\r\n      // 日期选择器配置\r\n      pickerOptions: {\r\n        shortcuts: [{\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近三个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    /** 初始化数据 */\r\n    async initData() {\r\n      try {\r\n        await Promise.all([\r\n          this.getStatistics(),\r\n          this.getTrendData()\r\n        ]);\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('初始化数据失败:', error);\r\n      }\r\n    },\r\n\r\n    /** 查询系统日志 */\r\n    getList() {\r\n      this.loading = true;\r\n      const params = this.addDateRange({...this.queryParams}, this.dateRange);\r\n      console.log('实际请求参数:', params); // 打印查询参数\r\n\r\n      // 使用操作日志API获取真实数据\r\n      listOperLog(params).then(response => {\r\n        console.log('API响应数据:', response); // 打印完整响应\r\n        // 映射字段名称\r\n        const mappedData = (response.rows || []).map(item => ({\r\n          logId: item.operId,\r\n          logType: item.businessType === 0 ? 'SYSTEM' : 'BUSINESS',\r\n          moduleName: item.title,\r\n          operationType: this.mapOperationType(item.businessType),\r\n          operationName: item.method,\r\n          operator: item.operName,\r\n          operationStatus: item.status,\r\n          costTime: item.costTime,\r\n          operationTime: item.operTime,\r\n          operatorIp: item.operIp,\r\n          operatorLocation: item.operLocation,\r\n          requestMethod: item.requestMethod,\r\n          requestUrl: item.operUrl,\r\n          requestParam: item.operParam,\r\n          jsonResult: item.jsonResult,\r\n          errorMsg: item.errorMsg\r\n        }));\r\n\r\n        this.systemLogList = mappedData;\r\n        this.total = response.total || 0;\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('API请求失败:', error); // 打印错误详情\r\n        this.loading = false;\r\n        this.$message.error('查询系统日志失败，请稍后重试');\r\n      });\r\n    },\r\n\r\n    /** 映射操作类型 */\r\n    mapOperationType(businessType) {\r\n      const typeMap = {\r\n        0: 'OTHER',\r\n        1: 'INSERT',\r\n        2: 'UPDATE',\r\n        3: 'DELETE',\r\n        4: 'GRANT',\r\n        5: 'EXPORT',\r\n        6: 'IMPORT',\r\n        7: 'FORCE',\r\n        8: 'GENCODE',\r\n        9: 'CLEAN'\r\n      };\r\n      return typeMap[businessType] || 'OTHER';\r\n    },\r\n\r\n    /** 获取统计数据 */\r\n    getStatistics() {\r\n      // 使用模拟数据，因为操作日志API没有统计接口\r\n      this.statisticsData = [\r\n        { title: '总日志数', value: this.systemLogList.length, icon: 'el-icon-s-data', color: '#409EFF' },\r\n        { title: '今日日志', value: 0, icon: 'el-icon-date', color: '#67C23A' },\r\n        { title: '系统操作', value: 0, icon: 'el-icon-setting', color: '#E6A23C' },\r\n        { title: '业务操作', value: 0, icon: 'el-icon-s-order', color: '#F56C6C' }\r\n      ];\r\n      return Promise.resolve();\r\n    },\r\n\r\n    /** 获取趋势数据 */\r\n    getTrendData() {\r\n      // 使用模拟数据，因为操作日志API没有趋势接口\r\n      const mockData = [];\r\n      const today = new Date();\r\n\r\n      for (let i = 6; i >= 0; i--) {\r\n        const date = new Date(today);\r\n        date.setDate(date.getDate() - i);\r\n        const dateStr = date.toISOString().substring(0, 10);\r\n\r\n        mockData.push({\r\n          date: dateStr,\r\n          systemLogs: Math.floor(Math.random() * 50) + 10,\r\n          businessLogs: Math.floor(Math.random() * 30) + 5,\r\n          dataChangeLogs: Math.floor(Math.random() * 20) + 2\r\n        });\r\n      }\r\n\r\n      this.trendData = mockData;\r\n      return Promise.resolve();\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 刷新数据 */\r\n    handleRefresh() {\r\n      this.getList();\r\n      this.getStatistics();\r\n      this.getTrendData();\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.operId || item.logId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true;\r\n      this.form = { ...row };\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const logIds = row ? [row.logId] : this.ids;\r\n      this.$modal.confirm('是否确认删除选中的系统日志数据项？').then(() => {\r\n        // 使用操作日志删除API\r\n        delOperLog(logIds.join(',')).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 清空按钮操作 */\r\n    handleClean() {\r\n      this.$modal.confirm('是否确认清空所有系统日志数据项？此操作不可恢复！').then(() => {\r\n        // 使用操作日志清空API\r\n        cleanOperLog().then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"清空成功\");\r\n        }).catch(error => {\r\n          console.error('清空系统日志失败:', error);\r\n          this.$modal.msgError(\"清空失败，请稍后重试\");\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出当前筛选条件下的系统日志数据？').then(() => {\r\n        const params = this.addDateRange({...this.queryParams}, this.dateRange);\r\n        delete params.pageNum;\r\n        delete params.pageSize;\r\n\r\n        exportOperLog(params).then(response => {\r\n          this.downloadFile(response, `系统日志_${new Date().getTime()}.xlsx`);\r\n          this.$modal.msgSuccess(\"导出成功\");\r\n        }).catch(error => {\r\n          console.error('导出系统日志失败:', error);\r\n          this.$modal.msgError(\"导出失败，请稍后重试\");\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 下载文件 */\r\n    downloadFile(data, fileName) {\r\n      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\r\n      const link = document.createElement('a');\r\n      link.href = window.URL.createObjectURL(blob);\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(link.href);\r\n    },\r\n\r\n    /** 快速筛选处理 */\r\n    handleQuickFilter(filterKey) {\r\n      const today = new Date();\r\n      const startOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());\r\n      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\r\n      \r\n      switch (filterKey) {\r\n        case 'today':\r\n          this.dateRange = [\r\n            this.parseTime(today, '{y}-{m}-{d}'),\r\n            this.parseTime(today, '{y}-{m}-{d}')\r\n          ];\r\n          break;\r\n        case 'week':\r\n          this.dateRange = [\r\n            this.parseTime(startOfWeek, '{y}-{m}-{d}'),\r\n            this.parseTime(today, '{y}-{m}-{d}')\r\n          ];\r\n          break;\r\n        case 'month':\r\n          this.dateRange = [\r\n            this.parseTime(startOfMonth, '{y}-{m}-{d}'),\r\n            this.parseTime(today, '{y}-{m}-{d}')\r\n          ];\r\n          break;\r\n        case 'system':\r\n          this.queryParams.logType = 'SYSTEM';\r\n          break;\r\n        case 'business':\r\n          this.queryParams.logType = 'BUSINESS';\r\n          break;\r\n        case 'data':\r\n          this.queryParams.logType = 'DATA';\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 主要操作处理 */\r\n    handleMainAction(actionKey) {\r\n      switch (actionKey) {\r\n        case 'export':\r\n          this.handleExport();\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 批量操作处理 */\r\n    handleBatchAction(actionKey) {\r\n      switch (actionKey) {\r\n        case 'batchDelete':\r\n          this.handleDelete();\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 周期变化处理 */\r\n    handlePeriodChange(period) {\r\n      this.trendPeriod = period;\r\n      this.getTrendData();\r\n    },\r\n\r\n    /** 获取日志类型名称 */\r\n    getLogTypeName(type) {\r\n      const nameMap = {\r\n        'SYSTEM': '系统操作',\r\n        'BUSINESS': '业务操作',\r\n        'DATA': '数据变更'\r\n      };\r\n      return nameMap[type] || type;\r\n    },\r\n\r\n    /** 获取日志类型标签类型 */\r\n    getLogTypeTagType(type) {\r\n      const typeMap = {\r\n        'SYSTEM': 'primary',\r\n        'BUSINESS': 'success',\r\n        'DATA': 'warning'\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    /** 获取操作类型名称 */\r\n    getOperationTypeName(type) {\r\n      const nameMap = {\r\n        'INSERT': '新增',\r\n        'UPDATE': '修改',\r\n        'DELETE': '删除',\r\n        'SELECT': '查询',\r\n        'EXPORT': '导出',\r\n        'IMPORT': '导入',\r\n        'GRANT': '授权',\r\n        'OTHER': '其他'\r\n      };\r\n      return nameMap[type] || type;\r\n    },\r\n\r\n    /** 获取操作类型标签类型 */\r\n    getOperationTypeTagType(type) {\r\n      const typeMap = {\r\n        'INSERT': 'success',\r\n        'UPDATE': 'warning',\r\n        'DELETE': 'danger',\r\n        'SELECT': 'primary',\r\n        'EXPORT': 'info',\r\n        'IMPORT': 'info',\r\n        'GRANT': 'warning',\r\n        'OTHER': 'info'\r\n      };\r\n      return typeMap[type] || 'info';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAiPA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,aAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QAAA;QACAC,UAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,aAAA,EAAAF,SAAA;QACAG,eAAA,EAAAH;MACA;MACA;MACAI,cAAA;MACA;MACAC,SAAA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;UACAC,OAAA;QACA;QACAC,MAAA;UACA3B,IAAA;QACA;QACA4B,KAAA;UACAC,IAAA;UACA7B,IAAA;QACA;QACA8B,KAAA;UACAD,IAAA;QACA;QACAE,SAAA;QACAC,MAAA,GACA;UACAnC,IAAA;UACAgC,IAAA;UACAI,OAAA;UACAC,KAAA;UACAC,SAAA;YACAC,UAAA;YACAC,QAAA;UACA;UACAC,MAAA;QACA,GACA;UACAzC,IAAA;UACAgC,IAAA;UACAI,OAAA;UACAC,KAAA;UACAC,SAAA;YACAC,UAAA;YACAC,QAAA;UACA;UACAC,MAAA;QACA,GACA;UACAzC,IAAA;UACAgC,IAAA;UACAI,OAAA;UACAC,KAAA;UACAC,SAAA;YACAC,UAAA;YACAC,QAAA;UACA;UACAC,MAAA;QACA;MAEA;MACA;MACAC,YAAA,GACA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,EACA;MACA;MACAC,WAAA,GACA;QACAH,GAAA;QACAC,KAAA;QACAZ,IAAA;QACAa,IAAA;QACAE,UAAA;MACA,EACA;MACA;MACAC,YAAA,GACA;QACAL,GAAA;QACAC,KAAA;QACAC,IAAA;QACAE,UAAA;MACA,EACA;MACA;MACAE,YAAA,GACA;QACAN,GAAA;QACAC,KAAA;QACAZ,IAAA;QACAa,IAAA;QACAE,UAAA;MACA,EACA;MACA;MACAG,aAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,GAAA,OAAAC,IAAA;YACA,IAAAC,KAAA,OAAAD,IAAA;YACAC,KAAA,CAAAC,OAAA,CAAAD,KAAA,CAAAE,OAAA;YACAL,MAAA,CAAAM,KAAA,UAAAH,KAAA,EAAAF,GAAA;UACA;QACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,GAAA,OAAAC,IAAA;YACA,IAAAC,KAAA,OAAAD,IAAA;YACAC,KAAA,CAAAC,OAAA,CAAAD,KAAA,CAAAE,OAAA;YACAL,MAAA,CAAAM,KAAA,UAAAH,KAAA,EAAAF,GAAA;UACA;QACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,GAAA,OAAAC,IAAA;YACA,IAAAC,KAAA,OAAAD,IAAA;YACAC,KAAA,CAAAC,OAAA,CAAAD,KAAA,CAAAE,OAAA;YACAL,MAAA,CAAAM,KAAA,UAAAH,KAAA,EAAAF,GAAA;UACA;QACA;MACA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACA,YACAD,QAAA,WAAAA,SAAA;MAAA,IAAAE,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,EAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAEAC,OAAA,CAAAC,GAAA,EACAZ,KAAA,CAAAa,aAAA,IACAb,KAAA,CAAAc,YAAA,GACA;YAAA;cACAd,KAAA,CAAAe,OAAA;cAAAP,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAQ,CAAA;cAEAC,OAAA,CAAAC,KAAA,aAAAZ,EAAA;YAAA;cAAA,OAAAE,QAAA,CAAAW,CAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IAEA;IAEA,aACAU,OAAA,WAAAA,QAAA;MAAA,IAAAK,MAAA;MACA,KAAAhF,OAAA;MACA,IAAAiF,MAAA,QAAAC,YAAA,KAAAC,cAAA,CAAArB,OAAA,WAAAnD,WAAA,QAAAF,SAAA;MACAoE,OAAA,CAAAO,GAAA,YAAAH,MAAA;;MAEA;MACA,IAAAI,oBAAA,EAAAJ,MAAA,EAAAK,IAAA,WAAAC,QAAA;QACAV,OAAA,CAAAO,GAAA,aAAAG,QAAA;QACA;QACA,IAAAC,UAAA,IAAAD,QAAA,CAAAE,IAAA,QAAAC,GAAA,WAAAC,IAAA;UAAA;YACAC,KAAA,EAAAD,IAAA,CAAAE,MAAA;YACA/E,OAAA,EAAA6E,IAAA,CAAAG,YAAA;YACA/E,UAAA,EAAA4E,IAAA,CAAApF,KAAA;YACAW,aAAA,EAAA8D,MAAA,CAAAe,gBAAA,CAAAJ,IAAA,CAAAG,YAAA;YACAE,aAAA,EAAAL,IAAA,CAAAM,MAAA;YACAC,QAAA,EAAAP,IAAA,CAAAQ,QAAA;YACAhF,eAAA,EAAAwE,IAAA,CAAAS,MAAA;YACAC,QAAA,EAAAV,IAAA,CAAAU,QAAA;YACAC,aAAA,EAAAX,IAAA,CAAAY,QAAA;YACAC,UAAA,EAAAb,IAAA,CAAAc,MAAA;YACAC,gBAAA,EAAAf,IAAA,CAAAgB,YAAA;YACAC,aAAA,EAAAjB,IAAA,CAAAiB,aAAA;YACAC,UAAA,EAAAlB,IAAA,CAAAmB,OAAA;YACAC,YAAA,EAAApB,IAAA,CAAAqB,SAAA;YACAC,UAAA,EAAAtB,IAAA,CAAAsB,UAAA;YACAC,QAAA,EAAAvB,IAAA,CAAAuB;UACA;QAAA;QAEAlC,MAAA,CAAA1E,aAAA,GAAAkF,UAAA;QACAR,MAAA,CAAA3E,KAAA,GAAAkF,QAAA,CAAAlF,KAAA;QACA2E,MAAA,CAAAhF,OAAA;MACA,GAAAmH,KAAA,WAAArC,KAAA;QACAD,OAAA,CAAAC,KAAA,aAAAA,KAAA;QACAE,MAAA,CAAAhF,OAAA;QACAgF,MAAA,CAAAoC,QAAA,CAAAtC,KAAA;MACA;IACA;IAEA,aACAiB,gBAAA,WAAAA,iBAAAD,YAAA;MACA,IAAAuB,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAvB,YAAA;IACA;IAEA,aACArB,aAAA,WAAAA,cAAA;MACA;MACA,KAAArD,cAAA,IACA;QAAAb,KAAA;QAAA+G,KAAA,OAAAhH,aAAA,CAAAiH,MAAA;QAAA9E,IAAA;QAAA+E,KAAA;MAAA,GACA;QAAAjH,KAAA;QAAA+G,KAAA;QAAA7E,IAAA;QAAA+E,KAAA;MAAA,GACA;QAAAjH,KAAA;QAAA+G,KAAA;QAAA7E,IAAA;QAAA+E,KAAA;MAAA,GACA;QAAAjH,KAAA;QAAA+G,KAAA;QAAA7E,IAAA;QAAA+E,KAAA;MAAA,EACA;MACA,OAAAjD,OAAA,CAAAkD,OAAA;IACA;IAEA,aACA/C,YAAA,WAAAA,aAAA;MACA;MACA,IAAAgD,QAAA;MACA,IAAAC,KAAA,OAAAvE,IAAA;MAEA,SAAAwE,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAAzE,IAAA,CAAAuE,KAAA;QACAE,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA,KAAAH,CAAA;QACA,IAAAI,OAAA,GAAAH,IAAA,CAAAI,WAAA,GAAAC,SAAA;QAEAR,QAAA,CAAAS,IAAA;UACAN,IAAA,EAAAG,OAAA;UACAI,UAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;UACAC,YAAA,EAAAH,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;UACAE,cAAA,EAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACA;MACA;MAEA,KAAAlH,SAAA,GAAAqG,QAAA;MACA,OAAAnD,OAAA,CAAAkD,OAAA;IACA;IAEA,aACAiB,WAAA,WAAAA,YAAA;MACA,KAAA/H,WAAA,CAAAC,OAAA;MACA,KAAA+D,OAAA;IACA;IAEA,aACAgE,UAAA,WAAAA,WAAA;MACA,KAAAlI,SAAA;MACA,KAAAmI,SAAA;MACA,KAAAF,WAAA;IACA;IAEA,WACAG,aAAA,WAAAA,cAAA;MACA,KAAAlE,OAAA;MACA,KAAAF,aAAA;MACA,KAAAC,YAAA;IACA;IAEA,cACAoE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9I,GAAA,GAAA8I,SAAA,CAAArD,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAE,MAAA,IAAAF,IAAA,CAAAC,KAAA;MAAA;MACA,KAAA1F,MAAA,GAAA6I,SAAA,CAAAxB,MAAA;MACA,KAAApH,QAAA,IAAA4I,SAAA,CAAAxB,MAAA;IACA;IAEA,aACAyB,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAzI,IAAA;MACA,KAAAE,IAAA,OAAAyE,cAAA,CAAArB,OAAA,MAAAmF,GAAA;IACA;IAEA,aACAC,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,MAAA,GAAAH,GAAA,IAAAA,GAAA,CAAArD,KAAA,SAAA3F,GAAA;MACA,KAAAoJ,MAAA,CAAAC,OAAA,sBAAAhE,IAAA;QACA;QACA,IAAAiE,mBAAA,EAAAH,MAAA,CAAAI,IAAA,OAAAlE,IAAA;UACA6D,MAAA,CAAAxE,OAAA;UACAwE,MAAA,CAAAE,MAAA,CAAAI,UAAA;QACA;MACA,GAAAtC,KAAA;IACA;IAEA,aACAuC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAN,MAAA,CAAAC,OAAA,6BAAAhE,IAAA;QACA;QACA,IAAAsE,qBAAA,IAAAtE,IAAA;UACAqE,MAAA,CAAAhF,OAAA;UACAgF,MAAA,CAAAN,MAAA,CAAAI,UAAA;QACA,GAAAtC,KAAA,WAAArC,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACA6E,MAAA,CAAAN,MAAA,CAAAQ,QAAA;QACA;MACA,GAAA1C,KAAA;IACA;IAEA,aACA2C,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAV,MAAA,CAAAC,OAAA,0BAAAhE,IAAA;QACA,IAAAL,MAAA,GAAA8E,MAAA,CAAA7E,YAAA,KAAAC,cAAA,CAAArB,OAAA,MAAAiG,MAAA,CAAApJ,WAAA,GAAAoJ,MAAA,CAAAtJ,SAAA;QACA,OAAAwE,MAAA,CAAArE,OAAA;QACA,OAAAqE,MAAA,CAAApE,QAAA;QAEA,IAAAmJ,sBAAA,EAAA/E,MAAA,EAAAK,IAAA,WAAAC,QAAA;UACAwE,MAAA,CAAAE,YAAA,CAAA1E,QAAA,8BAAA2E,MAAA,KAAA9G,IAAA,GAAAG,OAAA;UACAwG,MAAA,CAAAV,MAAA,CAAAI,UAAA;QACA,GAAAtC,KAAA,WAAArC,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACAiF,MAAA,CAAAV,MAAA,CAAAQ,QAAA;QACA;MACA,GAAA1C,KAAA;IACA;IAEA,WACA8C,YAAA,WAAAA,aAAAlK,IAAA,EAAAoK,QAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,EAAAtK,IAAA;QAAA6B,IAAA;MAAA;MACA,IAAA0I,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,IAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAR,IAAA;MACAE,IAAA,CAAAO,QAAA,GAAAV,QAAA;MACAI,QAAA,CAAAO,IAAA,CAAAC,WAAA,CAAAT,IAAA;MACAA,IAAA,CAAAU,KAAA;MACAT,QAAA,CAAAO,IAAA,CAAAG,WAAA,CAAAX,IAAA;MACAI,MAAA,CAAAC,GAAA,CAAAO,eAAA,CAAAZ,IAAA,CAAAG,IAAA;IACA;IAEA,aACAU,iBAAA,WAAAA,kBAAAC,SAAA;MACA,IAAAzD,KAAA,OAAAvE,IAAA;MACA,IAAAiI,WAAA,OAAAjI,IAAA,CAAAuE,KAAA,CAAA2D,WAAA,IAAA3D,KAAA,CAAA4D,QAAA,IAAA5D,KAAA,CAAAI,OAAA,KAAAJ,KAAA,CAAA6D,MAAA;MACA,IAAAC,YAAA,OAAArI,IAAA,CAAAuE,KAAA,CAAA2D,WAAA,IAAA3D,KAAA,CAAA4D,QAAA;MAEA,QAAAH,SAAA;QACA;UACA,KAAA3K,SAAA,IACA,KAAAiL,SAAA,CAAA/D,KAAA,kBACA,KAAA+D,SAAA,CAAA/D,KAAA,iBACA;UACA;QACA;UACA,KAAAlH,SAAA,IACA,KAAAiL,SAAA,CAAAL,WAAA,kBACA,KAAAK,SAAA,CAAA/D,KAAA,iBACA;UACA;QACA;UACA,KAAAlH,SAAA,IACA,KAAAiL,SAAA,CAAAD,YAAA,kBACA,KAAAC,SAAA,CAAA/D,KAAA,iBACA;UACA;QACA;UACA,KAAAhH,WAAA,CAAAG,OAAA;UACA;QACA;UACA,KAAAH,WAAA,CAAAG,OAAA;UACA;QACA;UACA,KAAAH,WAAA,CAAAG,OAAA;UACA;QACA;UACA;MACA;MACA,KAAA4H,WAAA;IACA;IAEA,aACAiD,gBAAA,WAAAA,iBAAAC,SAAA;MACA,QAAAA,SAAA;QACA;UACA,KAAA9B,YAAA;UACA;QACA;UACA;MACA;IACA;IAEA,aACA+B,iBAAA,WAAAA,kBAAAD,SAAA;MACA,QAAAA,SAAA;QACA;UACA,KAAA1C,YAAA;UACA;QACA;UACA;MACA;IACA;IAEA,aACA4C,kBAAA,WAAAA,mBAAAC,MAAA;MACA,KAAAzK,WAAA,GAAAyK,MAAA;MACA,KAAArH,YAAA;IACA;IAEA,eACAsH,cAAA,WAAAA,eAAApK,IAAA;MACA,IAAAqK,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAArK,IAAA,KAAAA,IAAA;IACA;IAEA,iBACAsK,iBAAA,WAAAA,kBAAAtK,IAAA;MACA,IAAAyF,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAzF,IAAA;IACA;IAEA,eACAuK,oBAAA,WAAAA,qBAAAvK,IAAA;MACA,IAAAqK,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAArK,IAAA,KAAAA,IAAA;IACA;IAEA,iBACAwK,uBAAA,WAAAA,wBAAAxK,IAAA;MACA,IAAAyF,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAzF,IAAA;IACA;EACA;AACA", "ignoreList": []}]}