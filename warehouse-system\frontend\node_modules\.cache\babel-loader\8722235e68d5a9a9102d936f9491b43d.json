{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\check\\print.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\check\\print.vue", "mtime": 1756537522255}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\babel.config.js", "mtime": 1747299429443}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_check", "require", "_userUtils", "name", "dicts", "data", "loading", "isPrinting", "printSettingsVisible", "checkData", "checkCode", "warehouseName", "checkTime", "status", "createBy", "createByName", "auditBy", "auditByName", "<PERSON><PERSON><PERSON>", "remark", "details", "checkId", "printSettings", "marginTop", "marginRight", "marginBottom", "marginLeft", "fontSize", "orientation", "paperSize", "created", "$route", "params", "id", "query", "console", "log", "$message", "error", "getCheckData", "methods", "_this", "getInventoryCheck", "then", "response", "userNames", "filter", "length", "getBatchUserRealNames", "nameMap", "catch", "handlePrintSettings", "savePrintSettings", "localStorage", "setItem", "JSON", "stringify", "success", "loadPrintSettings", "savedSettings", "getItem", "parse", "getDiffClass", "diffQuantity", "handlePrint", "_this2", "warning", "$nextTick", "printWindow", "window", "open", "printContent", "generatePrintHTML", "document", "write", "close", "onload", "setTimeout", "print", "detailsHTML", "for<PERSON>ach", "item", "index", "diffClass", "concat", "productCode", "productName", "bookQuantity", "realQuantity", "pageSizeStyle", "parseTime", "getStatusName", "statusDict", "dict", "type", "inventory_check_status", "statusItem", "find", "value", "label", "handleClose", "$router", "go", "mounted"], "sources": ["src/views/inventory/check/print.vue"], "sourcesContent": ["<template>\n  <div class=\"print-container\">\n    <!-- 打印预览界面 -->\n    <div class=\"print-header\" v-if=\"!isPrinting\">\n      <el-button type=\"primary\" icon=\"el-icon-printer\" size=\"small\" @click=\"handlePrint\">\n        打印盘点单\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-setting\" size=\"small\" @click=\"handlePrintSettings\">\n        打印设置\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-close\" size=\"small\" @click=\"handleClose\">\n        关闭\n      </el-button>\n    </div>\n    \n    <!-- 打印设置对话框 -->\n    <el-dialog title=\"打印设置\" :visible.sync=\"printSettingsVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"printSettingsForm\" :model=\"printSettings\" label-width=\"100px\">\n        <el-form-item label=\"页面边距\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginTop\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginRight\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginBottom\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginLeft\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n          </el-row>\n          <div class=\"margin-labels\">\n            <span>上</span>\n            <span>右</span>\n            <span>下</span>\n            <span>左</span>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"字体大小\">\n          <el-slider v-model=\"printSettings.fontSize\" :min=\"8\" :max=\"20\" show-input></el-slider>\n        </el-form-item>\n        \n        <el-form-item label=\"页面方向\">\n          <el-radio-group v-model=\"printSettings.orientation\">\n            <el-radio label=\"portrait\">纵向</el-radio>\n            <el-radio label=\"landscape\">横向</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"纸张大小\">\n          <el-select v-model=\"printSettings.paperSize\" placeholder=\"请选择纸张大小\">\n            <el-option label=\"A4\" value=\"A4\"></el-option>\n            <el-option label=\"A5\" value=\"A5\"></el-option>\n            <el-option label=\"B5\" value=\"B5\"></el-option>\n            <el-option label=\"Letter\" value=\"letter\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"printSettingsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"savePrintSettings\">确 定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 打印内容 -->\n    <div class=\"print-content\" id=\"printContent\">\n      <div class=\"check-header\">\n        <h2 class=\"title\">库存盘点单</h2>\n        <div class=\"header-info\">\n          <div class=\"header-item\">\n            <span class=\"label\">盘点单号：</span>\n            <span class=\"value\">{{ checkData.checkCode }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">盘点日期：</span>\n            <span class=\"value\">{{ parseTime(checkData.checkTime) }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">状态：</span>\n            <span class=\"value\">\n              <dict-tag :options=\"dict.type.inventory_check_status\" :value=\"checkData.status\" />\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"check-info\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">仓库名称：</span>\n              <span class=\"value\">{{ checkData.warehouseName }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">制单人：</span>\n              <span class=\"value\">{{ checkData.createByName || checkData.createBy }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">审核人：</span>\n              <span class=\"value\">{{ checkData.auditByName || checkData.auditBy || '未审核' }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">仓库负责人：</span>\n              <span class=\"value\">{{ checkData.managerName || '未指定' }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <div class=\"info-item\">\n              <span class=\"label\">备注：</span>\n              <span class=\"value\">{{ checkData.remark }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <div class=\"check-details\">\n        <h3>盘点物品信息</h3>\n        <el-table :data=\"checkData.details\" class=\"detail-table\">\n          <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n          <el-table-column label=\"物品编码\" prop=\"productCode\" />\n          <el-table-column label=\"物品名称\" prop=\"productName\" />\n          <el-table-column label=\"账面数量\" prop=\"bookQuantity\" />\n          <el-table-column label=\"实际数量\" prop=\"realQuantity\" />\n          <el-table-column label=\"差异数量\" prop=\"diffQuantity\">\n            <template slot-scope=\"scope\">\n              <span :class=\"getDiffClass(scope.row.diffQuantity)\">{{ scope.row.diffQuantity }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"备注\" prop=\"remark\" />\n        </el-table>\n      </div>\n      \n      <div class=\"check-footer\">\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">盘点人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 120px;\">仓库负责人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getInventoryCheck } from \"@/api/inventory/check\";\nimport { getBatchUserRealNames } from \"@/utils/userUtils\";\n\nexport default {\n  name: \"CheckPrint\",\n  dicts: ['inventory_check_status'],\n  data() {\n    return {\n      loading: false,\n      isPrinting: false,\n      printSettingsVisible: false,\n      checkData: {\n        checkCode: \"\",\n        warehouseName: \"\",\n        checkTime: null,\n        status: \"\",\n        createBy: \"\",\n        createByName: \"\",\n        auditBy: \"\",\n        auditByName: \"\",\n        managerName: \"\",\n        remark: \"\",\n        details: []\n      },\n      checkId: null,\n      printSettings: {\n        marginTop: 15,\n        marginRight: 15,\n        marginBottom: 15,\n        marginLeft: 15,\n        fontSize: 12,\n        orientation: 'portrait',\n        paperSize: 'A4'\n      }\n    };\n  },\n  created() {\n    this.checkId = this.$route.params.checkId || this.$route.params.id || this.$route.query.id;\n    console.log('盘点单打印页面初始化，盘点单ID:', this.checkId);\n    if (!this.checkId) {\n      this.$message.error('缺少盘点单ID参数');\n      return;\n    }\n    this.getCheckData();\n  },\n  methods: {\n    /** 获取盘点单信息 */\n    getCheckData() {\n      this.loading = true;\n      getInventoryCheck(this.checkId).then(response => {\n        this.checkData = response.data;\n        \n        // 获取用户真实姓名\n        const userNames = [\n          this.checkData.createBy,\n          this.checkData.auditBy\n        ].filter(name => name); // 过滤空值\n\n        if (userNames.length > 0) {\n          getBatchUserRealNames(userNames).then(nameMap => {\n            // 更新用户真实姓名\n            if (this.checkData.createBy && nameMap[this.checkData.createBy]) {\n              this.checkData.createByName = nameMap[this.checkData.createBy];\n            }\n            if (this.checkData.auditBy && nameMap[this.checkData.auditBy]) {\n              this.checkData.auditByName = nameMap[this.checkData.auditBy];\n            }\n          });\n        }\n        \n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n        this.$message.error(\"获取盘点单信息失败\");\n      });\n    },\n    \n    /** 处理打印设置 */\n    handlePrintSettings() {\n      this.printSettingsVisible = true;\n    },\n    \n    /** 保存打印设置 */\n    savePrintSettings() {\n      // 保存到本地存储\n      localStorage.setItem('checkPrintSettings', JSON.stringify(this.printSettings));\n      this.printSettingsVisible = false;\n      this.$message.success('打印设置已保存');\n    },\n    \n    /** 加载打印设置 */\n    loadPrintSettings() {\n      const savedSettings = localStorage.getItem('checkPrintSettings');\n      if (savedSettings) {\n        this.printSettings = JSON.parse(savedSettings);\n      }\n    },\n    \n    /** 获取差异数量样式 */\n    getDiffClass(diffQuantity) {\n      if (diffQuantity < 0) {\n        return 'text-red';\n      } else if (diffQuantity > 0) {\n        return 'text-green';\n      }\n      return '';\n    },\n    \n    /** 打印 */\n    handlePrint() {\n      this.isPrinting = true;\n      \n      // 确保数据已加载\n      if (!this.checkData.checkCode) {\n        this.$message.warning('数据还在加载中，请稍后再试');\n        this.isPrinting = false;\n        return;\n      }\n      \n      this.$nextTick(() => {\n        // 创建新窗口进行打印\n        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');\n        \n        // 生成打印内容\n        const printContent = this.generatePrintHTML();\n        \n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        \n        // 等待内容加载完成后打印\n        printWindow.onload = () => {\n          setTimeout(() => {\n            printWindow.print();\n            // 打印完成后不自动关闭窗口，让用户手动关闭\n            this.isPrinting = false;\n          }, 500);\n        };\n      });\n    },\n    \n    /** 生成打印页面HTML */\n    generatePrintHTML() {\n      const details = this.checkData.details || [];\n      let detailsHTML = '';\n      \n      details.forEach((item, index) => {\n        const diffClass = item.diffQuantity < 0 ? 'color: #F56C6C;' : item.diffQuantity > 0 ? 'color: #67C23A;' : '';\n        detailsHTML += `\n          <tr>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${index + 1}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productCode || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productName || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.bookQuantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.realQuantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center; ${diffClass}\">${item.diffQuantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.remark || ''}</td>\n          </tr>\n        `;\n      });\n      \n      // 根据设置确定页面方向和纸张大小\n      let pageSizeStyle = '';\n      if (this.printSettings.orientation === 'landscape') {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;\n      } else {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;\n      }\n      \n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>库存盘点单打印</title>\n          <style>\n            @page { \n              ${pageSizeStyle}\n              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; \n            }\n            \n            * {\n              box-sizing: border-box;\n            }\n            \n            html, body { \n              font-family: \"Microsoft YaHei\", SimSun, sans-serif; \n              font-size: ${this.printSettings.fontSize}pt; \n              color: #000; \n              background: #fff; \n              margin: 0; \n              padding: ${this.printSettings.marginTop/3}mm;\n              width: 100%;\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n            \n            .container {\n              width: 100%;\n              max-width: 1000px;\n              margin: 0 auto;\n            }\n            \n            .print-container {\n              padding: 20px;\n              background-color: #fff;\n              color: #000;\n            }\n            \n            .print-content {\n              max-width: 1000px;\n              margin: 0 auto;\n              font-family: \"SimSun\", \"宋体\", serif;\n              color: #000;\n            }\n            \n            .check-header {\n              text-align: center;\n              margin-bottom: 30px;\n              border-bottom: 2px solid #000;\n              padding-bottom: 10px;\n            }\n            \n            .check-header .title {\n              font-size: ${this.printSettings.fontSize + 8}pt;\n              font-weight: bold;\n              margin: 0 0 20px 0;\n            }\n            \n            .header-info {\n              display: flex;\n              justify-content: space-between;\n            }\n            \n            .header-item {\n              display: flex;\n              align-items: center;\n            }\n            \n            .header-item .label {\n              font-weight: bold;\n              margin-right: 5px;\n            }\n            \n            .check-info {\n              margin-bottom: 30px;\n            }\n            \n            .info-item {\n              margin-bottom: 15px;\n              display: flex;\n              align-items: flex-start;\n            }\n            \n            .info-item .label {\n              font-weight: bold;\n              min-width: 100px;\n              flex-shrink: 0;\n            }\n            \n            .check-details h3,\n            .approval-info h3 {\n              font-size: ${this.printSettings.fontSize + 4}pt;\n              margin: 0 0 15px 0;\n              border-left: 4px solid #409EFF;\n              padding-left: 10px;\n            }\n            \n            table {\n              width: 100%;\n              border-collapse: collapse;\n              margin-bottom: 30px;\n              table-layout: fixed;\n            }\n            \n            th {\n              background-color: #f5f5f5;\n              color: #000;\n              font-weight: bold;\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n            }\n            \n            td {\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n            \n            .check-footer {\n              margin: 30px 0;\n            }\n            \n            .footer-item {\n              margin-bottom: 15px;\n            }\n            \n            .signature-line {\n              display: inline-block;\n              width: 80px;\n              height: 1px;\n              background-color: #000;\n              margin-left: 10px;\n            }\n            \n            .text-red {\n              color: #F56C6C;\n            }\n            \n            .text-green {\n              color: #67C23A;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"print-container\">\n            <div class=\"print-content\">\n              <div class=\"check-header\">\n                <h2 class=\"title\">库存盘点单</h2>\n                <div class=\"header-info\">\n                  <div class=\"header-item\">\n                    <span class=\"label\">盘点单号：</span>\n                    <span class=\"value\">${this.checkData.checkCode || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">盘点日期：</span>\n                    <span class=\"value\">${this.parseTime(this.checkData.checkTime) || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">状态：</span>\n                    <span class=\"value\">${this.getStatusName(this.checkData.status) || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"check-info\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库名称：</span>\n                    <span>${this.checkData.warehouseName || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">制单人：</span>\n                    <span>${this.checkData.createByName || this.checkData.createBy || ''}</span>\n                  </div>\n                </div>\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人：</span>\n                    <span>${this.checkData.auditByName || this.checkData.auditBy || '未审核'}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 120px;\">仓库负责人：</span>\n                    <span>${this.checkData.managerName || '未指定'}</span>\n                  </div>\n                </div>\n                \n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 100%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">备注：</span>\n                    <span>${this.checkData.remark || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"check-details\">\n                <h3>盘点物品信息</h3>\n                <table>\n                  <thead>\n                    <tr>\n                      <th>序号</th>\n                      <th>物品编码</th>\n                      <th>物品名称</th>\n                      <th>账面数量</th>\n                      <th>实际数量</th>\n                      <th>差异数量</th>\n                      <th>备注</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    ${detailsHTML}\n                  </tbody>\n                </table>\n              </div>\n              \n              <div class=\"check-footer\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">盘点人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 120px;\">仓库负责人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    \n    /** 获取状态名称 */\n    getStatusName(status) {\n      // 使用与屏幕预览相同的字典映射方式\n      const statusDict = this.dict.type.inventory_check_status || [];\n      const statusItem = statusDict.find(item => item.value === status);\n      return statusItem ? statusItem.label : '';\n    },\n    \n    /** 关闭 */\n    handleClose() {\n      this.$router.go(-1);\n    }\n  },\n  mounted() {\n    // 加载打印设置\n    this.loadPrintSettings();\n  }\n};\n</script>\n\n<style scoped>\n.print-container {\n  padding: 20px;\n  background-color: #fff;\n  color: #000;\n}\n\n.print-header {\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.print-content {\n  max-width: 1000px;\n  margin: 0 auto;\n  font-family: \"SimSun\", \"宋体\", serif;\n  color: #000;\n}\n\n.check-header {\n  text-align: center;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #000;\n  padding-bottom: 10px;\n}\n\n.check-header .title {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0 0 20px 0;\n}\n\n.header-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.header-item {\n  display: flex;\n  align-items: center;\n}\n\n.header-item .label {\n  font-weight: bold;\n}\n\n.check-info {\n  margin-bottom: 30px;\n}\n\n.info-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.info-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.detail-table {\n  margin-bottom: 30px;\n}\n\n.detail-table ::v-deep .el-table__header th {\n  background-color: #f5f5f5;\n  color: #000;\n}\n\n.detail-table ::v-deep .el-table__row td {\n  color: #000;\n}\n\n.check-details h3,\n.approval-info h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n  border-left: 4px solid #409EFF;\n  padding-left: 10px;\n}\n\n.check-footer {\n  margin: 30px 0;\n}\n\n.footer-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-end;\n  height: 30px;\n}\n\n.footer-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.signature-line {\n  display: inline-block;\n  width: 80px;\n  height: 1px;\n  background-color: #000;\n  margin-left: 5px;\n  position: relative;\n  bottom: 3px;\n}\n\n.margin-labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n\n.text-red {\n  color: #F56C6C;\n}\n\n.text-green {\n  color: #67C23A;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;AAgLA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,oBAAA;MACAC,SAAA;QACAC,SAAA;QACAC,aAAA;QACAC,SAAA;QACAC,MAAA;QACAC,QAAA;QACAC,YAAA;QACAC,OAAA;QACAC,WAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,aAAA;QACAC,SAAA;QACAC,WAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAT,OAAA,QAAAU,MAAA,CAAAC,MAAA,CAAAX,OAAA,SAAAU,MAAA,CAAAC,MAAA,CAAAC,EAAA,SAAAF,MAAA,CAAAG,KAAA,CAAAD,EAAA;IACAE,OAAA,CAAAC,GAAA,2BAAAf,OAAA;IACA,UAAAA,OAAA;MACA,KAAAgB,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACA,cACAD,YAAA,WAAAA,aAAA;MAAA,IAAAE,KAAA;MACA,KAAAnC,OAAA;MACA,IAAAoC,wBAAA,OAAArB,OAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAhC,SAAA,GAAAmC,QAAA,CAAAvC,IAAA;;QAEA;QACA,IAAAwC,SAAA,IACAJ,KAAA,CAAAhC,SAAA,CAAAK,QAAA,EACA2B,KAAA,CAAAhC,SAAA,CAAAO,OAAA,CACA,CAAA8B,MAAA,WAAA3C,IAAA;UAAA,OAAAA,IAAA;QAAA;;QAEA,IAAA0C,SAAA,CAAAE,MAAA;UACA,IAAAC,gCAAA,EAAAH,SAAA,EAAAF,IAAA,WAAAM,OAAA;YACA;YACA,IAAAR,KAAA,CAAAhC,SAAA,CAAAK,QAAA,IAAAmC,OAAA,CAAAR,KAAA,CAAAhC,SAAA,CAAAK,QAAA;cACA2B,KAAA,CAAAhC,SAAA,CAAAM,YAAA,GAAAkC,OAAA,CAAAR,KAAA,CAAAhC,SAAA,CAAAK,QAAA;YACA;YACA,IAAA2B,KAAA,CAAAhC,SAAA,CAAAO,OAAA,IAAAiC,OAAA,CAAAR,KAAA,CAAAhC,SAAA,CAAAO,OAAA;cACAyB,KAAA,CAAAhC,SAAA,CAAAQ,WAAA,GAAAgC,OAAA,CAAAR,KAAA,CAAAhC,SAAA,CAAAO,OAAA;YACA;UACA;QACA;QAEAyB,KAAA,CAAAnC,OAAA;MACA,GAAA4C,KAAA;QACAT,KAAA,CAAAnC,OAAA;QACAmC,KAAA,CAAAJ,QAAA,CAAAC,KAAA;MACA;IACA;IAEA,aACAa,mBAAA,WAAAA,oBAAA;MACA,KAAA3C,oBAAA;IACA;IAEA,aACA4C,iBAAA,WAAAA,kBAAA;MACA;MACAC,YAAA,CAAAC,OAAA,uBAAAC,IAAA,CAAAC,SAAA,MAAAlC,aAAA;MACA,KAAAd,oBAAA;MACA,KAAA6B,QAAA,CAAAoB,OAAA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAA;MACA,IAAAC,aAAA,GAAAN,YAAA,CAAAO,OAAA;MACA,IAAAD,aAAA;QACA,KAAArC,aAAA,GAAAiC,IAAA,CAAAM,KAAA,CAAAF,aAAA;MACA;IACA;IAEA,eACAG,YAAA,WAAAA,aAAAC,YAAA;MACA,IAAAA,YAAA;QACA;MACA,WAAAA,YAAA;QACA;MACA;MACA;IACA;IAEA,SACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAA1D,UAAA;;MAEA;MACA,UAAAE,SAAA,CAAAC,SAAA;QACA,KAAA2B,QAAA,CAAA6B,OAAA;QACA,KAAA3D,UAAA;QACA;MACA;MAEA,KAAA4D,SAAA;QACA;QACA,IAAAC,WAAA,GAAAC,MAAA,CAAAC,IAAA;;QAEA;QACA,IAAAC,YAAA,GAAAN,MAAA,CAAAO,iBAAA;QAEAJ,WAAA,CAAAK,QAAA,CAAAC,KAAA,CAAAH,YAAA;QACAH,WAAA,CAAAK,QAAA,CAAAE,KAAA;;QAEA;QACAP,WAAA,CAAAQ,MAAA;UACAC,UAAA;YACAT,WAAA,CAAAU,KAAA;YACA;YACAb,MAAA,CAAA1D,UAAA;UACA;QACA;MACA;IACA;IAEA,iBACAiE,iBAAA,WAAAA,kBAAA;MACA,IAAApD,OAAA,QAAAX,SAAA,CAAAW,OAAA;MACA,IAAA2D,WAAA;MAEA3D,OAAA,CAAA4D,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,SAAA,GAAAF,IAAA,CAAAlB,YAAA,2BAAAkB,IAAA,CAAAlB,YAAA;QACAgB,WAAA,6GAAAK,MAAA,CAEAF,KAAA,qGAAAE,MAAA,CACAH,IAAA,CAAAI,WAAA,uGAAAD,MAAA,CACAH,IAAA,CAAAK,WAAA,uGAAAF,MAAA,CACAH,IAAA,CAAAM,YAAA,uGAAAH,MAAA,CACAH,IAAA,CAAAO,YAAA,qGAAAJ,MAAA,CACAD,SAAA,SAAAC,MAAA,CAAAH,IAAA,CAAAlB,YAAA,uGAAAqB,MAAA,CACAH,IAAA,CAAA9D,MAAA,2CAEA;MACA;;MAEA;MACA,IAAAsE,aAAA;MACA,SAAAnE,aAAA,CAAAM,WAAA;QACA6D,aAAA,YAAAL,MAAA,MAAA9D,aAAA,CAAAO,SAAA;MACA;QACA4D,aAAA,YAAAL,MAAA,MAAA9D,aAAA,CAAAO,SAAA;MACA;MAEA,2TAAAuD,MAAA,CASAK,aAAA,8BAAAL,MAAA,CACA,KAAA9D,aAAA,CAAAC,SAAA,SAAA6D,MAAA,MAAA9D,aAAA,CAAAE,WAAA,SAAA4D,MAAA,MAAA9D,aAAA,CAAAG,YAAA,SAAA2D,MAAA,MAAA9D,aAAA,CAAAI,UAAA,wPAAA0D,MAAA,CASA,KAAA9D,aAAA,CAAAK,QAAA,6HAAAyD,MAAA,CAIA,KAAA9D,aAAA,CAAAC,SAAA,g8BAAA6D,MAAA,CAiCA,KAAA9D,aAAA,CAAAK,QAAA,6+BAAAyD,MAAA,CAsCA,KAAA9D,aAAA,CAAAK,QAAA,+0DAAAyD,MAAA,CA+DA,KAAA3E,SAAA,CAAAC,SAAA,yNAAA0E,MAAA,CAIA,KAAAM,SAAA,MAAAjF,SAAA,CAAAG,SAAA,8MAAAwE,MAAA,CAIA,KAAAO,aAAA,MAAAlF,SAAA,CAAAI,MAAA,qaAAAuE,MAAA,CASA,KAAA3E,SAAA,CAAAE,aAAA,iPAAAyE,MAAA,CAIA,KAAA3E,SAAA,CAAAM,YAAA,SAAAN,SAAA,CAAAK,QAAA,8UAAAsE,MAAA,CAMA,KAAA3E,SAAA,CAAAQ,WAAA,SAAAR,SAAA,CAAAO,OAAA,gQAAAoE,MAAA,CAIA,KAAA3E,SAAA,CAAAS,WAAA,8VAAAkE,MAAA,CAOA,KAAA3E,SAAA,CAAAU,MAAA,owBAAAiE,MAAA,CAoBAL,WAAA;IA0BA;IAEA,aACAY,aAAA,WAAAA,cAAA9E,MAAA;MACA;MACA,IAAA+E,UAAA,QAAAC,IAAA,CAAAC,IAAA,CAAAC,sBAAA;MACA,IAAAC,UAAA,GAAAJ,UAAA,CAAAK,IAAA,WAAAhB,IAAA;QAAA,OAAAA,IAAA,CAAAiB,KAAA,KAAArF,MAAA;MAAA;MACA,OAAAmF,UAAA,GAAAA,UAAA,CAAAG,KAAA;IACA;IAEA,SACAC,WAAA,WAAAA,YAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAA7C,iBAAA;EACA;AACA", "ignoreList": []}]}