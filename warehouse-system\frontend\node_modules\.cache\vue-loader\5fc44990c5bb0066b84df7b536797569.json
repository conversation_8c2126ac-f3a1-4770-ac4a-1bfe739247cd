{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\log\\system\\index.vue?vue&type=style&index=0&id=1f422377&scoped=true&lang=css", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\log\\system\\index.vue", "mtime": 1756537793460}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1755901395158}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1755901427908}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1755901408157}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAstBA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/log/system", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计信息卡片 -->\r\n    <LogManagement\r\n      :statistics-data=\"statisticsData\"\r\n      :show-trend=\"true\"\r\n      trend-title=\"系统操作趋势\"\r\n      :trend-data=\"trendData\"\r\n      :trend-period=\"trendPeriod\"\r\n      :chart-config=\"chartConfig\"\r\n      :quick-filters=\"quickFilters\"\r\n      :main-actions=\"mainActions\"\r\n      :batch-actions=\"batchActions\"\r\n      :extra-actions=\"extraActions\"\r\n      :show-search.sync=\"showSearch\"\r\n      @period-change=\"handlePeriodChange\"\r\n      @quick-filter=\"handleQuickFilter\"\r\n      @main-action=\"handleMainAction\"\r\n      @batch-action=\"handleBatchAction\"\r\n      @refresh=\"handleRefresh\"\r\n    />\r\n\r\n    <!-- 高级搜索表单 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-form-item label=\"日志类型\" prop=\"logType\">\r\n        <el-select v-model=\"queryParams.logType\" placeholder=\"请选择日志类型\" clearable>\r\n          <el-option label=\"系统操作\" value=\"SYSTEM\" />\r\n          <el-option label=\"业务操作\" value=\"BUSINESS\" />\r\n          <el-option label=\"数据变更\" value=\"DATA\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作模块\" prop=\"moduleName\">\r\n        <el-input\r\n          v-model=\"queryParams.moduleName\"\r\n          placeholder=\"请输入操作模块\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"操作人员\" prop=\"operator\">\r\n        <el-input\r\n          v-model=\"queryParams.operator\"\r\n          placeholder=\"请输入操作人员\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"操作类型\" prop=\"operationType\">\r\n        <el-select v-model=\"queryParams.operationType\" placeholder=\"操作类型\" clearable>\r\n          <el-option label=\"新增\" value=\"INSERT\" />\r\n          <el-option label=\"修改\" value=\"UPDATE\" />\r\n          <el-option label=\"删除\" value=\"DELETE\" />\r\n          <el-option label=\"查询\" value=\"SELECT\" />\r\n          <el-option label=\"导出\" value=\"EXPORT\" />\r\n          <el-option label=\"导入\" value=\"IMPORT\" />\r\n          <el-option label=\"授权\" value=\"GRANT\" />\r\n          <el-option label=\"其他\" value=\"OTHER\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作状态\" prop=\"operationStatus\">\r\n        <el-select v-model=\"queryParams.operationStatus\" placeholder=\"操作状态\" clearable>\r\n          <el-option label=\"成功\" value=\"0\" />\r\n          <el-option label=\"失败\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          :picker-options=\"pickerOptions\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['log:operation:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          @click=\"handleClean\"\r\n          v-hasPermi=\"['log:operation:remove']\"\r\n        >清空</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['log:operation:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 数据表格 -->\r\n    <el-table v-loading=\"loading\" :data=\"systemLogList\" @selection-change=\"handleSelectionChange\" empty-text=\"暂无数据\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"日志编号\" align=\"center\" prop=\"logId\" width=\"80\" />\r\n      <el-table-column label=\"日志类型\" align=\"center\" prop=\"logType\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getLogTypeTagType(scope.row.logType)\">\r\n            {{ getLogTypeName(scope.row.logType) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作模块\" align=\"center\" prop=\"moduleName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationType\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getOperationTypeTagType(scope.row.operationType)\">\r\n            {{ getOperationTypeName(scope.row.operationType) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作名称\" align=\"center\" prop=\"operationName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作人员\" align=\"center\" prop=\"operator\" width=\"120\" />\r\n      <el-table-column label=\"操作状态\" align=\"center\" prop=\"operationStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.operationStatus === '0' ? 'success' : 'danger'\">\r\n            {{ scope.row.operationStatus === '0' ? '成功' : '失败' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"耗时(ms)\" align=\"center\" prop=\"costTime\" width=\"100\" />\r\n      <el-table-column label=\"操作时间\" align=\"center\" prop=\"operationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.operationTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['log:system:detail']\"\r\n          >详细</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 系统日志详细 -->\r\n    <el-dialog title=\"系统日志详细\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\" size=\"mini\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"日志类型：\">\r\n              <el-tag :type=\"getLogTypeTagType(form.logType)\">\r\n                {{ getLogTypeName(form.logType) }}\r\n              </el-tag>\r\n            </el-form-item>\r\n            <el-form-item label=\"操作模块：\">{{ form.moduleName }}</el-form-item>\r\n            <el-form-item label=\"操作类型：\">\r\n              <el-tag :type=\"getOperationTypeTagType(form.operationType)\">\r\n                {{ getOperationTypeName(form.operationType) }}\r\n              </el-tag>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作人员：\">{{ form.createBy }}</el-form-item>\r\n            <el-form-item label=\"操作IP：\">{{ form.operatorIp }}</el-form-item>\r\n            <el-form-item label=\"操作时间：\">{{ parseTime(form.createTime) }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"操作名称：\">{{ form.operationName }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"请求地址：\">{{ form.requestUrl }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"请求方式：\">{{ form.requestMethod }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"耗时：\">{{ form.costTime }}ms</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"请求参数：\">\r\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.requestParams\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"返回结果：\">\r\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.responseResult\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作状态：\">\r\n              <el-tag :type=\"form.operationStatus === '0' ? 'success' : 'danger'\">\r\n                {{ form.operationStatus === '0' ? '成功' : '失败' }}\r\n              </el-tag>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" v-if=\"form.operationStatus === '1'\">\r\n            <el-form-item label=\"错误信息：\">\r\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.errorMsg\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"open = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport LogManagement from \"@/components/LogManagement\";\r\nimport { listOperLog, delOperLog, cleanOperLog, exportOperLog } from \"@/api/monitor/operlog\";\r\n\r\nexport default {\r\n  name: \"SystemLog\",\r\n  components: {\r\n    LogManagement\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      systemLogList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        logType: \"SYSTEM\",  // 默认查询系统日志\r\n        moduleName: undefined,\r\n        createBy: undefined,\r\n        operationType: undefined,\r\n        operationStatus: undefined\r\n      },\r\n      // 统计数据\r\n      statisticsData: [],\r\n      // 趋势数据\r\n      trendData: [],\r\n      trendPeriod: '7d',\r\n      // 图表配置\r\n      chartConfig: {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          data: ['系统操作', '业务操作', '数据变更']\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: []\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        yAxisName: '操作数量',\r\n        series: [\r\n          {\r\n            name: '系统操作',\r\n            type: 'line',\r\n            dataKey: 'systemLogs',\r\n            stack: '总量',\r\n            areaStyle: {\r\n              startColor: '#80FFA5',\r\n              endColor: '#008F52'\r\n            },\r\n            smooth: true\r\n          },\r\n          {\r\n            name: '业务操作',\r\n            type: 'line',\r\n            dataKey: 'businessLogs',\r\n            stack: '总量',\r\n            areaStyle: {\r\n              startColor: '#FFA0A0',\r\n              endColor: '#8F0000'\r\n            },\r\n            smooth: true\r\n          },\r\n          {\r\n            name: '数据变更',\r\n            type: 'line',\r\n            dataKey: 'dataChangeLogs',\r\n            stack: '总量',\r\n            areaStyle: {\r\n              startColor: '#A0A0FF',\r\n              endColor: '#00008F'\r\n            },\r\n            smooth: true\r\n          }\r\n        ]\r\n      },\r\n      // 快速筛选\r\n      quickFilters: [\r\n        { key: 'today', label: '今日', icon: 'el-icon-date' },\r\n        { key: 'week', label: '本周', icon: 'el-icon-date' },\r\n        { key: 'month', label: '本月', icon: 'el-icon-date' },\r\n        { key: 'system', label: '系统操作', icon: 'el-icon-setting' },\r\n        { key: 'business', label: '业务操作', icon: 'el-icon-s-order' },\r\n        { key: 'data', label: '数据操作', icon: 'el-icon-s-data' }\r\n      ],\r\n      // 主要操作\r\n      mainActions: [\r\n        {\r\n          key: 'export',\r\n          label: '导出Excel',\r\n          type: 'warning',\r\n          icon: 'el-icon-download',\r\n          permission: 'log:operation:export'\r\n        }\r\n      ],\r\n      // 批量操作\r\n      batchActions: [\r\n        {\r\n          key: 'batchDelete',\r\n          label: '批量删除',\r\n          icon: 'el-icon-delete',\r\n          permission: 'log:operation:remove'\r\n        }\r\n      ],\r\n      // 额外操作\r\n      extraActions: [\r\n        {\r\n          key: 'clean',\r\n          label: '清空日志',\r\n          type: 'danger',\r\n          icon: 'el-icon-delete',\r\n          permission: 'log:operation:remove'\r\n        }\r\n      ],\r\n      // 日期选择器配置\r\n      pickerOptions: {\r\n        shortcuts: [{\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近三个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    /** 初始化数据 */\r\n    async initData() {\r\n      try {\r\n        await Promise.all([\r\n          this.getStatistics(),\r\n          this.getTrendData()\r\n        ]);\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('初始化数据失败:', error);\r\n      }\r\n    },\r\n\r\n    /** 查询系统日志 */\r\n    getList() {\r\n      this.loading = true;\r\n      const params = this.addDateRange({...this.queryParams}, this.dateRange);\r\n      console.log('实际请求参数:', params); // 打印查询参数\r\n\r\n      // 使用操作日志API获取真实数据\r\n      listOperLog(params).then(response => {\r\n        console.log('API响应数据:', response); // 打印完整响应\r\n        // 映射字段名称\r\n        const mappedData = (response.rows || []).map(item => ({\r\n          logId: item.operId,\r\n          logType: item.businessType === 0 ? 'SYSTEM' : 'BUSINESS',\r\n          moduleName: item.title,\r\n          operationType: this.mapOperationType(item.businessType),\r\n          operationName: item.method,\r\n          operator: item.operName,\r\n          operationStatus: item.status,\r\n          costTime: item.costTime,\r\n          operationTime: item.operTime,\r\n          operatorIp: item.operIp,\r\n          operatorLocation: item.operLocation,\r\n          requestMethod: item.requestMethod,\r\n          requestUrl: item.operUrl,\r\n          requestParam: item.operParam,\r\n          jsonResult: item.jsonResult,\r\n          errorMsg: item.errorMsg\r\n        }));\r\n\r\n        this.systemLogList = mappedData;\r\n        this.total = response.total || 0;\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('API请求失败:', error); // 打印错误详情\r\n        this.loading = false;\r\n        this.$message.error('查询系统日志失败，请稍后重试');\r\n      });\r\n    },\r\n\r\n    /** 映射操作类型 */\r\n    mapOperationType(businessType) {\r\n      const typeMap = {\r\n        0: 'OTHER',\r\n        1: 'INSERT',\r\n        2: 'UPDATE',\r\n        3: 'DELETE',\r\n        4: 'GRANT',\r\n        5: 'EXPORT',\r\n        6: 'IMPORT',\r\n        7: 'FORCE',\r\n        8: 'GENCODE',\r\n        9: 'CLEAN'\r\n      };\r\n      return typeMap[businessType] || 'OTHER';\r\n    },\r\n\r\n    /** 获取统计数据 */\r\n    getStatistics() {\r\n      // 使用模拟数据，因为操作日志API没有统计接口\r\n      this.statisticsData = [\r\n        { title: '总日志数', value: this.systemLogList.length, icon: 'el-icon-s-data', color: '#409EFF' },\r\n        { title: '今日日志', value: 0, icon: 'el-icon-date', color: '#67C23A' },\r\n        { title: '系统操作', value: 0, icon: 'el-icon-setting', color: '#E6A23C' },\r\n        { title: '业务操作', value: 0, icon: 'el-icon-s-order', color: '#F56C6C' }\r\n      ];\r\n      return Promise.resolve();\r\n    },\r\n\r\n    /** 获取趋势数据 */\r\n    getTrendData() {\r\n      // 使用模拟数据，因为操作日志API没有趋势接口\r\n      const mockData = [];\r\n      const today = new Date();\r\n\r\n      for (let i = 6; i >= 0; i--) {\r\n        const date = new Date(today);\r\n        date.setDate(date.getDate() - i);\r\n        const dateStr = date.toISOString().substring(0, 10);\r\n\r\n        mockData.push({\r\n          date: dateStr,\r\n          systemLogs: Math.floor(Math.random() * 50) + 10,\r\n          businessLogs: Math.floor(Math.random() * 30) + 5,\r\n          dataChangeLogs: Math.floor(Math.random() * 20) + 2\r\n        });\r\n      }\r\n\r\n      this.trendData = mockData;\r\n      return Promise.resolve();\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 刷新数据 */\r\n    handleRefresh() {\r\n      this.getList();\r\n      this.getStatistics();\r\n      this.getTrendData();\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.operId || item.logId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true;\r\n      this.form = { ...row };\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const logIds = row ? [row.logId] : this.ids;\r\n      this.$modal.confirm('是否确认删除选中的系统日志数据项？').then(() => {\r\n        // 使用操作日志删除API\r\n        delOperLog(logIds.join(',')).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 清空按钮操作 */\r\n    handleClean() {\r\n      this.$modal.confirm('是否确认清空所有系统日志数据项？此操作不可恢复！').then(() => {\r\n        // 使用操作日志清空API\r\n        cleanOperLog().then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"清空成功\");\r\n        }).catch(error => {\r\n          console.error('清空系统日志失败:', error);\r\n          this.$modal.msgError(\"清空失败，请稍后重试\");\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出当前筛选条件下的系统日志数据？').then(() => {\r\n        const params = this.addDateRange({...this.queryParams}, this.dateRange);\r\n        delete params.pageNum;\r\n        delete params.pageSize;\r\n\r\n        exportOperLog(params).then(response => {\r\n          this.downloadFile(response, `系统日志_${new Date().getTime()}.xlsx`);\r\n          this.$modal.msgSuccess(\"导出成功\");\r\n        }).catch(error => {\r\n          console.error('导出系统日志失败:', error);\r\n          this.$modal.msgError(\"导出失败，请稍后重试\");\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 下载文件 */\r\n    downloadFile(data, fileName) {\r\n      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\r\n      const link = document.createElement('a');\r\n      link.href = window.URL.createObjectURL(blob);\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(link.href);\r\n    },\r\n\r\n    /** 快速筛选处理 */\r\n    handleQuickFilter(filterKey) {\r\n      const today = new Date();\r\n      const startOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());\r\n      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\r\n      \r\n      switch (filterKey) {\r\n        case 'today':\r\n          this.dateRange = [\r\n            this.parseTime(today, '{y}-{m}-{d}'),\r\n            this.parseTime(today, '{y}-{m}-{d}')\r\n          ];\r\n          break;\r\n        case 'week':\r\n          this.dateRange = [\r\n            this.parseTime(startOfWeek, '{y}-{m}-{d}'),\r\n            this.parseTime(today, '{y}-{m}-{d}')\r\n          ];\r\n          break;\r\n        case 'month':\r\n          this.dateRange = [\r\n            this.parseTime(startOfMonth, '{y}-{m}-{d}'),\r\n            this.parseTime(today, '{y}-{m}-{d}')\r\n          ];\r\n          break;\r\n        case 'system':\r\n          this.queryParams.logType = 'SYSTEM';\r\n          break;\r\n        case 'business':\r\n          this.queryParams.logType = 'BUSINESS';\r\n          break;\r\n        case 'data':\r\n          this.queryParams.logType = 'DATA';\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 主要操作处理 */\r\n    handleMainAction(actionKey) {\r\n      switch (actionKey) {\r\n        case 'export':\r\n          this.handleExport();\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 批量操作处理 */\r\n    handleBatchAction(actionKey) {\r\n      switch (actionKey) {\r\n        case 'batchDelete':\r\n          this.handleDelete();\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n\r\n    /** 周期变化处理 */\r\n    handlePeriodChange(period) {\r\n      this.trendPeriod = period;\r\n      this.getTrendData();\r\n    },\r\n\r\n    /** 获取日志类型名称 */\r\n    getLogTypeName(type) {\r\n      const nameMap = {\r\n        'SYSTEM': '系统操作',\r\n        'BUSINESS': '业务操作',\r\n        'DATA': '数据变更'\r\n      };\r\n      return nameMap[type] || type;\r\n    },\r\n\r\n    /** 获取日志类型标签类型 */\r\n    getLogTypeTagType(type) {\r\n      const typeMap = {\r\n        'SYSTEM': 'primary',\r\n        'BUSINESS': 'success',\r\n        'DATA': 'warning'\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    /** 获取操作类型名称 */\r\n    getOperationTypeName(type) {\r\n      const nameMap = {\r\n        'INSERT': '新增',\r\n        'UPDATE': '修改',\r\n        'DELETE': '删除',\r\n        'SELECT': '查询',\r\n        'EXPORT': '导出',\r\n        'IMPORT': '导入',\r\n        'GRANT': '授权',\r\n        'OTHER': '其他'\r\n      };\r\n      return nameMap[type] || type;\r\n    },\r\n\r\n    /** 获取操作类型标签类型 */\r\n    getOperationTypeTagType(type) {\r\n      const typeMap = {\r\n        'INSERT': 'success',\r\n        'UPDATE': 'warning',\r\n        'DELETE': 'danger',\r\n        'SELECT': 'primary',\r\n        'EXPORT': 'info',\r\n        'IMPORT': 'info',\r\n        'GRANT': 'warning',\r\n        'OTHER': 'info'\r\n      };\r\n      return typeMap[type] || 'info';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n</style>"]}]}