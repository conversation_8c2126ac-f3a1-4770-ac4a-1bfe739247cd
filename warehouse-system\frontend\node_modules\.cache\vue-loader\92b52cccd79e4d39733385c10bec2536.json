{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\index.vue?vue&type=style&index=0&id=a83bd3b0&lang=scss&scoped=true", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\index.vue", "mtime": 1756537442794}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1755901395158}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1755901427908}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1755901408157}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1755901388804}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouaG9tZSB7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOwogIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4NHB4KTsKCiAgLndlbGNvbWUtY2FyZCB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCAjMTg5MGZmLCAjMzZjZmM5KTsKICAgIGNvbG9yOiAjZmZmOwoKICAgIC53ZWxjb21lLWhlYWRlciB7CiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgcGFkZGluZzogMjBweCAwOwoKICAgICAgaDEgewogICAgICAgIGZvbnQtc2l6ZTogMjhweDsKICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICB9CgogICAgICBwIHsKICAgICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgICAgb3BhY2l0eTogMC44OwogICAgICB9CiAgICB9CiAgfQoKICAuc3RhdGlzdGljcy1yb3cgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAuc3RhdGlzdGljcy1jYXJkIHsKICAgICAgaGVpZ2h0OiAxMjBweDsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgcGFkZGluZzogMjBweDsKICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAgIC5zdGF0aXN0aWNzLWljb24gewogICAgICAgIHdpZHRoOiA2MHB4OwogICAgICAgIGhlaWdodDogNjBweDsKICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjllYjsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxNXB4OwoKICAgICAgICBpIHsKICAgICAgICAgIGZvbnQtc2l6ZTogMzBweDsKICAgICAgICAgIGNvbG9yOiAjNjdDMjNBOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLnN0YXRpc3RpY3MtaW5mbyB7CiAgICAgICAgZmxleDogMTsKCiAgICAgICAgLnN0YXRpc3RpY3MtdGl0bGUgewogICAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgICAgY29sb3I6ICM2MDYyNjY7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICAgIH0KCiAgICAgICAgLnN0YXRpc3RpY3MtdmFsdWUgewogICAgICAgICAgZm9udC1zaXplOiAyNHB4OwogICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9CgogIC5jaGFydC1yb3cgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAuY2hhcnQtY2FyZCB7CiAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CgogICAgICAuY2hhcnQtY29udGFpbmVyIHsKICAgICAgICBoZWlnaHQ6IDMwMHB4OwogICAgICB9CiAgICB9CiAgfQoKICAuYWN0aXZpdHktcm93IHsKICAgIC5hY3Rpdml0eS1jYXJkIHsKICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAgIC5hY3Rpdml0eS1pdGVtIHsKICAgICAgICAuYWN0aXZpdHktdGl0bGUgewogICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA1cHg7CiAgICAgICAgfQoKICAgICAgICAuYWN0aXZpdHktZGVzYyB7CiAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICBjb2xvcjogIzkwOTM5OTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5hY3Rpdml0eS10aW1lIHsKICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgY29sb3I6ICM5MDkzOTk7CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAocA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"app-container home\">\n    <el-row :gutter=\"20\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"24\">\n        <el-card shadow=\"hover\" class=\"welcome-card\">\n          <div class=\"welcome-header\">\n            <h1>万裕物业仓库管理系统</h1>\n            <p>欢迎使用万裕物业仓库管理系统，高效管理您的仓库和物品信息</p>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 统计卡片 -->\n    <el-row :gutter=\"20\" class=\"statistics-row\">\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-s-home\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">仓库总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.warehouseCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-shopping-cart-full\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">物品总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.productCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-upload\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">入库总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.inboundCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-download\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">出库总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.outboundCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 图表区域 -->\n    <el-row :gutter=\"20\" class=\"chart-row\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"chart-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>库存统计</span>\n          </div>\n          <div class=\"chart-container\" ref=\"inventoryChart\"></div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"chart-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>出入库趋势</span>\n          </div>\n          <div class=\"chart-container\" ref=\"trendChart\"></div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 最近活动 -->\n    <el-row :gutter=\"20\" class=\"activity-row\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"activity-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>最近入库记录</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"viewMoreInbound\">查看更多</el-button>\n          </div>\n          <el-table :data=\"recentInbound\" style=\"width: 100%\" :show-header=\"false\">\n            <el-table-column width=\"50\">\n              <template slot-scope=\"scope\">\n                <el-avatar icon=\"el-icon-upload\" size=\"small\" style=\"background-color: #67C23A;\"></el-avatar>\n              </template>\n            </el-table-column>\n            <el-table-column>\n              <template slot-scope=\"scope\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-title\">{{ scope.row.productName }} 入库</div>\n                  <div class=\"activity-desc\">数量: {{ scope.row.quantity }} | 仓库: {{ scope.row.warehouseName }}</div>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column width=\"150\" align=\"right\">\n              <template slot-scope=\"scope\">\n                <span class=\"activity-time\">{{ scope.row.createTime }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"activity-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>最近出库记录</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"viewMoreOutbound\">查看更多</el-button>\n          </div>\n          <el-table :data=\"recentOutbound\" style=\"width: 100%\" :show-header=\"false\">\n            <el-table-column width=\"50\">\n              <template slot-scope=\"scope\">\n                <el-avatar icon=\"el-icon-download\" size=\"small\" style=\"background-color: #F56C6C;\"></el-avatar>\n              </template>\n            </el-table-column>\n            <el-table-column>\n              <template slot-scope=\"scope\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-title\">{{ scope.row.productName }} 出库</div>\n                  <div class=\"activity-desc\">数量: {{ scope.row.quantity }} | 仓库: {{ scope.row.warehouseName }}</div>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column width=\"150\" align=\"right\">\n              <template slot-scope=\"scope\">\n                <span class=\"activity-time\">{{ scope.row.createTime }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: \"Index\",\n  data() {\n    return {\n      // 统计数据\n      statisticsData: {\n        warehouseCount: 3,\n        productCount: 128,\n        inboundCount: 256,\n        outboundCount: 198\n      },\n      // 最近入库记录\n      recentInbound: [\n        { id: 1, productName: \"笔记本电脑\", quantity: 10, warehouseName: \"主仓库\", createTime: \"2023-05-16 14:30:00\" },\n        { id: 2, productName: \"办公桌\", quantity: 5, warehouseName: \"主仓库\", createTime: \"2023-05-16 11:20:00\" },\n        { id: 3, productName: \"打印机\", quantity: 3, warehouseName: \"备用仓库\", createTime: \"2023-05-15 16:45:00\" },\n        { id: 4, productName: \"投影仪\", quantity: 2, warehouseName: \"主仓库\", createTime: \"2023-05-15 09:15:00\" },\n        { id: 5, productName: \"办公椅\", quantity: 8, warehouseName: \"备用仓库\", createTime: \"2023-05-14 14:30:00\" }\n      ],\n      // 最近出库记录\n      recentOutbound: [\n        { id: 1, productName: \"笔记本电脑\", quantity: 2, warehouseName: \"主仓库\", createTime: \"2023-05-16 15:30:00\" },\n        { id: 2, productName: \"打印机\", quantity: 1, warehouseName: \"备用仓库\", createTime: \"2023-05-16 10:20:00\" },\n        { id: 3, productName: \"办公桌\", quantity: 2, warehouseName: \"主仓库\", createTime: \"2023-05-15 13:45:00\" },\n        { id: 4, productName: \"投影仪\", quantity: 1, warehouseName: \"主仓库\", createTime: \"2023-05-15 08:15:00\" },\n        { id: 5, productName: \"办公椅\", quantity: 3, warehouseName: \"备用仓库\", createTime: \"2023-05-14 11:30:00\" }\n      ],\n      // 图表实例\n      inventoryChart: null,\n      trendChart: null\n    };\n  },\n  mounted() {\n    this.initInventoryChart();\n    this.initTrendChart();\n    // 监听窗口大小变化，重新调整图表大小\n    window.addEventListener('resize', this.resizeCharts);\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    window.removeEventListener('resize', this.resizeCharts);\n    // 销毁图表实例\n    if (this.inventoryChart) {\n      this.inventoryChart.dispose();\n    }\n    if (this.trendChart) {\n      this.trendChart.dispose();\n    }\n  },\n  methods: {\n    // 初始化库存统计图表\n    initInventoryChart() {\n      this.inventoryChart = echarts.init(this.$refs.inventoryChart);\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 10,\n          data: ['电子产品', '办公用品', '生活用品', '其他']\n        },\n        series: [\n          {\n            name: '库存分布',\n            type: 'pie',\n            radius: ['50%', '70%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: false,\n              position: 'center'\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '18',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            data: [\n              { value: 45, name: '电子产品' },\n              { value: 30, name: '办公用品' },\n              { value: 15, name: '生活用品' },\n              { value: 10, name: '其他' }\n            ]\n          }\n        ]\n      };\n      this.inventoryChart.setOption(option);\n    },\n    // 初始化出入库趋势图表\n    initTrendChart() {\n      this.trendChart = echarts.init(this.$refs.trendChart);\n\n      // 获取真实的趋势数据\n      this.getStockTrendData().then(trendData => {\n        const dates = trendData.map(item => item.date);\n        const inData = trendData.map(item => item.inOperations || 0);\n        const outData = trendData.map(item => item.outOperations || 0);\n        const transferData = trendData.map(item => item.transferOperations || 0);\n        const purchaseData = trendData.map(item => item.purchaseOperations || 0);\n\n        const option = {\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'cross'\n            }\n          },\n          legend: {\n            data: ['入库', '出库', '调拨', '申购']\n          },\n          grid: {\n            left: '3%',\n            right: '4%',\n            bottom: '3%',\n            containLabel: true\n          },\n          xAxis: {\n            type: 'category',\n            boundaryGap: false,\n            data: dates.length > 0 ? dates : ['暂无数据'],\n            axisLabel: {\n              formatter: function(value) {\n                if (!value) return '';\n                return value.substring(5); // 只显示月-日\n              }\n            }\n          },\n          yAxis: {\n            type: 'value',\n            name: '操作次数'\n          },\n          series: [\n            {\n              name: '入库',\n              type: 'line',\n              data: inData.length > 0 ? inData : [0],\n              itemStyle: {\n                color: '#67C23A'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            },\n            {\n              name: '出库',\n              type: 'line',\n              data: outData.length > 0 ? outData : [0],\n              itemStyle: {\n                color: '#F56C6C'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            },\n            {\n              name: '调拨',\n              type: 'line',\n              data: transferData.length > 0 ? transferData : [0],\n              itemStyle: {\n                color: '#E6A23C'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            },\n            {\n              name: '申购',\n              type: 'line',\n              data: purchaseData.length > 0 ? purchaseData : [0],\n              itemStyle: {\n                color: '#409EFF'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            }\n          ]\n        };\n        this.trendChart.setOption(option);\n      }).catch(error => {\n        console.error('获取趋势数据失败:', error);\n        // 使用默认数据\n        const option = {\n          tooltip: {\n            trigger: 'axis'\n          },\n          legend: {\n            data: ['入库', '出库', '调拨', '申购']\n          },\n          grid: {\n            left: '3%',\n            right: '4%',\n            bottom: '3%',\n            containLabel: true\n          },\n          xAxis: {\n            type: 'category',\n            boundaryGap: false,\n            data: ['暂无数据']\n          },\n          yAxis: {\n            type: 'value'\n          },\n          series: [\n            {\n              name: '入库',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#67C23A' }\n            },\n            {\n              name: '出库',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#F56C6C' }\n            },\n            {\n              name: '调拨',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#E6A23C' }\n            },\n            {\n              name: '申购',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#409EFF' }\n            }\n          ]\n        };\n        this.trendChart.setOption(option);\n      });\n    },\n    // 重新调整图表大小\n    resizeCharts() {\n      if (this.inventoryChart) {\n        this.inventoryChart.resize();\n      }\n      if (this.trendChart) {\n        this.trendChart.resize();\n      }\n    },\n    // 查看更多入库记录\n    viewMoreInbound() {\n      this.$router.push('/inventory/in');\n    },\n    // 查看更多出库记录\n    viewMoreOutbound() {\n      this.$router.push('/inventory/out');\n    },\n\n    // 获取出入库趋势数据\n    getStockTrendData() {\n      return new Promise((resolve, reject) => {\n        // 调用库存日志趋势API\n        this.$http.get('/log/stock/trend', {\n          params: { days: 7 }\n        }).then(response => {\n          if (response.data && response.data.code === 200) {\n            resolve(response.data.data || []);\n          } else {\n            // 生成模拟数据\n            const mockData = this.generateMockTrendData();\n            resolve(mockData);\n          }\n        }).catch(error => {\n          console.error('获取趋势数据失败:', error);\n          // 生成模拟数据\n          const mockData = this.generateMockTrendData();\n          resolve(mockData);\n        });\n      });\n    },\n\n    // 生成模拟趋势数据\n    generateMockTrendData() {\n      const data = [];\n      const today = new Date();\n\n      for (let i = 6; i >= 0; i--) {\n        const date = new Date(today);\n        date.setDate(date.getDate() - i);\n        const dateStr = date.toISOString().substring(0, 10);\n\n        data.push({\n          date: dateStr,\n          inOperations: Math.floor(Math.random() * 20) + 5,\n          outOperations: Math.floor(Math.random() * 15) + 3,\n          transferOperations: Math.floor(Math.random() * 8) + 1,\n          purchaseOperations: Math.floor(Math.random() * 5) + 1\n        });\n      }\n\n      return data;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.home {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 84px);\n\n  .welcome-card {\n    margin-bottom: 20px;\n    background: linear-gradient(to right, #1890ff, #36cfc9);\n    color: #fff;\n\n    .welcome-header {\n      text-align: center;\n      padding: 20px 0;\n\n      h1 {\n        font-size: 28px;\n        margin-bottom: 10px;\n      }\n\n      p {\n        font-size: 16px;\n        opacity: 0.8;\n      }\n    }\n  }\n\n  .statistics-row {\n    margin-bottom: 20px;\n\n    .statistics-card {\n      height: 120px;\n      display: flex;\n      align-items: center;\n      padding: 20px;\n      margin-bottom: 20px;\n\n      .statistics-icon {\n        width: 60px;\n        height: 60px;\n        border-radius: 50%;\n        background-color: #f0f9eb;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 15px;\n\n        i {\n          font-size: 30px;\n          color: #67C23A;\n        }\n      }\n\n      .statistics-info {\n        flex: 1;\n\n        .statistics-title {\n          font-size: 16px;\n          color: #606266;\n          margin-bottom: 10px;\n        }\n\n        .statistics-value {\n          font-size: 24px;\n          font-weight: bold;\n          color: #303133;\n        }\n      }\n    }\n  }\n\n  .chart-row {\n    margin-bottom: 20px;\n\n    .chart-card {\n      margin-bottom: 20px;\n\n      .chart-container {\n        height: 300px;\n      }\n    }\n  }\n\n  .activity-row {\n    .activity-card {\n      margin-bottom: 20px;\n\n      .activity-item {\n        .activity-title {\n          font-size: 14px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n\n        .activity-desc {\n          font-size: 12px;\n          color: #909399;\n        }\n      }\n\n      .activity-time {\n        font-size: 12px;\n        color: #909399;\n      }\n    }\n  }\n}\n</style>"]}]}