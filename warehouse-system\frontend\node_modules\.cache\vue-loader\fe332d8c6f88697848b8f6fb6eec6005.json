{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\stock\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\stock\\index.vue", "mtime": 1756537609373}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inventory/stock", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索表单 -->\n    <SearchForm\n      :query-params=\"queryParams\"\n      :warehouse-options=\"warehouseOptions\"\n      :dict-options=\"dict.type.inventory_status\"\n      :show-search=\"showSearch\"\n      :is-mobile=\"isMobile\"\n      @query=\"handleQuery\"\n      @reset=\"resetQuery\"\n      @update:queryParams=\"queryParams = { ...queryParams, ...$event }\"\n    />\n\n    <!-- 操作按钮栏 -->\n    <ActionBar\n      :single=\"single\"\n      :multiple=\"multiple\"\n      :show-search.sync=\"showSearch\"\n      :is-mobile=\"isMobile\"\n      @add=\"handleAdd\"\n      @update=\"handleUpdate\"\n      @delete=\"handleDelete\"\n      @export=\"handleExport\"\n      @report=\"handleReport\"\n      @alert-report=\"handleAlertReport\"\n      @threshold=\"handleThreshold\"\n      @analysis=\"handleAnalysis\"\n      @product-stock=\"handleProductStock\"\n      @refresh=\"getList\"\n    />\n\n    <!-- 移动端库存列表 -->\n    <MobileStockList\n      v-if=\"isMobile\"\n      :data=\"stockList\"\n      :loading=\"loading\"\n      @view=\"handleView\"\n      @update=\"handleUpdate\"\n      @delete=\"handleDelete\"\n    />\n\n    <!-- 桌面端数据表格 -->\n    <DataTable\n      v-else\n      :data=\"stockList\"\n      :loading=\"loading\"\n      @selection-change=\"handleSelectionChange\"\n      @update=\"handleUpdate\"\n      @delete=\"handleDelete\"\n    />\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 表单对话框 -->\n    <FormDialog\n      :visible.sync=\"open\"\n      :title=\"title\"\n      :form=\"form\"\n      :rules=\"rules\"\n      :product-options=\"productOptions\"\n      :warehouse-options=\"warehouseOptions\"\n      :is-mobile=\"isMobile\"\n      @submit=\"submitForm\"\n      @cancel=\"cancel\"\n      @product-change=\"handleProductChange\"\n      ref=\"formDialog\"\n    />\n  </div>\n</template>\n\n<script>\nimport { listStock, getStock, delStock, addStock, updateStock } from \"@/api/inventory/stock\";\nimport { optionselect } from \"@/api/system/warehouse\";\nimport { listProduct } from \"@/api/product/info\";\nimport { \n  SearchForm, \n  ActionBar, \n  DataTable, \n  MobileStockList, \n  FormDialog \n} from './components';\nimport stockDataProcessingMixin from './mixins/stockDataProcessing.js';\n\nexport default {\n  name: \"Stock\",\n  components: {\n    SearchForm,\n    ActionBar,\n    DataTable,\n    MobileStockList,\n    FormDialog\n  },\n  mixins: [stockDataProcessingMixin],\n  dicts: ['inventory_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 库存表格数据\n      stockList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 仓库选项\n      warehouseOptions: [],\n      // 物品选项\n      productOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        productName: null,\n        productCode: null,\n        warehouseId: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        productId: [\n          { required: true, message: \"物品不能为空\", trigger: \"change\" }\n        ],\n        warehouseId: [\n          { required: true, message: \"仓库不能为空\", trigger: \"change\" }\n        ],\n        quantity: [\n          { required: true, message: \"库存数量不能为空\", trigger: \"blur\" }\n        ]\n      },\n      // 移动端搜索折叠面板\n      mobileSearchVisible: ['search']\n    };\n  },\n  computed: {\n    /** 是否为移动端 */\n    isMobile() {\n      return this.$store.getters.device === 'mobile'\n    }\n  },\n  created() {\n    this.getList();\n    this.getWarehouseOptions();\n    this.getProductOptions();\n    // 自动弹窗详情\n    if (this.$route.query.id) {\n      this.openStockDetailById(this.$route.query.id);\n    }\n  },\n  watch: {\n    '$route.query.id'(newId) {\n      if (newId) {\n        this.openStockDetailById(newId);\n      }\n    }\n  },\n  methods: {\n    /** 查询库存列表 */\n    getList() {\n      this.loading = true;\n      listStock(this.queryParams).then(response => {\n        this.stockList = this.processStockList(response.rows || []);\n        this.total = response.total || 0;\n        this.loading = false;\n      }).catch(error => {\n        this.loading = false;\n        this.handleApiError(error, '查询库存列表失败');\n      });\n    },\n    /** 获取仓库选项 */\n    getWarehouseOptions() {\n      optionselect().then(response => {\n        this.warehouseOptions = this.processWarehouseOptions(response.data || response.rows || []);\n      }).catch(error => {\n        this.handleApiError(error, '获取仓库选项失败');\n      });\n    },\n    /** 获取物品选项 */\n    getProductOptions() {\n      listProduct().then(response => {\n        this.productOptions = this.processProductOptions(response.rows || []);\n      }).catch(error => {\n        this.handleApiError(error, '获取物品选项失败');\n      });\n    },\n    // 物品选择事件\n    handleProductChange(value) {\n      // 可以在这里处理物品选择后的逻辑\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        inventoryId: null,\n        productId: null,\n        warehouseId: null,\n        quantity: 0,\n        minQuantity: 0,\n        maxQuantity: 0,\n        status: \"0\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.inventoryId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加库存信息\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const inventoryId = row.inventoryId || this.ids[0];\n      getStock(inventoryId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改库存信息\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs.formDialog.validate(valid => {\n        if (valid) {\n          // 使用混入中的验证方法\n          const validation = this.validateStockForm(this.form);\n          if (!validation.valid) {\n            this.$message.error(validation.errors[0]);\n            return;\n          }\n\n          if (this.form.inventoryId != null) {\n            updateStock(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            }).catch(error => {\n              this.handleApiError(error, \"修改失败\");\n            });\n          } else {\n            addStock(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            }).catch(error => {\n              this.handleApiError(error, \"新增失败\");\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      // 如果传入了row参数，说明是单行删除；否则是批量删除\n      const inventoryIds = row ? row.inventoryId : this.ids;\n      \n      // 检查是否有选中的数据\n      if (!inventoryIds || (Array.isArray(inventoryIds) && inventoryIds.length === 0)) {\n        this.$modal.msgError(\"请选择要删除的数据\");\n        return;\n      }\n      \n      const confirmText = row \n        ? `是否确认删除库存编号为\"${inventoryIds}\"的数据项？`\n        : `是否确认删除选中的${this.ids.length}条数据？`;\n        \n      this.$modal.confirm(confirmText).then(() => {\n        return delStock(inventoryIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.$modal.confirm('是否确认导出当前筛选条件下的库存数据？').then(() => {\n        const exportData = this.formatExportData(this.stockList);\n        const filename = this.generateExportFileName();\n        \n        this.download('api/v1/inventory/stocks/export', {\n          ...this.queryParams\n        }, filename);\n      }).catch(() => {});\n    },\n    /** 报表按钮操作 */\n    handleReport() {\n      this.$router.push({ path: \"/report/stock/index\" });\n    },\n    /** 预警报表按钮操作 */\n    handleAlertReport() {\n      this.$router.push({ path: \"/report/alert/index\" });\n    },\n    /** 阈值设置按钮操作 */\n    handleThreshold() {\n      this.$router.push({ path: \"/inventory/batch/threshold\" });\n    },\n    /** 高级分析按钮操作 */\n    handleAnalysis() {\n      this.$confirm('请选择分析类型', '高级分析', {\n        confirmButtonText: '周转率分析',\n        cancelButtonText: '价值分析',\n        type: 'info'\n      }).then(() => {\n        // 周转率分析\n        this.$router.push({ path: \"/report/analysis/turnover\" });\n      }).catch(() => {\n        // 价值分析\n        this.$router.push({ path: \"/report/analysis/value\" });\n      });\n    },\n    /** 物品库存按钮操作 */\n    handleProductStock() {\n      this.$router.push({ path: \"/inventory/stock/product\" });\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      this.$router.push({ path: \"/inventory/stock/detail/\" + row.inventoryId });\n    },\n    /** 审核按钮操作 */\n    handleAudit(row) {\n      this.$modal.msgError(\"审核功能待实现\");\n    },\n    /** 打印按钮操作 */\n    handlePrint(row) {\n      // 创建打印窗口\n      const printWindow = window.open('', '_blank', 'width=800,height=600');\n\n      // 生成打印内容\n      const printContent = this.generateStockPrintHTML(row);\n\n      printWindow.document.write(printContent);\n      printWindow.document.close();\n\n      // 等待内容加载完成后打印\n      printWindow.onload = () => {\n        setTimeout(() => {\n          printWindow.print();\n        }, 500);\n      };\n    },\n\n    /** 生成库存打印HTML */\n    generateStockPrintHTML(stockData) {\n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <title>库存信息打印</title>\n          <style>\n            body { font-family: \"Microsoft YaHei\", SimSun, sans-serif; font-size: 12pt; margin: 20px; }\n            .header { text-align: center; margin-bottom: 30px; }\n            .title { font-size: 18pt; font-weight: bold; margin-bottom: 10px; }\n            .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n            .info-table th, .info-table td { border: 1px solid #000; padding: 8px; text-align: left; }\n            .info-table th { background-color: #f5f5f5; font-weight: bold; }\n            .footer { margin-top: 30px; text-align: right; }\n            @media print {\n              body { margin: 0; }\n              .no-print { display: none; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <div class=\"title\">库存信息单</div>\n            <div>打印时间：${new Date().toLocaleString()}</div>\n          </div>\n\n          <table class=\"info-table\">\n            <tr>\n              <th width=\"15%\">物品编码</th>\n              <td width=\"35%\">${stockData.productCode || ''}</td>\n              <th width=\"15%\">物品名称</th>\n              <td width=\"35%\">${stockData.productName || ''}</td>\n            </tr>\n            <tr>\n              <th>仓库名称</th>\n              <td>${stockData.warehouseName || ''}</td>\n              <th>库位</th>\n              <td>${stockData.locationName || ''}</td>\n            </tr>\n            <tr>\n              <th>当前库存</th>\n              <td>${stockData.quantity || 0}</td>\n              <th>单位</th>\n              <td>${stockData.unit || ''}</td>\n            </tr>\n            <tr>\n              <th>单价</th>\n              <td>${stockData.price || 0}</td>\n              <th>总价值</th>\n              <td>${(stockData.quantity * stockData.price || 0).toFixed(2)}</td>\n            </tr>\n            <tr>\n              <th>最低库存</th>\n              <td>${stockData.minQuantity || 0}</td>\n              <th>最高库存</th>\n              <td>${stockData.maxQuantity || 0}</td>\n            </tr>\n            <tr>\n              <th>状态</th>\n              <td colspan=\"3\">${this.getDictLabel(this.dict.type.inventory_status, stockData.status)}</td>\n            </tr>\n          </table>\n\n          <div class=\"footer\">\n            <div>打印人：${this.$store.state.user.name}</div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    /** 自动弹窗详情 */\n    openStockDetailById(id) {\n      if (!id) return;\n      this.loading = true;\n      this.$api.getStock(id).then(res => {\n        this.form = res.data;\n        this.title = '库存详情';\n        this.open = true;\n      }).finally(() => {\n        this.loading = false;\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n\n/* 分页样式 */\n.el-pagination {\n  text-align: center;\n  margin-top: 20px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .app-container {\n    padding: 10px;\n  }\n}\n\n/* 加载状态优化 */\n.el-loading-mask {\n  background-color: rgba(255, 255, 255, 0.8);\n}\n</style>\n"]}]}