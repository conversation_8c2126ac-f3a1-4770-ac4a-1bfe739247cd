{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\index.vue", "mtime": 1756537442794}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\babel.config.js", "mtime": 1747299429443}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "name", "data", "statisticsData", "warehouseCount", "productCount", "inboundCount", "outboundCount", "recentInbound", "id", "productName", "quantity", "warehouseName", "createTime", "recentOutbound", "inventoryChart", "trendChart", "mounted", "initInventoryChart", "initTrendChart", "window", "addEventListener", "resi<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "dispose", "methods", "init", "$refs", "option", "tooltip", "trigger", "formatter", "legend", "orient", "left", "series", "type", "radius", "avoidLabelOverlap", "label", "show", "position", "emphasis", "fontSize", "fontWeight", "labelLine", "value", "setOption", "_this", "getStockTrendData", "then", "trendData", "dates", "map", "item", "date", "inData", "inOperations", "outData", "outOperations", "transferData", "transferOperations", "purchaseData", "purchaseOperations", "axisPointer", "grid", "right", "bottom", "containLabel", "xAxis", "boundaryGap", "length", "axisLabel", "substring", "yAxis", "itemStyle", "color", "areaStyle", "opacity", "catch", "error", "console", "resize", "viewMoreInbound", "$router", "push", "viewMoreOutbound", "_this2", "Promise", "resolve", "reject", "$http", "get", "params", "days", "response", "code", "mockData", "generateMockTrendData", "today", "Date", "i", "setDate", "getDate", "dateStr", "toISOString", "Math", "floor", "random"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container home\">\n    <el-row :gutter=\"20\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"24\">\n        <el-card shadow=\"hover\" class=\"welcome-card\">\n          <div class=\"welcome-header\">\n            <h1>万裕物业仓库管理系统</h1>\n            <p>欢迎使用万裕物业仓库管理系统，高效管理您的仓库和物品信息</p>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 统计卡片 -->\n    <el-row :gutter=\"20\" class=\"statistics-row\">\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-s-home\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">仓库总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.warehouseCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-shopping-cart-full\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">物品总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.productCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-upload\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">入库总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.inboundCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\">\n        <el-card shadow=\"hover\" class=\"statistics-card\">\n          <div class=\"statistics-icon\">\n            <i class=\"el-icon-download\"></i>\n          </div>\n          <div class=\"statistics-info\">\n            <div class=\"statistics-title\">出库总数</div>\n            <div class=\"statistics-value\">{{ statisticsData.outboundCount }}</div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 图表区域 -->\n    <el-row :gutter=\"20\" class=\"chart-row\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"chart-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>库存统计</span>\n          </div>\n          <div class=\"chart-container\" ref=\"inventoryChart\"></div>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"chart-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>出入库趋势</span>\n          </div>\n          <div class=\"chart-container\" ref=\"trendChart\"></div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 最近活动 -->\n    <el-row :gutter=\"20\" class=\"activity-row\">\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"activity-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>最近入库记录</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"viewMoreInbound\">查看更多</el-button>\n          </div>\n          <el-table :data=\"recentInbound\" style=\"width: 100%\" :show-header=\"false\">\n            <el-table-column width=\"50\">\n              <template slot-scope=\"scope\">\n                <el-avatar icon=\"el-icon-upload\" size=\"small\" style=\"background-color: #67C23A;\"></el-avatar>\n              </template>\n            </el-table-column>\n            <el-table-column>\n              <template slot-scope=\"scope\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-title\">{{ scope.row.productName }} 入库</div>\n                  <div class=\"activity-desc\">数量: {{ scope.row.quantity }} | 仓库: {{ scope.row.warehouseName }}</div>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column width=\"150\" align=\"right\">\n              <template slot-scope=\"scope\">\n                <span class=\"activity-time\">{{ scope.row.createTime }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-card>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\n        <el-card shadow=\"hover\" class=\"activity-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>最近出库记录</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"viewMoreOutbound\">查看更多</el-button>\n          </div>\n          <el-table :data=\"recentOutbound\" style=\"width: 100%\" :show-header=\"false\">\n            <el-table-column width=\"50\">\n              <template slot-scope=\"scope\">\n                <el-avatar icon=\"el-icon-download\" size=\"small\" style=\"background-color: #F56C6C;\"></el-avatar>\n              </template>\n            </el-table-column>\n            <el-table-column>\n              <template slot-scope=\"scope\">\n                <div class=\"activity-item\">\n                  <div class=\"activity-title\">{{ scope.row.productName }} 出库</div>\n                  <div class=\"activity-desc\">数量: {{ scope.row.quantity }} | 仓库: {{ scope.row.warehouseName }}</div>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column width=\"150\" align=\"right\">\n              <template slot-scope=\"scope\">\n                <span class=\"activity-time\">{{ scope.row.createTime }}</span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: \"Index\",\n  data() {\n    return {\n      // 统计数据\n      statisticsData: {\n        warehouseCount: 3,\n        productCount: 128,\n        inboundCount: 256,\n        outboundCount: 198\n      },\n      // 最近入库记录\n      recentInbound: [\n        { id: 1, productName: \"笔记本电脑\", quantity: 10, warehouseName: \"主仓库\", createTime: \"2023-05-16 14:30:00\" },\n        { id: 2, productName: \"办公桌\", quantity: 5, warehouseName: \"主仓库\", createTime: \"2023-05-16 11:20:00\" },\n        { id: 3, productName: \"打印机\", quantity: 3, warehouseName: \"备用仓库\", createTime: \"2023-05-15 16:45:00\" },\n        { id: 4, productName: \"投影仪\", quantity: 2, warehouseName: \"主仓库\", createTime: \"2023-05-15 09:15:00\" },\n        { id: 5, productName: \"办公椅\", quantity: 8, warehouseName: \"备用仓库\", createTime: \"2023-05-14 14:30:00\" }\n      ],\n      // 最近出库记录\n      recentOutbound: [\n        { id: 1, productName: \"笔记本电脑\", quantity: 2, warehouseName: \"主仓库\", createTime: \"2023-05-16 15:30:00\" },\n        { id: 2, productName: \"打印机\", quantity: 1, warehouseName: \"备用仓库\", createTime: \"2023-05-16 10:20:00\" },\n        { id: 3, productName: \"办公桌\", quantity: 2, warehouseName: \"主仓库\", createTime: \"2023-05-15 13:45:00\" },\n        { id: 4, productName: \"投影仪\", quantity: 1, warehouseName: \"主仓库\", createTime: \"2023-05-15 08:15:00\" },\n        { id: 5, productName: \"办公椅\", quantity: 3, warehouseName: \"备用仓库\", createTime: \"2023-05-14 11:30:00\" }\n      ],\n      // 图表实例\n      inventoryChart: null,\n      trendChart: null\n    };\n  },\n  mounted() {\n    this.initInventoryChart();\n    this.initTrendChart();\n    // 监听窗口大小变化，重新调整图表大小\n    window.addEventListener('resize', this.resizeCharts);\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    window.removeEventListener('resize', this.resizeCharts);\n    // 销毁图表实例\n    if (this.inventoryChart) {\n      this.inventoryChart.dispose();\n    }\n    if (this.trendChart) {\n      this.trendChart.dispose();\n    }\n  },\n  methods: {\n    // 初始化库存统计图表\n    initInventoryChart() {\n      this.inventoryChart = echarts.init(this.$refs.inventoryChart);\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 10,\n          data: ['电子产品', '办公用品', '生活用品', '其他']\n        },\n        series: [\n          {\n            name: '库存分布',\n            type: 'pie',\n            radius: ['50%', '70%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: false,\n              position: 'center'\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '18',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            data: [\n              { value: 45, name: '电子产品' },\n              { value: 30, name: '办公用品' },\n              { value: 15, name: '生活用品' },\n              { value: 10, name: '其他' }\n            ]\n          }\n        ]\n      };\n      this.inventoryChart.setOption(option);\n    },\n    // 初始化出入库趋势图表\n    initTrendChart() {\n      this.trendChart = echarts.init(this.$refs.trendChart);\n\n      // 获取真实的趋势数据\n      this.getStockTrendData().then(trendData => {\n        const dates = trendData.map(item => item.date);\n        const inData = trendData.map(item => item.inOperations || 0);\n        const outData = trendData.map(item => item.outOperations || 0);\n        const transferData = trendData.map(item => item.transferOperations || 0);\n        const purchaseData = trendData.map(item => item.purchaseOperations || 0);\n\n        const option = {\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'cross'\n            }\n          },\n          legend: {\n            data: ['入库', '出库', '调拨', '申购']\n          },\n          grid: {\n            left: '3%',\n            right: '4%',\n            bottom: '3%',\n            containLabel: true\n          },\n          xAxis: {\n            type: 'category',\n            boundaryGap: false,\n            data: dates.length > 0 ? dates : ['暂无数据'],\n            axisLabel: {\n              formatter: function(value) {\n                if (!value) return '';\n                return value.substring(5); // 只显示月-日\n              }\n            }\n          },\n          yAxis: {\n            type: 'value',\n            name: '操作次数'\n          },\n          series: [\n            {\n              name: '入库',\n              type: 'line',\n              data: inData.length > 0 ? inData : [0],\n              itemStyle: {\n                color: '#67C23A'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            },\n            {\n              name: '出库',\n              type: 'line',\n              data: outData.length > 0 ? outData : [0],\n              itemStyle: {\n                color: '#F56C6C'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            },\n            {\n              name: '调拨',\n              type: 'line',\n              data: transferData.length > 0 ? transferData : [0],\n              itemStyle: {\n                color: '#E6A23C'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            },\n            {\n              name: '申购',\n              type: 'line',\n              data: purchaseData.length > 0 ? purchaseData : [0],\n              itemStyle: {\n                color: '#409EFF'\n              },\n              areaStyle: {\n                opacity: 0.3\n              }\n            }\n          ]\n        };\n        this.trendChart.setOption(option);\n      }).catch(error => {\n        console.error('获取趋势数据失败:', error);\n        // 使用默认数据\n        const option = {\n          tooltip: {\n            trigger: 'axis'\n          },\n          legend: {\n            data: ['入库', '出库', '调拨', '申购']\n          },\n          grid: {\n            left: '3%',\n            right: '4%',\n            bottom: '3%',\n            containLabel: true\n          },\n          xAxis: {\n            type: 'category',\n            boundaryGap: false,\n            data: ['暂无数据']\n          },\n          yAxis: {\n            type: 'value'\n          },\n          series: [\n            {\n              name: '入库',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#67C23A' }\n            },\n            {\n              name: '出库',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#F56C6C' }\n            },\n            {\n              name: '调拨',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#E6A23C' }\n            },\n            {\n              name: '申购',\n              type: 'line',\n              data: [0],\n              itemStyle: { color: '#409EFF' }\n            }\n          ]\n        };\n        this.trendChart.setOption(option);\n      });\n    },\n    // 重新调整图表大小\n    resizeCharts() {\n      if (this.inventoryChart) {\n        this.inventoryChart.resize();\n      }\n      if (this.trendChart) {\n        this.trendChart.resize();\n      }\n    },\n    // 查看更多入库记录\n    viewMoreInbound() {\n      this.$router.push('/inventory/in');\n    },\n    // 查看更多出库记录\n    viewMoreOutbound() {\n      this.$router.push('/inventory/out');\n    },\n\n    // 获取出入库趋势数据\n    getStockTrendData() {\n      return new Promise((resolve, reject) => {\n        // 调用库存日志趋势API\n        this.$http.get('/log/stock/trend', {\n          params: { days: 7 }\n        }).then(response => {\n          if (response.data && response.data.code === 200) {\n            resolve(response.data.data || []);\n          } else {\n            // 生成模拟数据\n            const mockData = this.generateMockTrendData();\n            resolve(mockData);\n          }\n        }).catch(error => {\n          console.error('获取趋势数据失败:', error);\n          // 生成模拟数据\n          const mockData = this.generateMockTrendData();\n          resolve(mockData);\n        });\n      });\n    },\n\n    // 生成模拟趋势数据\n    generateMockTrendData() {\n      const data = [];\n      const today = new Date();\n\n      for (let i = 6; i >= 0; i--) {\n        const date = new Date(today);\n        date.setDate(date.getDate() - i);\n        const dateStr = date.toISOString().substring(0, 10);\n\n        data.push({\n          date: dateStr,\n          inOperations: Math.floor(Math.random() * 20) + 5,\n          outOperations: Math.floor(Math.random() * 15) + 3,\n          transferOperations: Math.floor(Math.random() * 8) + 1,\n          purchaseOperations: Math.floor(Math.random() * 5) + 1\n        });\n      }\n\n      return data;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.home {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 84px);\n\n  .welcome-card {\n    margin-bottom: 20px;\n    background: linear-gradient(to right, #1890ff, #36cfc9);\n    color: #fff;\n\n    .welcome-header {\n      text-align: center;\n      padding: 20px 0;\n\n      h1 {\n        font-size: 28px;\n        margin-bottom: 10px;\n      }\n\n      p {\n        font-size: 16px;\n        opacity: 0.8;\n      }\n    }\n  }\n\n  .statistics-row {\n    margin-bottom: 20px;\n\n    .statistics-card {\n      height: 120px;\n      display: flex;\n      align-items: center;\n      padding: 20px;\n      margin-bottom: 20px;\n\n      .statistics-icon {\n        width: 60px;\n        height: 60px;\n        border-radius: 50%;\n        background-color: #f0f9eb;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 15px;\n\n        i {\n          font-size: 30px;\n          color: #67C23A;\n        }\n      }\n\n      .statistics-info {\n        flex: 1;\n\n        .statistics-title {\n          font-size: 16px;\n          color: #606266;\n          margin-bottom: 10px;\n        }\n\n        .statistics-value {\n          font-size: 24px;\n          font-weight: bold;\n          color: #303133;\n        }\n      }\n    }\n  }\n\n  .chart-row {\n    margin-bottom: 20px;\n\n    .chart-card {\n      margin-bottom: 20px;\n\n      .chart-container {\n        height: 300px;\n      }\n    }\n  }\n\n  .activity-row {\n    .activity-card {\n      margin-bottom: 20px;\n\n      .activity-item {\n        .activity-title {\n          font-size: 14px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n\n        .activity-desc {\n          font-size: 12px;\n          color: #909399;\n        }\n      }\n\n      .activity-time {\n        font-size: 12px;\n        color: #909399;\n      }\n    }\n  }\n}\n</style>"], "mappings": ";;;;;;;;;;;;AAgJA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,cAAA;QACAC,cAAA;QACAC,YAAA;QACAC,YAAA;QACAC,aAAA;MACA;MACA;MACAC,aAAA,GACA;QAAAC,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,GACA;QAAAJ,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,GACA;QAAAJ,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,GACA;QAAAJ,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,GACA;QAAAJ,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,EACA;MACA;MACAC,cAAA,GACA;QAAAL,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,GACA;QAAAJ,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,GACA;QAAAJ,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,GACA;QAAAJ,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,GACA;QAAAJ,EAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,UAAA;MAAA,EACA;MACA;MACAE,cAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,cAAA;IACA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,YAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,YAAA;IACA;IACA,SAAAP,cAAA;MACA,KAAAA,cAAA,CAAAU,OAAA;IACA;IACA,SAAAT,UAAA;MACA,KAAAA,UAAA,CAAAS,OAAA;IACA;EACA;EACAC,OAAA;IACA;IACAR,kBAAA,WAAAA,mBAAA;MACA,KAAAH,cAAA,GAAAjB,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAb,cAAA;MACA,IAAAc,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAC,MAAA;UACAC,MAAA;UACAC,IAAA;UACAjC,IAAA;QACA;QACAkC,MAAA,GACA;UACAnC,IAAA;UACAoC,IAAA;UACAC,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACAH,KAAA;cACAC,IAAA;cACAG,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAL,IAAA;UACA;UACAvC,IAAA,GACA;YAAA6C,KAAA;YAAA9C,IAAA;UAAA,GACA;YAAA8C,KAAA;YAAA9C,IAAA;UAAA,GACA;YAAA8C,KAAA;YAAA9C,IAAA;UAAA,GACA;YAAA8C,KAAA;YAAA9C,IAAA;UAAA;QAEA;MAEA;MACA,KAAAc,cAAA,CAAAiC,SAAA,CAAAnB,MAAA;IACA;IACA;IACAV,cAAA,WAAAA,eAAA;MAAA,IAAA8B,KAAA;MACA,KAAAjC,UAAA,GAAAlB,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAZ,UAAA;;MAEA;MACA,KAAAkC,iBAAA,GAAAC,IAAA,WAAAC,SAAA;QACA,IAAAC,KAAA,GAAAD,SAAA,CAAAE,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,IAAA;QAAA;QACA,IAAAC,MAAA,GAAAL,SAAA,CAAAE,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAG,YAAA;QAAA;QACA,IAAAC,OAAA,GAAAP,SAAA,CAAAE,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAK,aAAA;QAAA;QACA,IAAAC,YAAA,GAAAT,SAAA,CAAAE,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAO,kBAAA;QAAA;QACA,IAAAC,YAAA,GAAAX,SAAA,CAAAE,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAS,kBAAA;QAAA;QAEA,IAAAnC,MAAA;UACAC,OAAA;YACAC,OAAA;YACAkC,WAAA;cACA5B,IAAA;YACA;UACA;UACAJ,MAAA;YACA/B,IAAA;UACA;UACAgE,IAAA;YACA/B,IAAA;YACAgC,KAAA;YACAC,MAAA;YACAC,YAAA;UACA;UACAC,KAAA;YACAjC,IAAA;YACAkC,WAAA;YACArE,IAAA,EAAAmD,KAAA,CAAAmB,MAAA,OAAAnB,KAAA;YACAoB,SAAA;cACAzC,SAAA,WAAAA,UAAAe,KAAA;gBACA,KAAAA,KAAA;gBACA,OAAAA,KAAA,CAAA2B,SAAA;cACA;YACA;UACA;UACAC,KAAA;YACAtC,IAAA;YACApC,IAAA;UACA;UACAmC,MAAA,GACA;YACAnC,IAAA;YACAoC,IAAA;YACAnC,IAAA,EAAAuD,MAAA,CAAAe,MAAA,OAAAf,MAAA;YACAmB,SAAA;cACAC,KAAA;YACA;YACAC,SAAA;cACAC,OAAA;YACA;UACA,GACA;YACA9E,IAAA;YACAoC,IAAA;YACAnC,IAAA,EAAAyD,OAAA,CAAAa,MAAA,OAAAb,OAAA;YACAiB,SAAA;cACAC,KAAA;YACA;YACAC,SAAA;cACAC,OAAA;YACA;UACA,GACA;YACA9E,IAAA;YACAoC,IAAA;YACAnC,IAAA,EAAA2D,YAAA,CAAAW,MAAA,OAAAX,YAAA;YACAe,SAAA;cACAC,KAAA;YACA;YACAC,SAAA;cACAC,OAAA;YACA;UACA,GACA;YACA9E,IAAA;YACAoC,IAAA;YACAnC,IAAA,EAAA6D,YAAA,CAAAS,MAAA,OAAAT,YAAA;YACAa,SAAA;cACAC,KAAA;YACA;YACAC,SAAA;cACAC,OAAA;YACA;UACA;QAEA;QACA9B,KAAA,CAAAjC,UAAA,CAAAgC,SAAA,CAAAnB,MAAA;MACA,GAAAmD,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA;QACA,IAAApD,MAAA;UACAC,OAAA;YACAC,OAAA;UACA;UACAE,MAAA;YACA/B,IAAA;UACA;UACAgE,IAAA;YACA/B,IAAA;YACAgC,KAAA;YACAC,MAAA;YACAC,YAAA;UACA;UACAC,KAAA;YACAjC,IAAA;YACAkC,WAAA;YACArE,IAAA;UACA;UACAyE,KAAA;YACAtC,IAAA;UACA;UACAD,MAAA,GACA;YACAnC,IAAA;YACAoC,IAAA;YACAnC,IAAA;YACA0E,SAAA;cAAAC,KAAA;YAAA;UACA,GACA;YACA5E,IAAA;YACAoC,IAAA;YACAnC,IAAA;YACA0E,SAAA;cAAAC,KAAA;YAAA;UACA,GACA;YACA5E,IAAA;YACAoC,IAAA;YACAnC,IAAA;YACA0E,SAAA;cAAAC,KAAA;YAAA;UACA,GACA;YACA5E,IAAA;YACAoC,IAAA;YACAnC,IAAA;YACA0E,SAAA;cAAAC,KAAA;YAAA;UACA;QAEA;QACA5B,KAAA,CAAAjC,UAAA,CAAAgC,SAAA,CAAAnB,MAAA;MACA;IACA;IACA;IACAP,YAAA,WAAAA,aAAA;MACA,SAAAP,cAAA;QACA,KAAAA,cAAA,CAAAoE,MAAA;MACA;MACA,SAAAnE,UAAA;QACA,KAAAA,UAAA,CAAAmE,MAAA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAF,OAAA,CAAAC,IAAA;IACA;IAEA;IACApC,iBAAA,WAAAA,kBAAA;MAAA,IAAAsC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA;QACAH,MAAA,CAAAI,KAAA,CAAAC,GAAA;UACAC,MAAA;YAAAC,IAAA;UAAA;QACA,GAAA5C,IAAA,WAAA6C,QAAA;UACA,IAAAA,QAAA,CAAA9F,IAAA,IAAA8F,QAAA,CAAA9F,IAAA,CAAA+F,IAAA;YACAP,OAAA,CAAAM,QAAA,CAAA9F,IAAA,CAAAA,IAAA;UACA;YACA;YACA,IAAAgG,QAAA,GAAAV,MAAA,CAAAW,qBAAA;YACAT,OAAA,CAAAQ,QAAA;UACA;QACA,GAAAlB,KAAA,WAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;UACA;UACA,IAAAiB,QAAA,GAAAV,MAAA,CAAAW,qBAAA;UACAT,OAAA,CAAAQ,QAAA;QACA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAA;MACA,IAAAjG,IAAA;MACA,IAAAkG,KAAA,OAAAC,IAAA;MAEA,SAAAC,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAA9C,IAAA,OAAA6C,IAAA,CAAAD,KAAA;QACA5C,IAAA,CAAA+C,OAAA,CAAA/C,IAAA,CAAAgD,OAAA,KAAAF,CAAA;QACA,IAAAG,OAAA,GAAAjD,IAAA,CAAAkD,WAAA,GAAAhC,SAAA;QAEAxE,IAAA,CAAAoF,IAAA;UACA9B,IAAA,EAAAiD,OAAA;UACA/C,YAAA,EAAAiD,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;UACAjD,aAAA,EAAA+C,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;UACA/C,kBAAA,EAAA6C,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;UACA7C,kBAAA,EAAA2C,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACA;MACA;MAEA,OAAA3G,IAAA;IACA;EACA;AACA", "ignoreList": []}]}