{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\in\\print.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\in\\print.vue", "mtime": 1756537496223}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\babel.config.js", "mtime": 1747299429443}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_in", "require", "_userUtils", "name", "dicts", "data", "loading", "isPrinting", "printSettingsVisible", "inData", "inCode", "warehouseName", "inTime", "inType", "status", "createBy", "createByName", "auditBy", "auditByName", "remark", "details", "inId", "printSettings", "marginTop", "marginRight", "marginBottom", "marginLeft", "fontSize", "orientation", "paperSize", "created", "$route", "params", "id", "query", "console", "log", "$message", "error", "getInData", "methods", "_this", "getInventoryIn", "then", "response", "userNames", "filter", "length", "getBatchUserRealNames", "nameMap", "catch", "handlePrintSettings", "savePrintSettings", "localStorage", "setItem", "JSON", "stringify", "success", "loadPrintSettings", "savedSettings", "getItem", "parse", "formatAmount", "row", "column", "cellValue", "parseFloat", "toFixed", "handlePrint", "_this2", "warning", "$nextTick", "printWindow", "window", "open", "printContent", "generatePrintHTML", "document", "write", "close", "onload", "setTimeout", "print", "_this3", "detailsHTML", "for<PERSON>ach", "item", "index", "concat", "productCode", "productName", "quantity", "price", "amount", "pageSizeStyle", "parseTime", "getStatusName", "getInTypeName", "statusDict", "dict", "type", "inventory_in_status", "statusItem", "find", "value", "label", "typeDict", "inventory_in_type", "typeItem", "handleClose", "$router", "go", "mounted"], "sources": ["src/views/inventory/in/print.vue"], "sourcesContent": ["<template>\n  <div class=\"print-container\">\n    <!-- 打印预览界面 -->\n    <div class=\"print-header\" v-if=\"!isPrinting\">\n      <el-button type=\"primary\" icon=\"el-icon-printer\" size=\"small\" @click=\"handlePrint\">\n        打印入库单\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-setting\" size=\"small\" @click=\"handlePrintSettings\">\n        打印设置\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-close\" size=\"small\" @click=\"handleClose\">\n        关闭\n      </el-button>\n    </div>\n    \n    <!-- 打印设置对话框 -->\n    <el-dialog title=\"打印设置\" :visible.sync=\"printSettingsVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"printSettingsForm\" :model=\"printSettings\" label-width=\"100px\">\n        <el-form-item label=\"页面边距\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginTop\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginRight\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginBottom\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginLeft\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n          </el-row>\n          <div class=\"margin-labels\">\n            <span>上</span>\n            <span>右</span>\n            <span>下</span>\n            <span>左</span>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"字体大小\">\n          <el-slider v-model=\"printSettings.fontSize\" :min=\"8\" :max=\"20\" show-input></el-slider>\n        </el-form-item>\n        \n        <el-form-item label=\"页面方向\">\n          <el-radio-group v-model=\"printSettings.orientation\">\n            <el-radio label=\"portrait\">纵向</el-radio>\n            <el-radio label=\"landscape\">横向</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"纸张大小\">\n          <el-select v-model=\"printSettings.paperSize\" placeholder=\"请选择纸张大小\">\n            <el-option label=\"A4\" value=\"A4\"></el-option>\n            <el-option label=\"A5\" value=\"A5\"></el-option>\n            <el-option label=\"B5\" value=\"B5\"></el-option>\n            <el-option label=\"Letter\" value=\"letter\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"printSettingsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"savePrintSettings\">确 定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 打印内容 -->\n    <div class=\"print-content\" id=\"printContent\">\n      <div class=\"in-header\">\n        <h2 class=\"title\">入库单</h2>\n        <div class=\"header-info\">\n          <div class=\"header-item\">\n            <span class=\"label\">入库单号：</span>\n            <span class=\"value\">{{ inData.inCode }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">入库日期：</span>\n            <span class=\"value\">{{ parseTime(inData.inTime) }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">状态：</span>\n            <span class=\"value\">\n              <dict-tag :options=\"dict.type.inventory_in_status\" :value=\"inData.status\" />\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"in-info\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">仓库名称：</span>\n              <span class=\"value\">{{ inData.warehouseName }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">入库类型：</span>\n              <span class=\"value\">\n                <dict-tag :options=\"dict.type.inventory_in_type\" :value=\"inData.inType\" />\n              </span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">制单人：</span>\n              <span class=\"value\">{{ inData.createByName || inData.createBy }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">审核人：</span>\n              <span class=\"value\">{{ inData.auditByName || inData.auditBy || '未审核' }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <div class=\"info-item\">\n              <span class=\"label\">备注：</span>\n              <span class=\"value\">{{ inData.remark }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <div class=\"in-details\">\n        <h3>入库物品信息</h3>\n        <el-table :data=\"inData.details\" class=\"detail-table\" show-summary>\n          <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n          <el-table-column label=\"物品编码\" prop=\"productCode\" />\n          <el-table-column label=\"物品名称\" prop=\"productName\" />\n          <el-table-column label=\"入库数量\" prop=\"quantity\" />\n          <el-table-column label=\"单价\" prop=\"price\" :formatter=\"formatAmount\" />\n          <el-table-column label=\"金额\" prop=\"amount\" :formatter=\"formatAmount\" />\n          <el-table-column label=\"备注\" prop=\"remark\" />\n        </el-table>\n      </div>\n      \n      <div class=\"in-footer\">\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">仓库管理员：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">日期：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getInventoryIn } from \"@/api/inventory/in\";\nimport { getBatchUserRealNames } from \"@/utils/userUtils\";\n\nexport default {\n  name: \"InPrint\",\n  dicts: ['inventory_in_type', 'inventory_in_status'],\n  data() {\n    return {\n      loading: false,\n      isPrinting: false,\n      printSettingsVisible: false,\n      inData: {\n        inCode: \"\",\n        warehouseName: \"\",\n        inTime: null,\n        inType: \"\",\n        status: \"\",\n        createBy: \"\",\n        createByName: \"\",\n        auditBy: \"\",\n        auditByName: \"\",\n        remark: \"\",\n        details: []\n      },\n      inId: null,\n      printSettings: {\n        marginTop: 15,\n        marginRight: 15,\n        marginBottom: 15,\n        marginLeft: 15,\n        fontSize: 12,\n        orientation: 'portrait',\n        paperSize: 'A4'\n      }\n    };\n  },\n  created() {\n    this.inId = this.$route.params.inId || this.$route.params.id || this.$route.query.id;\n    console.log('入库单打印页面初始化，入库单ID:', this.inId);\n    if (!this.inId) {\n      this.$message.error('缺少入库单ID参数');\n      return;\n    }\n    this.getInData();\n  },\n  methods: {\n    /** 获取入库单信息 */\n    getInData() {\n      this.loading = true;\n      getInventoryIn(this.inId).then(response => {\n        this.inData = response.data;\n        \n        // 获取用户真实姓名\n        const userNames = [\n          this.inData.createBy,\n          this.inData.auditBy\n        ].filter(name => name); // 过滤空值\n\n        if (userNames.length > 0) {\n          getBatchUserRealNames(userNames).then(nameMap => {\n            // 更新用户真实姓名\n            if (this.inData.createBy && nameMap[this.inData.createBy]) {\n              this.inData.createByName = nameMap[this.inData.createBy];\n            }\n            if (this.inData.auditBy && nameMap[this.inData.auditBy]) {\n              this.inData.auditByName = nameMap[this.inData.auditBy];\n            }\n          });\n        }\n        \n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n        this.$message.error(\"获取入库单信息失败\");\n      });\n    },\n    \n    /** 处理打印设置 */\n    handlePrintSettings() {\n      this.printSettingsVisible = true;\n    },\n    \n    /** 保存打印设置 */\n    savePrintSettings() {\n      // 保存到本地存储\n      localStorage.setItem('inPrintSettings', JSON.stringify(this.printSettings));\n      this.printSettingsVisible = false;\n      this.$message.success('打印设置已保存');\n    },\n    \n    /** 加载打印设置 */\n    loadPrintSettings() {\n      const savedSettings = localStorage.getItem('inPrintSettings');\n      if (savedSettings) {\n        this.printSettings = JSON.parse(savedSettings);\n      }\n    },\n    \n    /** 格式化金额 */\n    formatAmount(row, column, cellValue) {\n      if (!cellValue) return '0.00';\n      return parseFloat(cellValue).toFixed(2);\n    },\n    \n    /** 打印 */\n    handlePrint() {\n      this.isPrinting = true;\n      \n      // 确保数据已加载\n      if (!this.inData.inCode) {\n        this.$message.warning('数据还在加载中，请稍后再试');\n        this.isPrinting = false;\n        return;\n      }\n      \n      this.$nextTick(() => {\n        // 创建新窗口进行打印\n        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');\n        \n        // 生成打印内容\n        const printContent = this.generatePrintHTML();\n        \n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        \n        // 等待内容加载完成后打印\n        printWindow.onload = () => {\n          setTimeout(() => {\n            printWindow.print();\n            // 打印完成后不自动关闭窗口，让用户手动关闭\n            this.isPrinting = false;\n          }, 500);\n        };\n      });\n    },\n    \n    /** 生成打印页面HTML */\n    generatePrintHTML() {\n      const details = this.inData.details || [];\n      let detailsHTML = '';\n      \n      details.forEach((item, index) => {\n        detailsHTML += `\n          <tr>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${index + 1}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productCode || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productName || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.quantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${this.formatAmount(null, null, item.price)}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${this.formatAmount(null, null, item.amount)}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.remark || ''}</td>\n          </tr>\n        `;\n      });\n      \n      // 根据设置确定页面方向和纸张大小\n      let pageSizeStyle = '';\n      if (this.printSettings.orientation === 'landscape') {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;\n      } else {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;\n      }\n      \n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>入库单打印</title>\n          <style>\n            @page { \n              ${pageSizeStyle}\n              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; \n            }\n            \n            * {\n              box-sizing: border-box;\n            }\n            \n            html, body { \n              font-family: \"Microsoft YaHei\", SimSun, sans-serif; \n              font-size: ${this.printSettings.fontSize}pt; \n              color: #000; \n              background: #fff; \n              margin: 0; \n              padding: ${this.printSettings.marginTop/3}mm;\n              width: 100%;\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n            \n            .container {\n              width: 100%;\n              max-width: 1000px;\n              margin: 0 auto;\n            }\n            \n            .print-container {\n              padding: 20px;\n              background-color: #fff;\n              color: #000;\n            }\n            \n            .print-content {\n              max-width: 1000px;\n              margin: 0 auto;\n              font-family: \"SimSun\", \"宋体\", serif;\n              color: #000;\n            }\n            \n            .in-header {\n              text-align: center;\n              margin-bottom: 30px;\n              border-bottom: 2px solid #000;\n              padding-bottom: 10px;\n            }\n            \n            .in-header .title {\n              font-size: ${this.printSettings.fontSize + 8}pt;\n              font-weight: bold;\n              margin: 0 0 20px 0;\n            }\n            \n            .header-info {\n              display: flex;\n              justify-content: space-between;\n            }\n            \n            .header-item {\n              display: flex;\n              align-items: center;\n            }\n            \n            .header-item .label {\n              font-weight: bold;\n              margin-right: 5px;\n            }\n            \n            .in-info {\n              margin-bottom: 30px;\n            }\n            \n            .info-item {\n              margin-bottom: 15px;\n              display: flex;\n              align-items: flex-start;\n            }\n            \n            .info-item .label {\n              font-weight: bold;\n              min-width: 100px;\n              flex-shrink: 0;\n            }\n            \n            .in-details h3,\n            .approval-info h3 {\n              font-size: ${this.printSettings.fontSize + 4}pt;\n              margin: 0 0 15px 0;\n              border-left: 4px solid #409EFF;\n              padding-left: 10px;\n            }\n            \n            table {\n              width: 100%;\n              border-collapse: collapse;\n              margin-bottom: 30px;\n              table-layout: fixed;\n            }\n            \n            th {\n              background-color: #f5f5f5;\n              color: #000;\n              font-weight: bold;\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n            }\n            \n            td {\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n            \n            .in-footer {\n              margin: 30px 0;\n            }\n            \n            .footer-item {\n              margin-bottom: 15px;\n            }\n            \n            .signature-line {\n              display: inline-block;\n              width: 80px;\n              height: 1px;\n              background-color: #000;\n              margin-left: 10px;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"print-container\">\n            <div class=\"print-content\">\n              <div class=\"in-header\">\n                <h2 class=\"title\">入库单</h2>\n                <div class=\"header-info\">\n                  <div class=\"header-item\">\n                    <span class=\"label\">入库单号：</span>\n                    <span class=\"value\">${this.inData.inCode || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">入库日期：</span>\n                    <span class=\"value\">${this.parseTime(this.inData.inTime) || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">状态：</span>\n                    <span class=\"value\">${this.getStatusName(this.inData.status) || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"in-info\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库名称：</span>\n                    <span>${this.inData.warehouseName || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">入库类型：</span>\n                    <span>${this.getInTypeName(this.inData.inType) || ''}</span>\n                  </div>\n                </div>\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">制单人：</span>\n                    <span>${this.inData.createByName || this.inData.createBy || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人：</span>\n                    <span>${this.inData.auditByName || this.inData.auditBy || '未审核'}</span>\n                  </div>\n                </div>\n                \n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 100%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">备注：</span>\n                    <span>${this.inData.remark || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"in-details\">\n                <h3>入库物品信息</h3>\n                <table>\n                  <thead>\n                    <tr>\n                      <th>序号</th>\n                      <th>物品编码</th>\n                      <th>物品名称</th>\n                      <th>入库数量</th>\n                      <th>单价</th>\n                      <th>金额</th>\n                      <th>备注</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    ${detailsHTML}\n                  </tbody>\n                </table>\n              </div>\n              \n              <div class=\"in-footer\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库管理员：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">日期：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    \n    /** 获取状态名称 */\n    getStatusName(status) {\n      // 使用与屏幕预览相同的字典映射方式\n      const statusDict = this.dict.type.inventory_in_status || [];\n      const statusItem = statusDict.find(item => item.value === status);\n      return statusItem ? statusItem.label : '';\n    },\n    \n    /** 获取入库类型名称 */\n    getInTypeName(inType) {\n      // 使用与屏幕预览相同的字典映射方式\n      const typeDict = this.dict.type.inventory_in_type || [];\n      const typeItem = typeDict.find(item => item.value === inType);\n      return typeItem ? typeItem.label : '';\n    },\n    \n    /** 关闭 */\n    handleClose() {\n      this.$router.go(-1);\n    }\n  },\n  mounted() {\n    // 加载打印设置\n    this.loadPrintSettings();\n  }\n};\n</script>\n\n<style scoped>\n.print-container {\n  padding: 20px;\n  background-color: #fff;\n  color: #000;\n}\n\n.print-header {\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.print-content {\n  max-width: 1000px;\n  margin: 0 auto;\n  font-family: \"SimSun\", \"宋体\", serif;\n  color: #000;\n}\n\n.in-header {\n  text-align: center;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #000;\n  padding-bottom: 10px;\n}\n\n.in-header .title {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0 0 20px 0;\n}\n\n.header-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.header-item {\n  display: flex;\n  align-items: center;\n}\n\n.header-item .label {\n  font-weight: bold;\n}\n\n.in-info {\n  margin-bottom: 30px;\n}\n\n.info-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.info-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.detail-table {\n  margin-bottom: 30px;\n}\n\n.detail-table ::v-deep .el-table__header th {\n  background-color: #f5f5f5;\n  color: #000;\n}\n\n.detail-table ::v-deep .el-table__row td {\n  color: #000;\n}\n\n.in-details h3,\n.approval-info h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n  border-left: 4px solid #409EFF;\n  padding-left: 10px;\n}\n\n.in-footer {\n  margin: 30px 0;\n}\n\n.footer-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-end;\n  height: 30px;\n}\n\n.footer-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.signature-line {\n  display: inline-block;\n  width: 80px;\n  height: 1px;\n  background-color: #000;\n  margin-left: 5px;\n  position: relative;\n  bottom: 3px;\n}\n\n.margin-labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;AA8KA,IAAAA,GAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,oBAAA;MACAC,MAAA;QACAC,MAAA;QACAC,aAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,YAAA;QACAC,OAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACAC,IAAA;MACAC,aAAA;QACAC,SAAA;QACAC,WAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAT,IAAA,QAAAU,MAAA,CAAAC,MAAA,CAAAX,IAAA,SAAAU,MAAA,CAAAC,MAAA,CAAAC,EAAA,SAAAF,MAAA,CAAAG,KAAA,CAAAD,EAAA;IACAE,OAAA,CAAAC,GAAA,2BAAAf,IAAA;IACA,UAAAA,IAAA;MACA,KAAAgB,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACA,cACAD,SAAA,WAAAA,UAAA;MAAA,IAAAE,KAAA;MACA,KAAAnC,OAAA;MACA,IAAAoC,kBAAA,OAAArB,IAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAhC,MAAA,GAAAmC,QAAA,CAAAvC,IAAA;;QAEA;QACA,IAAAwC,SAAA,IACAJ,KAAA,CAAAhC,MAAA,CAAAM,QAAA,EACA0B,KAAA,CAAAhC,MAAA,CAAAQ,OAAA,CACA,CAAA6B,MAAA,WAAA3C,IAAA;UAAA,OAAAA,IAAA;QAAA;;QAEA,IAAA0C,SAAA,CAAAE,MAAA;UACA,IAAAC,gCAAA,EAAAH,SAAA,EAAAF,IAAA,WAAAM,OAAA;YACA;YACA,IAAAR,KAAA,CAAAhC,MAAA,CAAAM,QAAA,IAAAkC,OAAA,CAAAR,KAAA,CAAAhC,MAAA,CAAAM,QAAA;cACA0B,KAAA,CAAAhC,MAAA,CAAAO,YAAA,GAAAiC,OAAA,CAAAR,KAAA,CAAAhC,MAAA,CAAAM,QAAA;YACA;YACA,IAAA0B,KAAA,CAAAhC,MAAA,CAAAQ,OAAA,IAAAgC,OAAA,CAAAR,KAAA,CAAAhC,MAAA,CAAAQ,OAAA;cACAwB,KAAA,CAAAhC,MAAA,CAAAS,WAAA,GAAA+B,OAAA,CAAAR,KAAA,CAAAhC,MAAA,CAAAQ,OAAA;YACA;UACA;QACA;QAEAwB,KAAA,CAAAnC,OAAA;MACA,GAAA4C,KAAA;QACAT,KAAA,CAAAnC,OAAA;QACAmC,KAAA,CAAAJ,QAAA,CAAAC,KAAA;MACA;IACA;IAEA,aACAa,mBAAA,WAAAA,oBAAA;MACA,KAAA3C,oBAAA;IACA;IAEA,aACA4C,iBAAA,WAAAA,kBAAA;MACA;MACAC,YAAA,CAAAC,OAAA,oBAAAC,IAAA,CAAAC,SAAA,MAAAlC,aAAA;MACA,KAAAd,oBAAA;MACA,KAAA6B,QAAA,CAAAoB,OAAA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAA;MACA,IAAAC,aAAA,GAAAN,YAAA,CAAAO,OAAA;MACA,IAAAD,aAAA;QACA,KAAArC,aAAA,GAAAiC,IAAA,CAAAM,KAAA,CAAAF,aAAA;MACA;IACA;IAEA,YACAG,YAAA,WAAAA,aAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA;MACA,KAAAA,SAAA;MACA,OAAAC,UAAA,CAAAD,SAAA,EAAAE,OAAA;IACA;IAEA,SACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAA9D,UAAA;;MAEA;MACA,UAAAE,MAAA,CAAAC,MAAA;QACA,KAAA2B,QAAA,CAAAiC,OAAA;QACA,KAAA/D,UAAA;QACA;MACA;MAEA,KAAAgE,SAAA;QACA;QACA,IAAAC,WAAA,GAAAC,MAAA,CAAAC,IAAA;;QAEA;QACA,IAAAC,YAAA,GAAAN,MAAA,CAAAO,iBAAA;QAEAJ,WAAA,CAAAK,QAAA,CAAAC,KAAA,CAAAH,YAAA;QACAH,WAAA,CAAAK,QAAA,CAAAE,KAAA;;QAEA;QACAP,WAAA,CAAAQ,MAAA;UACAC,UAAA;YACAT,WAAA,CAAAU,KAAA;YACA;YACAb,MAAA,CAAA9D,UAAA;UACA;QACA;MACA;IACA;IAEA,iBACAqE,iBAAA,WAAAA,kBAAA;MAAA,IAAAO,MAAA;MACA,IAAA/D,OAAA,QAAAX,MAAA,CAAAW,OAAA;MACA,IAAAgE,WAAA;MAEAhE,OAAA,CAAAiE,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACAH,WAAA,6GAAAI,MAAA,CAEAD,KAAA,qGAAAC,MAAA,CACAF,IAAA,CAAAG,WAAA,uGAAAD,MAAA,CACAF,IAAA,CAAAI,WAAA,uGAAAF,MAAA,CACAF,IAAA,CAAAK,QAAA,uGAAAH,MAAA,CACAL,MAAA,CAAArB,YAAA,aAAAwB,IAAA,CAAAM,KAAA,kGAAAJ,MAAA,CACAL,MAAA,CAAArB,YAAA,aAAAwB,IAAA,CAAAO,MAAA,kGAAAL,MAAA,CACAF,IAAA,CAAAnE,MAAA,2CAEA;MACA;;MAEA;MACA,IAAA2E,aAAA;MACA,SAAAxE,aAAA,CAAAM,WAAA;QACAkE,aAAA,YAAAN,MAAA,MAAAlE,aAAA,CAAAO,SAAA;MACA;QACAiE,aAAA,YAAAN,MAAA,MAAAlE,aAAA,CAAAO,SAAA;MACA;MAEA,+SAAA2D,MAAA,CASAM,aAAA,8BAAAN,MAAA,CACA,KAAAlE,aAAA,CAAAC,SAAA,SAAAiE,MAAA,MAAAlE,aAAA,CAAAE,WAAA,SAAAgE,MAAA,MAAAlE,aAAA,CAAAG,YAAA,SAAA+D,MAAA,MAAAlE,aAAA,CAAAI,UAAA,wPAAA8D,MAAA,CASA,KAAAlE,aAAA,CAAAK,QAAA,6HAAA6D,MAAA,CAIA,KAAAlE,aAAA,CAAAC,SAAA,07BAAAiE,MAAA,CAiCA,KAAAlE,aAAA,CAAAK,QAAA,u+BAAA6D,MAAA,CAsCA,KAAAlE,aAAA,CAAAK,QAAA,ipDAAA6D,MAAA,CAuDA,KAAA/E,MAAA,CAAAC,MAAA,yNAAA8E,MAAA,CAIA,KAAAO,SAAA,MAAAtF,MAAA,CAAAG,MAAA,8MAAA4E,MAAA,CAIA,KAAAQ,aAAA,MAAAvF,MAAA,CAAAK,MAAA,kaAAA0E,MAAA,CASA,KAAA/E,MAAA,CAAAE,aAAA,uPAAA6E,MAAA,CAIA,KAAAS,aAAA,MAAAxF,MAAA,CAAAI,MAAA,+UAAA2E,MAAA,CAMA,KAAA/E,MAAA,CAAAO,YAAA,SAAAP,MAAA,CAAAM,QAAA,iPAAAyE,MAAA,CAIA,KAAA/E,MAAA,CAAAS,WAAA,SAAAT,MAAA,CAAAQ,OAAA,8VAAAuE,MAAA,CAOA,KAAA/E,MAAA,CAAAU,MAAA,yuBAAAqE,MAAA,CAoBAJ,WAAA;IA0BA;IAEA,aACAY,aAAA,WAAAA,cAAAlF,MAAA;MACA;MACA,IAAAoF,UAAA,QAAAC,IAAA,CAAAC,IAAA,CAAAC,mBAAA;MACA,IAAAC,UAAA,GAAAJ,UAAA,CAAAK,IAAA,WAAAjB,IAAA;QAAA,OAAAA,IAAA,CAAAkB,KAAA,KAAA1F,MAAA;MAAA;MACA,OAAAwF,UAAA,GAAAA,UAAA,CAAAG,KAAA;IACA;IAEA,eACAR,aAAA,WAAAA,cAAApF,MAAA;MACA;MACA,IAAA6F,QAAA,QAAAP,IAAA,CAAAC,IAAA,CAAAO,iBAAA;MACA,IAAAC,QAAA,GAAAF,QAAA,CAAAH,IAAA,WAAAjB,IAAA;QAAA,OAAAA,IAAA,CAAAkB,KAAA,KAAA3F,MAAA;MAAA;MACA,OAAA+F,QAAA,GAAAA,QAAA,CAAAH,KAAA;IACA;IAEA,SACAI,WAAA,WAAAA,YAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAtD,iBAAA;EACA;AACA", "ignoreList": []}]}