{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\CKGLXT\\warehouse-system\\frontend\\src\\api\\monitor\\operlog.js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\api\\monitor\\operlog.js", "mtime": 1753120480964}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\babel.config.js", "mtime": 1747299429443}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1755901392719}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9DS0dMWFQvd2FyZWhvdXNlLXN5c3RlbS9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuY2xlYW5PcGVybG9nID0gY2xlYW5PcGVybG9nOwpleHBvcnRzLmRlbE9wZXJsb2cgPSBkZWxPcGVybG9nOwpleHBvcnRzLmxpc3QgPSBsaXN0Owp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i5pON5L2c5pel5b+X5YiX6KGoCmZ1bmN0aW9uIGxpc3QocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9tb25pdG9yL29wZXJsb2cnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5Yig6Zmk5pON5L2c5pel5b+XCmZ1bmN0aW9uIGRlbE9wZXJsb2cob3BlcklkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICIvbW9uaXRvci9vcGVybG9nLyIuY29uY2F0KG9wZXJJZCksCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOa4heepuuaTjeS9nOaXpeW/lwpmdW5jdGlvbiBjbGVhbk9wZXJsb2coKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbW9uaXRvci9vcGVybG9nL2NsZWFuJywKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "list", "query", "request", "url", "method", "params", "delOperlog", "operId", "concat", "cleanOperlog"], "sources": ["C:/CKGLXT/warehouse-system/frontend/src/api/monitor/operlog.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询操作日志列表\r\nexport function list(query) {\r\n  return request({\r\n    url: '/monitor/operlog',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 删除操作日志\r\nexport function delOperlog(operId) {\r\n  return request({\r\n    url: `/monitor/operlog/${operId}`,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 清空操作日志\r\nexport function cleanOperlog() {\r\n  return request({\r\n    url: '/monitor/operlog/clean',\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,IAAIA,CAACC,KAAK,EAAE;EAC1B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,MAAM,EAAE;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,sBAAAK,MAAA,CAAsBD,MAAM,CAAE;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}