{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\out\\print.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\out\\print.vue", "mtime": 1756537509311}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\babel.config.js", "mtime": 1747299429443}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_out", "require", "_userUtils", "name", "dicts", "data", "loading", "isPrinting", "printSettingsVisible", "outData", "outCode", "warehouseName", "outTime", "outType", "status", "createBy", "createByName", "auditBy", "auditByName", "remark", "details", "outId", "printSettings", "marginTop", "marginRight", "marginBottom", "marginLeft", "fontSize", "orientation", "paperSize", "created", "$route", "params", "id", "query", "console", "log", "$message", "error", "getOutData", "methods", "_this", "getInventoryOut", "then", "response", "userNames", "filter", "length", "getBatchUserRealNames", "nameMap", "catch", "handlePrintSettings", "savePrintSettings", "localStorage", "setItem", "JSON", "stringify", "success", "loadPrintSettings", "savedSettings", "getItem", "parse", "formatAmount", "row", "column", "cellValue", "parseFloat", "toFixed", "handlePrint", "_this2", "warning", "$nextTick", "printWindow", "window", "open", "printContent", "generatePrintHTML", "document", "write", "close", "onload", "setTimeout", "print", "_this3", "detailsHTML", "for<PERSON>ach", "item", "index", "concat", "productCode", "productName", "quantity", "price", "amount", "pageSizeStyle", "parseTime", "getStatusName", "getOutTypeName", "statusDict", "dict", "type", "inventory_out_status", "statusItem", "find", "value", "label", "typeDict", "inventory_out_type", "typeItem", "handleClose", "$router", "go", "mounted"], "sources": ["src/views/inventory/out/print.vue"], "sourcesContent": ["<template>\n  <div class=\"print-container\">\n    <!-- 打印预览界面 -->\n    <div class=\"print-header\" v-if=\"!isPrinting\">\n      <el-button type=\"primary\" icon=\"el-icon-printer\" size=\"small\" @click=\"handlePrint\">\n        打印出库单\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-setting\" size=\"small\" @click=\"handlePrintSettings\">\n        打印设置\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-close\" size=\"small\" @click=\"handleClose\">\n        关闭\n      </el-button>\n    </div>\n    \n    <!-- 打印设置对话框 -->\n    <el-dialog title=\"打印设置\" :visible.sync=\"printSettingsVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"printSettingsForm\" :model=\"printSettings\" label-width=\"100px\">\n        <el-form-item label=\"页面边距\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginTop\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginRight\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginBottom\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginLeft\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n          </el-row>\n          <div class=\"margin-labels\">\n            <span>上</span>\n            <span>右</span>\n            <span>下</span>\n            <span>左</span>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"字体大小\">\n          <el-slider v-model=\"printSettings.fontSize\" :min=\"8\" :max=\"20\" show-input></el-slider>\n        </el-form-item>\n        \n        <el-form-item label=\"页面方向\">\n          <el-radio-group v-model=\"printSettings.orientation\">\n            <el-radio label=\"portrait\">纵向</el-radio>\n            <el-radio label=\"landscape\">横向</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"纸张大小\">\n          <el-select v-model=\"printSettings.paperSize\" placeholder=\"请选择纸张大小\">\n            <el-option label=\"A4\" value=\"A4\"></el-option>\n            <el-option label=\"A5\" value=\"A5\"></el-option>\n            <el-option label=\"B5\" value=\"B5\"></el-option>\n            <el-option label=\"Letter\" value=\"letter\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"printSettingsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"savePrintSettings\">确 定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 打印内容 -->\n    <div class=\"print-content\" id=\"printContent\">\n      <div class=\"out-header\">\n        <h2 class=\"title\">出库单</h2>\n        <div class=\"header-info\">\n          <div class=\"header-item\">\n            <span class=\"label\">出库单号：</span>\n            <span class=\"value\">{{ outData.outCode }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">出库日期：</span>\n            <span class=\"value\">{{ parseTime(outData.outTime) }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">状态：</span>\n            <span class=\"value\">\n              <dict-tag :options=\"dict.type.inventory_out_status\" :value=\"outData.status\" />\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"out-info\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">仓库名称：</span>\n              <span class=\"value\">{{ outData.warehouseName }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">出库类型：</span>\n              <span class=\"value\">\n                <dict-tag :options=\"dict.type.inventory_out_type\" :value=\"outData.outType\" />\n              </span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">制单人：</span>\n              <span class=\"value\">{{ outData.createByName || outData.createBy }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">审核人：</span>\n              <span class=\"value\">{{ outData.auditByName || outData.auditBy || '未审核' }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <div class=\"info-item\">\n              <span class=\"label\">备注：</span>\n              <span class=\"value\">{{ outData.remark }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <div class=\"out-details\">\n        <h3>出库物品信息</h3>\n        <el-table :data=\"outData.details\" class=\"detail-table\" show-summary>\n          <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n          <el-table-column label=\"物品编码\" prop=\"productCode\" />\n          <el-table-column label=\"物品名称\" prop=\"productName\" />\n          <el-table-column label=\"出库数量\" prop=\"quantity\" />\n          <el-table-column label=\"单价\" prop=\"price\" :formatter=\"formatAmount\" />\n          <el-table-column label=\"金额\" prop=\"amount\" :formatter=\"formatAmount\" />\n          <el-table-column label=\"备注\" prop=\"remark\" />\n        </el-table>\n      </div>\n      \n      <div class=\"out-footer\">\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">仓库管理员：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">日期：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getInventoryOut } from \"@/api/inventory/out\";\nimport { getBatchUserRealNames } from \"@/utils/userUtils\";\n\nexport default {\n  name: \"OutPrint\",\n  dicts: ['inventory_out_type', 'inventory_out_status'],\n  data() {\n    return {\n      loading: false,\n      isPrinting: false,\n      printSettingsVisible: false,\n      outData: {\n        outCode: \"\",\n        warehouseName: \"\",\n        outTime: null,\n        outType: \"\",\n        status: \"\",\n        createBy: \"\",\n        createByName: \"\",\n        auditBy: \"\",\n        auditByName: \"\",\n        remark: \"\",\n        details: []\n      },\n      outId: null,\n      printSettings: {\n        marginTop: 15,\n        marginRight: 15,\n        marginBottom: 15,\n        marginLeft: 15,\n        fontSize: 12,\n        orientation: 'portrait',\n        paperSize: 'A4'\n      }\n    };\n  },\n  created() {\n    this.outId = this.$route.params.outId || this.$route.params.id || this.$route.query.id;\n    console.log('出库单打印页面初始化，出库单ID:', this.outId);\n    if (!this.outId) {\n      this.$message.error('缺少出库单ID参数');\n      return;\n    }\n    this.getOutData();\n  },\n  methods: {\n    /** 获取出库单信息 */\n    getOutData() {\n      this.loading = true;\n      getInventoryOut(this.outId).then(response => {\n        this.outData = response.data;\n        \n        // 获取用户真实姓名\n        const userNames = [\n          this.outData.createBy,\n          this.outData.auditBy\n        ].filter(name => name); // 过滤空值\n\n        if (userNames.length > 0) {\n          getBatchUserRealNames(userNames).then(nameMap => {\n            // 更新用户真实姓名\n            if (this.outData.createBy && nameMap[this.outData.createBy]) {\n              this.outData.createByName = nameMap[this.outData.createBy];\n            }\n            if (this.outData.auditBy && nameMap[this.outData.auditBy]) {\n              this.outData.auditByName = nameMap[this.outData.auditBy];\n            }\n          });\n        }\n        \n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n        this.$message.error(\"获取出库单信息失败\");\n      });\n    },\n    \n    /** 处理打印设置 */\n    handlePrintSettings() {\n      this.printSettingsVisible = true;\n    },\n    \n    /** 保存打印设置 */\n    savePrintSettings() {\n      // 保存到本地存储\n      localStorage.setItem('outPrintSettings', JSON.stringify(this.printSettings));\n      this.printSettingsVisible = false;\n      this.$message.success('打印设置已保存');\n    },\n    \n    /** 加载打印设置 */\n    loadPrintSettings() {\n      const savedSettings = localStorage.getItem('outPrintSettings');\n      if (savedSettings) {\n        this.printSettings = JSON.parse(savedSettings);\n      }\n    },\n    \n    /** 格式化金额 */\n    formatAmount(row, column, cellValue) {\n      if (!cellValue) return '0.00';\n      return parseFloat(cellValue).toFixed(2);\n    },\n    \n    /** 打印 */\n    handlePrint() {\n      this.isPrinting = true;\n      \n      // 确保数据已加载\n      if (!this.outData.outCode) {\n        this.$message.warning('数据还在加载中，请稍后再试');\n        this.isPrinting = false;\n        return;\n      }\n      \n      this.$nextTick(() => {\n        // 创建新窗口进行打印\n        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');\n        \n        // 生成打印内容\n        const printContent = this.generatePrintHTML();\n        \n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        \n        // 等待内容加载完成后打印\n        printWindow.onload = () => {\n          setTimeout(() => {\n            printWindow.print();\n            // 打印完成后不自动关闭窗口，让用户手动关闭\n            this.isPrinting = false;\n          }, 500);\n        };\n      });\n    },\n    \n    /** 生成打印页面HTML */\n    generatePrintHTML() {\n      const details = this.outData.details || [];\n      let detailsHTML = '';\n      \n      details.forEach((item, index) => {\n        detailsHTML += `\n          <tr>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${index + 1}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productCode || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productName || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.quantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${this.formatAmount(null, null, item.price)}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${this.formatAmount(null, null, item.amount)}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.remark || ''}</td>\n          </tr>\n        `;\n      });\n      \n      // 根据设置确定页面方向和纸张大小\n      let pageSizeStyle = '';\n      if (this.printSettings.orientation === 'landscape') {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;\n      } else {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;\n      }\n      \n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>出库单打印</title>\n          <style>\n            @page { \n              ${pageSizeStyle}\n              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; \n            }\n            \n            * {\n              box-sizing: border-box;\n            }\n            \n            html, body { \n              font-family: \"Microsoft YaHei\", SimSun, sans-serif; \n              font-size: ${this.printSettings.fontSize}pt; \n              color: #000; \n              background: #fff; \n              margin: 0; \n              padding: ${this.printSettings.marginTop/3}mm;\n              width: 100%;\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n            \n            .container {\n              width: 100%;\n              max-width: 1000px;\n              margin: 0 auto;\n            }\n            \n            .print-container {\n              padding: 20px;\n              background-color: #fff;\n              color: #000;\n            }\n            \n            .print-content {\n              max-width: 1000px;\n              margin: 0 auto;\n              font-family: \"SimSun\", \"宋体\", serif;\n              color: #000;\n            }\n            \n            .out-header {\n              text-align: center;\n              margin-bottom: 30px;\n              border-bottom: 2px solid #000;\n              padding-bottom: 10px;\n            }\n            \n            .out-header .title {\n              font-size: ${this.printSettings.fontSize + 8}pt;\n              font-weight: bold;\n              margin: 0 0 20px 0;\n            }\n            \n            .header-info {\n              display: flex;\n              justify-content: space-between;\n            }\n            \n            .header-item {\n              display: flex;\n              align-items: center;\n            }\n            \n            .header-item .label {\n              font-weight: bold;\n              margin-right: 5px;\n            }\n            \n            .out-info {\n              margin-bottom: 30px;\n            }\n            \n            .info-item {\n              margin-bottom: 15px;\n              display: flex;\n              align-items: flex-start;\n            }\n            \n            .info-item .label {\n              font-weight: bold;\n              min-width: 100px;\n              flex-shrink: 0;\n            }\n            \n            .out-details h3,\n            .approval-info h3 {\n              font-size: ${this.printSettings.fontSize + 4}pt;\n              margin: 0 0 15px 0;\n              border-left: 4px solid #409EFF;\n              padding-left: 10px;\n            }\n            \n            table {\n              width: 100%;\n              border-collapse: collapse;\n              margin-bottom: 30px;\n              table-layout: fixed;\n            }\n            \n            th {\n              background-color: #f5f5f5;\n              color: #000;\n              font-weight: bold;\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n            }\n            \n            td {\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n            \n            .out-footer {\n              margin: 30px 0;\n            }\n            \n            .footer-item {\n              margin-bottom: 15px;\n            }\n            \n            .signature-line {\n              display: inline-block;\n              width: 80px;\n              height: 1px;\n              background-color: #000;\n              margin-left: 10px;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"print-container\">\n            <div class=\"print-content\">\n              <div class=\"out-header\">\n                <h2 class=\"title\">出库单</h2>\n                <div class=\"header-info\">\n                  <div class=\"header-item\">\n                    <span class=\"label\">出库单号：</span>\n                    <span class=\"value\">${this.outData.outCode || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">出库日期：</span>\n                    <span class=\"value\">${this.parseTime(this.outData.outTime) || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">状态：</span>\n                    <span class=\"value\">${this.getStatusName(this.outData.status) || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"out-info\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库名称：</span>\n                    <span>${this.outData.warehouseName || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">出库类型：</span>\n                    <span>${this.getOutTypeName(this.outData.outType) || ''}</span>\n                  </div>\n                </div>\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">制单人：</span>\n                    <span>${this.outData.createByName || this.outData.createBy || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人：</span>\n                    <span>${this.outData.auditByName || this.outData.auditBy || '未审核'}</span>\n                  </div>\n                </div>\n                \n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 100%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">备注：</span>\n                    <span>${this.outData.remark || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"out-details\">\n                <h3>出库物品信息</h3>\n                <table>\n                  <thead>\n                    <tr>\n                      <th>序号</th>\n                      <th>物品编码</th>\n                      <th>物品名称</th>\n                      <th>出库数量</th>\n                      <th>单价</th>\n                      <th>金额</th>\n                      <th>备注</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    ${detailsHTML}\n                  </tbody>\n                </table>\n              </div>\n              \n              <div class=\"out-footer\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库管理员：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">日期：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    \n    /** 获取状态名称 */\n    getStatusName(status) {\n      // 使用与屏幕预览相同的字典映射方式\n      const statusDict = this.dict.type.inventory_out_status || [];\n      const statusItem = statusDict.find(item => item.value === status);\n      return statusItem ? statusItem.label : '';\n    },\n    \n    /** 获取出库类型名称 */\n    getOutTypeName(outType) {\n      // 使用与屏幕预览相同的字典映射方式\n      const typeDict = this.dict.type.inventory_out_type || [];\n      const typeItem = typeDict.find(item => item.value === outType);\n      return typeItem ? typeItem.label : '';\n    },\n    \n    /** 关闭 */\n    handleClose() {\n      this.$router.go(-1);\n    }\n  },\n  mounted() {\n    // 加载打印设置\n    this.loadPrintSettings();\n  }\n};\n</script>\n\n<style scoped>\n.print-container {\n  padding: 20px;\n  background-color: #fff;\n  color: #000;\n}\n\n.print-header {\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.print-content {\n  max-width: 1000px;\n  margin: 0 auto;\n  font-family: \"SimSun\", \"宋体\", serif;\n  color: #000;\n}\n\n.out-header {\n  text-align: center;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #000;\n  padding-bottom: 10px;\n}\n\n.out-header .title {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0 0 20px 0;\n}\n\n.header-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.header-item {\n  display: flex;\n  align-items: center;\n}\n\n.header-item .label {\n  font-weight: bold;\n}\n\n.out-info {\n  margin-bottom: 30px;\n}\n\n.info-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.info-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.detail-table {\n  margin-bottom: 30px;\n}\n\n.detail-table ::v-deep .el-table__header th {\n  background-color: #f5f5f5;\n  color: #000;\n}\n\n.detail-table ::v-deep .el-table__row td {\n  color: #000;\n}\n\n.out-details h3,\n.approval-info h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n  border-left: 4px solid #409EFF;\n  padding-left: 10px;\n}\n\n.out-footer {\n  margin: 30px 0;\n}\n\n.footer-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-end;\n  height: 30px;\n}\n\n.footer-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.signature-line {\n  display: inline-block;\n  width: 80px;\n  height: 1px;\n  background-color: #000;\n  margin-left: 5px;\n  position: relative;\n  bottom: 3px;\n}\n\n.margin-labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;AA8KA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,oBAAA;MACAC,OAAA;QACAC,OAAA;QACAC,aAAA;QACAC,OAAA;QACAC,OAAA;QACAC,MAAA;QACAC,QAAA;QACAC,YAAA;QACAC,OAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACAC,KAAA;MACAC,aAAA;QACAC,SAAA;QACAC,WAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAT,KAAA,QAAAU,MAAA,CAAAC,MAAA,CAAAX,KAAA,SAAAU,MAAA,CAAAC,MAAA,CAAAC,EAAA,SAAAF,MAAA,CAAAG,KAAA,CAAAD,EAAA;IACAE,OAAA,CAAAC,GAAA,2BAAAf,KAAA;IACA,UAAAA,KAAA;MACA,KAAAgB,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACA,cACAD,UAAA,WAAAA,WAAA;MAAA,IAAAE,KAAA;MACA,KAAAnC,OAAA;MACA,IAAAoC,oBAAA,OAAArB,KAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAhC,OAAA,GAAAmC,QAAA,CAAAvC,IAAA;;QAEA;QACA,IAAAwC,SAAA,IACAJ,KAAA,CAAAhC,OAAA,CAAAM,QAAA,EACA0B,KAAA,CAAAhC,OAAA,CAAAQ,OAAA,CACA,CAAA6B,MAAA,WAAA3C,IAAA;UAAA,OAAAA,IAAA;QAAA;;QAEA,IAAA0C,SAAA,CAAAE,MAAA;UACA,IAAAC,gCAAA,EAAAH,SAAA,EAAAF,IAAA,WAAAM,OAAA;YACA;YACA,IAAAR,KAAA,CAAAhC,OAAA,CAAAM,QAAA,IAAAkC,OAAA,CAAAR,KAAA,CAAAhC,OAAA,CAAAM,QAAA;cACA0B,KAAA,CAAAhC,OAAA,CAAAO,YAAA,GAAAiC,OAAA,CAAAR,KAAA,CAAAhC,OAAA,CAAAM,QAAA;YACA;YACA,IAAA0B,KAAA,CAAAhC,OAAA,CAAAQ,OAAA,IAAAgC,OAAA,CAAAR,KAAA,CAAAhC,OAAA,CAAAQ,OAAA;cACAwB,KAAA,CAAAhC,OAAA,CAAAS,WAAA,GAAA+B,OAAA,CAAAR,KAAA,CAAAhC,OAAA,CAAAQ,OAAA;YACA;UACA;QACA;QAEAwB,KAAA,CAAAnC,OAAA;MACA,GAAA4C,KAAA;QACAT,KAAA,CAAAnC,OAAA;QACAmC,KAAA,CAAAJ,QAAA,CAAAC,KAAA;MACA;IACA;IAEA,aACAa,mBAAA,WAAAA,oBAAA;MACA,KAAA3C,oBAAA;IACA;IAEA,aACA4C,iBAAA,WAAAA,kBAAA;MACA;MACAC,YAAA,CAAAC,OAAA,qBAAAC,IAAA,CAAAC,SAAA,MAAAlC,aAAA;MACA,KAAAd,oBAAA;MACA,KAAA6B,QAAA,CAAAoB,OAAA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAA;MACA,IAAAC,aAAA,GAAAN,YAAA,CAAAO,OAAA;MACA,IAAAD,aAAA;QACA,KAAArC,aAAA,GAAAiC,IAAA,CAAAM,KAAA,CAAAF,aAAA;MACA;IACA;IAEA,YACAG,YAAA,WAAAA,aAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA;MACA,KAAAA,SAAA;MACA,OAAAC,UAAA,CAAAD,SAAA,EAAAE,OAAA;IACA;IAEA,SACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAA9D,UAAA;;MAEA;MACA,UAAAE,OAAA,CAAAC,OAAA;QACA,KAAA2B,QAAA,CAAAiC,OAAA;QACA,KAAA/D,UAAA;QACA;MACA;MAEA,KAAAgE,SAAA;QACA;QACA,IAAAC,WAAA,GAAAC,MAAA,CAAAC,IAAA;;QAEA;QACA,IAAAC,YAAA,GAAAN,MAAA,CAAAO,iBAAA;QAEAJ,WAAA,CAAAK,QAAA,CAAAC,KAAA,CAAAH,YAAA;QACAH,WAAA,CAAAK,QAAA,CAAAE,KAAA;;QAEA;QACAP,WAAA,CAAAQ,MAAA;UACAC,UAAA;YACAT,WAAA,CAAAU,KAAA;YACA;YACAb,MAAA,CAAA9D,UAAA;UACA;QACA;MACA;IACA;IAEA,iBACAqE,iBAAA,WAAAA,kBAAA;MAAA,IAAAO,MAAA;MACA,IAAA/D,OAAA,QAAAX,OAAA,CAAAW,OAAA;MACA,IAAAgE,WAAA;MAEAhE,OAAA,CAAAiE,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACAH,WAAA,6GAAAI,MAAA,CAEAD,KAAA,qGAAAC,MAAA,CACAF,IAAA,CAAAG,WAAA,uGAAAD,MAAA,CACAF,IAAA,CAAAI,WAAA,uGAAAF,MAAA,CACAF,IAAA,CAAAK,QAAA,uGAAAH,MAAA,CACAL,MAAA,CAAArB,YAAA,aAAAwB,IAAA,CAAAM,KAAA,kGAAAJ,MAAA,CACAL,MAAA,CAAArB,YAAA,aAAAwB,IAAA,CAAAO,MAAA,kGAAAL,MAAA,CACAF,IAAA,CAAAnE,MAAA,2CAEA;MACA;;MAEA;MACA,IAAA2E,aAAA;MACA,SAAAxE,aAAA,CAAAM,WAAA;QACAkE,aAAA,YAAAN,MAAA,MAAAlE,aAAA,CAAAO,SAAA;MACA;QACAiE,aAAA,YAAAN,MAAA,MAAAlE,aAAA,CAAAO,SAAA;MACA;MAEA,+SAAA2D,MAAA,CASAM,aAAA,8BAAAN,MAAA,CACA,KAAAlE,aAAA,CAAAC,SAAA,SAAAiE,MAAA,MAAAlE,aAAA,CAAAE,WAAA,SAAAgE,MAAA,MAAAlE,aAAA,CAAAG,YAAA,SAAA+D,MAAA,MAAAlE,aAAA,CAAAI,UAAA,wPAAA8D,MAAA,CASA,KAAAlE,aAAA,CAAAK,QAAA,6HAAA6D,MAAA,CAIA,KAAAlE,aAAA,CAAAC,SAAA,47BAAAiE,MAAA,CAiCA,KAAAlE,aAAA,CAAAK,QAAA,y+BAAA6D,MAAA,CAsCA,KAAAlE,aAAA,CAAAK,QAAA,mpDAAA6D,MAAA,CAuDA,KAAA/E,OAAA,CAAAC,OAAA,yNAAA8E,MAAA,CAIA,KAAAO,SAAA,MAAAtF,OAAA,CAAAG,OAAA,8MAAA4E,MAAA,CAIA,KAAAQ,aAAA,MAAAvF,OAAA,CAAAK,MAAA,maAAA0E,MAAA,CASA,KAAA/E,OAAA,CAAAE,aAAA,uPAAA6E,MAAA,CAIA,KAAAS,cAAA,MAAAxF,OAAA,CAAAI,OAAA,+UAAA2E,MAAA,CAMA,KAAA/E,OAAA,CAAAO,YAAA,SAAAP,OAAA,CAAAM,QAAA,iPAAAyE,MAAA,CAIA,KAAA/E,OAAA,CAAAS,WAAA,SAAAT,OAAA,CAAAQ,OAAA,8VAAAuE,MAAA,CAOA,KAAA/E,OAAA,CAAAU,MAAA,0uBAAAqE,MAAA,CAoBAJ,WAAA;IA0BA;IAEA,aACAY,aAAA,WAAAA,cAAAlF,MAAA;MACA;MACA,IAAAoF,UAAA,QAAAC,IAAA,CAAAC,IAAA,CAAAC,oBAAA;MACA,IAAAC,UAAA,GAAAJ,UAAA,CAAAK,IAAA,WAAAjB,IAAA;QAAA,OAAAA,IAAA,CAAAkB,KAAA,KAAA1F,MAAA;MAAA;MACA,OAAAwF,UAAA,GAAAA,UAAA,CAAAG,KAAA;IACA;IAEA,eACAR,cAAA,WAAAA,eAAApF,OAAA;MACA;MACA,IAAA6F,QAAA,QAAAP,IAAA,CAAAC,IAAA,CAAAO,kBAAA;MACA,IAAAC,QAAA,GAAAF,QAAA,CAAAH,IAAA,WAAAjB,IAAA;QAAA,OAAAA,IAAA,CAAAkB,KAAA,KAAA3F,OAAA;MAAA;MACA,OAAA+F,QAAA,GAAAA,QAAA,CAAAH,KAAA;IACA;IAEA,SACAI,WAAA,WAAAA,YAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAtD,iBAAA;EACA;AACA", "ignoreList": []}]}