{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\check\\print.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\check\\print.vue", "mtime": 1756537522255}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEludmVudG9yeUNoZWNrIH0gZnJvbSAiQC9hcGkvaW52ZW50b3J5L2NoZWNrIjsKaW1wb3J0IHsgZ2V0QmF0Y2hVc2VyUmVhbE5hbWVzIH0gZnJvbSAiQC91dGlscy91c2VyVXRpbHMiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJDaGVja1ByaW50IiwKICBkaWN0czogWydpbnZlbnRvcnlfY2hlY2tfc3RhdHVzJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBpc1ByaW50aW5nOiBmYWxzZSwKICAgICAgcHJpbnRTZXR0aW5nc1Zpc2libGU6IGZhbHNlLAogICAgICBjaGVja0RhdGE6IHsKICAgICAgICBjaGVja0NvZGU6ICIiLAogICAgICAgIHdhcmVob3VzZU5hbWU6ICIiLAogICAgICAgIGNoZWNrVGltZTogbnVsbCwKICAgICAgICBzdGF0dXM6ICIiLAogICAgICAgIGNyZWF0ZUJ5OiAiIiwKICAgICAgICBjcmVhdGVCeU5hbWU6ICIiLAogICAgICAgIGF1ZGl0Qnk6ICIiLAogICAgICAgIGF1ZGl0QnlOYW1lOiAiIiwKICAgICAgICBtYW5hZ2VyTmFtZTogIiIsCiAgICAgICAgcmVtYXJrOiAiIiwKICAgICAgICBkZXRhaWxzOiBbXQogICAgICB9LAogICAgICBjaGVja0lkOiBudWxsLAogICAgICBwcmludFNldHRpbmdzOiB7CiAgICAgICAgbWFyZ2luVG9wOiAxNSwKICAgICAgICBtYXJnaW5SaWdodDogMTUsCiAgICAgICAgbWFyZ2luQm90dG9tOiAxNSwKICAgICAgICBtYXJnaW5MZWZ0OiAxNSwKICAgICAgICBmb250U2l6ZTogMTIsCiAgICAgICAgb3JpZW50YXRpb246ICdwb3J0cmFpdCcsCiAgICAgICAgcGFwZXJTaXplOiAnQTQnCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5jaGVja0lkID0gdGhpcy4kcm91dGUucGFyYW1zLmNoZWNrSWQgfHwgdGhpcy4kcm91dGUucGFyYW1zLmlkIHx8IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOwogICAgY29uc29sZS5sb2coJ+ebmOeCueWNleaJk+WNsOmhtemdouWIneWni+WMlu+8jOebmOeCueWNlUlEOicsIHRoaXMuY2hlY2tJZCk7CiAgICBpZiAoIXRoaXMuY2hlY2tJZCkgewogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvLrlsJHnm5jngrnljZVJROWPguaVsCcpOwogICAgICByZXR1cm47CiAgICB9CiAgICB0aGlzLmdldENoZWNrRGF0YSgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOiOt+WPluebmOeCueWNleS/oeaBryAqLwogICAgZ2V0Q2hlY2tEYXRhKCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBnZXRJbnZlbnRvcnlDaGVjayh0aGlzLmNoZWNrSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuY2hlY2tEYXRhID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAKICAgICAgICAvLyDojrflj5bnlKjmiLfnnJ/lrp7lp5PlkI0KICAgICAgICBjb25zdCB1c2VyTmFtZXMgPSBbCiAgICAgICAgICB0aGlzLmNoZWNrRGF0YS5jcmVhdGVCeSwKICAgICAgICAgIHRoaXMuY2hlY2tEYXRhLmF1ZGl0QnkKICAgICAgICBdLmZpbHRlcihuYW1lID0+IG5hbWUpOyAvLyDov4fmu6TnqbrlgLwKCiAgICAgICAgaWYgKHVzZXJOYW1lcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICBnZXRCYXRjaFVzZXJSZWFsTmFtZXModXNlck5hbWVzKS50aGVuKG5hbWVNYXAgPT4gewogICAgICAgICAgICAvLyDmm7TmlrDnlKjmiLfnnJ/lrp7lp5PlkI0KICAgICAgICAgICAgaWYgKHRoaXMuY2hlY2tEYXRhLmNyZWF0ZUJ5ICYmIG5hbWVNYXBbdGhpcy5jaGVja0RhdGEuY3JlYXRlQnldKSB7CiAgICAgICAgICAgICAgdGhpcy5jaGVja0RhdGEuY3JlYXRlQnlOYW1lID0gbmFtZU1hcFt0aGlzLmNoZWNrRGF0YS5jcmVhdGVCeV07CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKHRoaXMuY2hlY2tEYXRhLmF1ZGl0QnkgJiYgbmFtZU1hcFt0aGlzLmNoZWNrRGF0YS5hdWRpdEJ5XSkgewogICAgICAgICAgICAgIHRoaXMuY2hlY2tEYXRhLmF1ZGl0QnlOYW1lID0gbmFtZU1hcFt0aGlzLmNoZWNrRGF0YS5hdWRpdEJ5XTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICAgIAogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+W55uY54K55Y2V5L+h5oGv5aSx6LSlIik7CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLyoqIOWkhOeQhuaJk+WNsOiuvue9riAqLwogICAgaGFuZGxlUHJpbnRTZXR0aW5ncygpIHsKICAgICAgdGhpcy5wcmludFNldHRpbmdzVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgCiAgICAvKiog5L+d5a2Y5omT5Y2w6K6+572uICovCiAgICBzYXZlUHJpbnRTZXR0aW5ncygpIHsKICAgICAgLy8g5L+d5a2Y5Yiw5pys5Zyw5a2Y5YKoCiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjaGVja1ByaW50U2V0dGluZ3MnLCBKU09OLnN0cmluZ2lmeSh0aGlzLnByaW50U2V0dGluZ3MpKTsKICAgICAgdGhpcy5wcmludFNldHRpbmdzVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aJk+WNsOiuvue9ruW3suS/neWtmCcpOwogICAgfSwKICAgIAogICAgLyoqIOWKoOi9veaJk+WNsOiuvue9riAqLwogICAgbG9hZFByaW50U2V0dGluZ3MoKSB7CiAgICAgIGNvbnN0IHNhdmVkU2V0dGluZ3MgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY2hlY2tQcmludFNldHRpbmdzJyk7CiAgICAgIGlmIChzYXZlZFNldHRpbmdzKSB7CiAgICAgICAgdGhpcy5wcmludFNldHRpbmdzID0gSlNPTi5wYXJzZShzYXZlZFNldHRpbmdzKTsKICAgICAgfQogICAgfSwKICAgIAogICAgLyoqIOiOt+WPluW3ruW8guaVsOmHj+agt+W8jyAqLwogICAgZ2V0RGlmZkNsYXNzKGRpZmZRdWFudGl0eSkgewogICAgICBpZiAoZGlmZlF1YW50aXR5IDwgMCkgewogICAgICAgIHJldHVybiAndGV4dC1yZWQnOwogICAgICB9IGVsc2UgaWYgKGRpZmZRdWFudGl0eSA+IDApIHsKICAgICAgICByZXR1cm4gJ3RleHQtZ3JlZW4nOwogICAgICB9CiAgICAgIHJldHVybiAnJzsKICAgIH0sCiAgICAKICAgIC8qKiDmiZPljbAgKi8KICAgIGhhbmRsZVByaW50KCkgewogICAgICB0aGlzLmlzUHJpbnRpbmcgPSB0cnVlOwogICAgICAKICAgICAgLy8g56Gu5L+d5pWw5o2u5bey5Yqg6L29CiAgICAgIGlmICghdGhpcy5jaGVja0RhdGEuY2hlY2tDb2RlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmlbDmja7ov5jlnKjliqDovb3kuK3vvIzor7fnqI3lkI7lho3or5UnKTsKICAgICAgICB0aGlzLmlzUHJpbnRpbmcgPSBmYWxzZTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAvLyDliJvlu7rmlrDnqpflj6Pov5vooYzmiZPljbAKICAgICAgICBjb25zdCBwcmludFdpbmRvdyA9IHdpbmRvdy5vcGVuKCcnLCAnX2JsYW5rJywgJ3dpZHRoPTEwMDAsaGVpZ2h0PTgwMCxzY3JvbGxiYXJzPXllcyxyZXNpemFibGU9eWVzJyk7CiAgICAgICAgCiAgICAgICAgLy8g55Sf5oiQ5omT5Y2w5YaF5a65CiAgICAgICAgY29uc3QgcHJpbnRDb250ZW50ID0gdGhpcy5nZW5lcmF0ZVByaW50SFRNTCgpOwogICAgICAgIAogICAgICAgIHByaW50V2luZG93LmRvY3VtZW50LndyaXRlKHByaW50Q29udGVudCk7CiAgICAgICAgcHJpbnRXaW5kb3cuZG9jdW1lbnQuY2xvc2UoKTsKICAgICAgICAKICAgICAgICAvLyDnrYnlvoXlhoXlrrnliqDovb3lrozmiJDlkI7miZPljbAKICAgICAgICBwcmludFdpbmRvdy5vbmxvYWQgPSAoKSA9PiB7CiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgcHJpbnRXaW5kb3cucHJpbnQoKTsKICAgICAgICAgICAgLy8g5omT5Y2w5a6M5oiQ5ZCO5LiN6Ieq5Yqo5YWz6Zet56qX5Y+j77yM6K6p55So5oi35omL5Yqo5YWz6ZetCiAgICAgICAgICAgIHRoaXMuaXNQcmludGluZyA9IGZhbHNlOwogICAgICAgICAgfSwgNTAwKTsKICAgICAgICB9OwogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8qKiDnlJ/miJDmiZPljbDpobXpnaJIVE1MICovCiAgICBnZW5lcmF0ZVByaW50SFRNTCgpIHsKICAgICAgY29uc3QgZGV0YWlscyA9IHRoaXMuY2hlY2tEYXRhLmRldGFpbHMgfHwgW107CiAgICAgIGxldCBkZXRhaWxzSFRNTCA9ICcnOwogICAgICAKICAgICAgZGV0YWlscy5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gewogICAgICAgIGNvbnN0IGRpZmZDbGFzcyA9IGl0ZW0uZGlmZlF1YW50aXR5IDwgMCA/ICdjb2xvcjogI0Y1NkM2QzsnIDogaXRlbS5kaWZmUXVhbnRpdHkgPiAwID8gJ2NvbG9yOiAjNjdDMjNBOycgOiAnJzsKICAgICAgICBkZXRhaWxzSFRNTCArPSBgCiAgICAgICAgICA8dHI+CiAgICAgICAgICAgIDx0ZCBzdHlsZT0iYm9yZGVyOiAxcHggc29saWQgIzAwMDsgcGFkZGluZzogOHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7Ij4ke2luZGV4ICsgMX08L3RkPgogICAgICAgICAgICA8dGQgc3R5bGU9ImJvcmRlcjogMXB4IHNvbGlkICMwMDA7IHBhZGRpbmc6IDhweDsgdGV4dC1hbGlnbjogY2VudGVyOyI+JHtpdGVtLnByb2R1Y3RDb2RlIHx8ICcnfTwvdGQ+CiAgICAgICAgICAgIDx0ZCBzdHlsZT0iYm9yZGVyOiAxcHggc29saWQgIzAwMDsgcGFkZGluZzogOHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7Ij4ke2l0ZW0ucHJvZHVjdE5hbWUgfHwgJyd9PC90ZD4KICAgICAgICAgICAgPHRkIHN0eWxlPSJib3JkZXI6IDFweCBzb2xpZCAjMDAwOyBwYWRkaW5nOiA4cHg7IHRleHQtYWxpZ246IGNlbnRlcjsiPiR7aXRlbS5ib29rUXVhbnRpdHkgfHwgJyd9PC90ZD4KICAgICAgICAgICAgPHRkIHN0eWxlPSJib3JkZXI6IDFweCBzb2xpZCAjMDAwOyBwYWRkaW5nOiA4cHg7IHRleHQtYWxpZ246IGNlbnRlcjsiPiR7aXRlbS5yZWFsUXVhbnRpdHkgfHwgJyd9PC90ZD4KICAgICAgICAgICAgPHRkIHN0eWxlPSJib3JkZXI6IDFweCBzb2xpZCAjMDAwOyBwYWRkaW5nOiA4cHg7IHRleHQtYWxpZ246IGNlbnRlcjsgJHtkaWZmQ2xhc3N9Ij4ke2l0ZW0uZGlmZlF1YW50aXR5IHx8ICcnfTwvdGQ+CiAgICAgICAgICAgIDx0ZCBzdHlsZT0iYm9yZGVyOiAxcHggc29saWQgIzAwMDsgcGFkZGluZzogOHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7Ij4ke2l0ZW0ucmVtYXJrIHx8ICcnfTwvdGQ+CiAgICAgICAgICA8L3RyPgogICAgICAgIGA7CiAgICAgIH0pOwogICAgICAKICAgICAgLy8g5qC55o2u6K6+572u56Gu5a6a6aG16Z2i5pa55ZCR5ZKM57q45byg5aSn5bCPCiAgICAgIGxldCBwYWdlU2l6ZVN0eWxlID0gJyc7CiAgICAgIGlmICh0aGlzLnByaW50U2V0dGluZ3Mub3JpZW50YXRpb24gPT09ICdsYW5kc2NhcGUnKSB7CiAgICAgICAgcGFnZVNpemVTdHlsZSA9IGBzaXplOiAke3RoaXMucHJpbnRTZXR0aW5ncy5wYXBlclNpemV9IGxhbmRzY2FwZTtgOwogICAgICB9IGVsc2UgewogICAgICAgIHBhZ2VTaXplU3R5bGUgPSBgc2l6ZTogJHt0aGlzLnByaW50U2V0dGluZ3MucGFwZXJTaXplfSBwb3J0cmFpdDtgOwogICAgICB9CiAgICAgIAogICAgICByZXR1cm4gYAogICAgICAgIDwhRE9DVFlQRSBodG1sPgogICAgICAgIDxodG1sPgogICAgICAgIDxoZWFkPgogICAgICAgICAgPG1ldGEgY2hhcnNldD0iVVRGLTgiPgogICAgICAgICAgPG1ldGEgbmFtZT0idmlld3BvcnQiIGNvbnRlbnQ9IndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xLjAiPgogICAgICAgICAgPHRpdGxlPuW6k+WtmOebmOeCueWNleaJk+WNsDwvdGl0bGU+CiAgICAgICAgICA8c3R5bGU+CiAgICAgICAgICAgIEBwYWdlIHsgCiAgICAgICAgICAgICAgJHtwYWdlU2l6ZVN0eWxlfQogICAgICAgICAgICAgIG1hcmdpbjogJHt0aGlzLnByaW50U2V0dGluZ3MubWFyZ2luVG9wfW1tICR7dGhpcy5wcmludFNldHRpbmdzLm1hcmdpblJpZ2h0fW1tICR7dGhpcy5wcmludFNldHRpbmdzLm1hcmdpbkJvdHRvbX1tbSAke3RoaXMucHJpbnRTZXR0aW5ncy5tYXJnaW5MZWZ0fW1tOyAKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgKiB7CiAgICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgaHRtbCwgYm9keSB7IAogICAgICAgICAgICAgIGZvbnQtZmFtaWx5OiAiTWljcm9zb2Z0IFlhSGVpIiwgU2ltU3VuLCBzYW5zLXNlcmlmOyAKICAgICAgICAgICAgICBmb250LXNpemU6ICR7dGhpcy5wcmludFNldHRpbmdzLmZvbnRTaXplfXB0OyAKICAgICAgICAgICAgICBjb2xvcjogIzAwMDsgCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsgCiAgICAgICAgICAgICAgbWFyZ2luOiAwOyAKICAgICAgICAgICAgICBwYWRkaW5nOiAke3RoaXMucHJpbnRTZXR0aW5ncy5tYXJnaW5Ub3AvM31tbTsKICAgICAgICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICAgICAgICAtd2Via2l0LXByaW50LWNvbG9yLWFkanVzdDogZXhhY3Q7CiAgICAgICAgICAgICAgcHJpbnQtY29sb3ItYWRqdXN0OiBleGFjdDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmNvbnRhaW5lciB7CiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgICAgICAgbWF4LXdpZHRoOiAxMDAwcHg7CiAgICAgICAgICAgICAgbWFyZ2luOiAwIGF1dG87CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5wcmludC1jb250YWluZXIgewogICAgICAgICAgICAgIHBhZGRpbmc6IDIwcHg7CiAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICAgICAgICAgICAgICBjb2xvcjogIzAwMDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLnByaW50LWNvbnRlbnQgewogICAgICAgICAgICAgIG1heC13aWR0aDogMTAwMHB4OwogICAgICAgICAgICAgIG1hcmdpbjogMCBhdXRvOwogICAgICAgICAgICAgIGZvbnQtZmFtaWx5OiAiU2ltU3VuIiwgIuWui+S9kyIsIHNlcmlmOwogICAgICAgICAgICAgIGNvbG9yOiAjMDAwOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuY2hlY2staGVhZGVyIHsKICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDsKICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAycHggc29saWQgIzAwMDsKICAgICAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogMTBweDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmNoZWNrLWhlYWRlciAudGl0bGUgewogICAgICAgICAgICAgIGZvbnQtc2l6ZTogJHt0aGlzLnByaW50U2V0dGluZ3MuZm9udFNpemUgKyA4fXB0OwogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgICAgICAgIG1hcmdpbjogMCAwIDIwcHggMDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmhlYWRlci1pbmZvIHsKICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmhlYWRlci1pdGVtIHsKICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5oZWFkZXItaXRlbSAubGFiZWwgewogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNXB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuY2hlY2staW5mbyB7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmluZm8taXRlbSB7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDsKICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaW5mby1pdGVtIC5sYWJlbCB7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgICAgbWluLXdpZHRoOiAxMDBweDsKICAgICAgICAgICAgICBmbGV4LXNocmluazogMDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmNoZWNrLWRldGFpbHMgaDMsCiAgICAgICAgICAgIC5hcHByb3ZhbC1pbmZvIGgzIHsKICAgICAgICAgICAgICBmb250LXNpemU6ICR7dGhpcy5wcmludFNldHRpbmdzLmZvbnRTaXplICsgNH1wdDsKICAgICAgICAgICAgICBtYXJnaW46IDAgMCAxNXB4IDA7CiAgICAgICAgICAgICAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjNDA5RUZGOwogICAgICAgICAgICAgIHBhZGRpbmctbGVmdDogMTBweDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgdGFibGUgewogICAgICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgICAgIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDsKICAgICAgICAgICAgICB0YWJsZS1sYXlvdXQ6IGZpeGVkOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICB0aCB7CiAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsKICAgICAgICAgICAgICBjb2xvcjogIzAwMDsKICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjMDAwOwogICAgICAgICAgICAgIHBhZGRpbmc6IDhweDsKICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIHRkIHsKICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjMDAwOwogICAgICAgICAgICAgIHBhZGRpbmc6IDhweDsKICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgICAgICAgd29yZC13cmFwOiBicmVhay13b3JkOwogICAgICAgICAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmNoZWNrLWZvb3RlciB7CiAgICAgICAgICAgICAgbWFyZ2luOiAzMHB4IDA7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5mb290ZXItaXRlbSB7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLnNpZ25hdHVyZS1saW5lIHsKICAgICAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICAgICAgICAgICAgd2lkdGg6IDgwcHg7CiAgICAgICAgICAgICAgaGVpZ2h0OiAxcHg7CiAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzAwMDsKICAgICAgICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLnRleHQtcmVkIHsKICAgICAgICAgICAgICBjb2xvcjogI0Y1NkM2QzsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLnRleHQtZ3JlZW4gewogICAgICAgICAgICAgIGNvbG9yOiAjNjdDMjNBOwogICAgICAgICAgICB9CiAgICAgICAgICA8L3N0eWxlPgogICAgICAgIDwvaGVhZD4KICAgICAgICA8Ym9keT4KICAgICAgICAgIDxkaXYgY2xhc3M9InByaW50LWNvbnRhaW5lciI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InByaW50LWNvbnRlbnQiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNoZWNrLWhlYWRlciI+CiAgICAgICAgICAgICAgICA8aDIgY2xhc3M9InRpdGxlIj7lupPlrZjnm5jngrnljZU8L2gyPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyLWluZm8iPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItaXRlbSI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImxhYmVsIj7nm5jngrnljZXlj7fvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InZhbHVlIj4ke3RoaXMuY2hlY2tEYXRhLmNoZWNrQ29kZSB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItaXRlbSI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImxhYmVsIj7nm5jngrnml6XmnJ/vvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InZhbHVlIj4ke3RoaXMucGFyc2VUaW1lKHRoaXMuY2hlY2tEYXRhLmNoZWNrVGltZSkgfHwgJyd9PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyLWl0ZW0iPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJsYWJlbCI+54q25oCB77yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJ2YWx1ZSI+JHt0aGlzLmdldFN0YXR1c05hbWUodGhpcy5jaGVja0RhdGEuc3RhdHVzKSB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY2hlY2staW5mbyI+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiA1MCU7IGRpc3BsYXk6IGZsZXg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7ku5PlupPlkI3np7DvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4+JHt0aGlzLmNoZWNrRGF0YS53YXJlaG91c2VOYW1lIHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiA1MCU7IGRpc3BsYXk6IGZsZXg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7liLbljZXkurrvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4+JHt0aGlzLmNoZWNrRGF0YS5jcmVhdGVCeU5hbWUgfHwgdGhpcy5jaGVja0RhdGEuY3JlYXRlQnkgfHwgJyd9PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsgbWFyZ2luLWJvdHRvbTogMTVweDsiPgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogNTAlOyBkaXNwbGF5OiBmbGV4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEwMHB4OyI+5a6h5qC45Lq677yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuPiR7dGhpcy5jaGVja0RhdGEuYXVkaXRCeU5hbWUgfHwgdGhpcy5jaGVja0RhdGEuYXVkaXRCeSB8fCAn5pyq5a6h5qC4J308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogNTAlOyBkaXNwbGF5OiBmbGV4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEyMHB4OyI+5LuT5bqT6LSf6LSj5Lq677yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuPiR7dGhpcy5jaGVja0RhdGEubWFuYWdlck5hbWUgfHwgJ+acquaMh+Wumid9PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiAxMDAlOyBkaXNwbGF5OiBmbGV4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEwMHB4OyI+5aSH5rOo77yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuPiR7dGhpcy5jaGVja0RhdGEucmVtYXJrIHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAKICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjaGVjay1kZXRhaWxzIj4KICAgICAgICAgICAgICAgIDxoMz7nm5jngrnnianlk4Hkv6Hmga88L2gzPgogICAgICAgICAgICAgICAgPHRhYmxlPgogICAgICAgICAgICAgICAgICA8dGhlYWQ+CiAgICAgICAgICAgICAgICAgICAgPHRyPgogICAgICAgICAgICAgICAgICAgICAgPHRoPuW6j+WPtzwvdGg+CiAgICAgICAgICAgICAgICAgICAgICA8dGg+54mp5ZOB57yW56CBPC90aD4KICAgICAgICAgICAgICAgICAgICAgIDx0aD7nianlk4HlkI3np7A8L3RoPgogICAgICAgICAgICAgICAgICAgICAgPHRoPui0pumdouaVsOmHjzwvdGg+CiAgICAgICAgICAgICAgICAgICAgICA8dGg+5a6e6ZmF5pWw6YePPC90aD4KICAgICAgICAgICAgICAgICAgICAgIDx0aD7lt67lvILmlbDph488L3RoPgogICAgICAgICAgICAgICAgICAgICAgPHRoPuWkh+azqDwvdGg+CiAgICAgICAgICAgICAgICAgICAgPC90cj4KICAgICAgICAgICAgICAgICAgPC90aGVhZD4KICAgICAgICAgICAgICAgICAgPHRib2R5PgogICAgICAgICAgICAgICAgICAgICR7ZGV0YWlsc0hUTUx9CiAgICAgICAgICAgICAgICAgIDwvdGJvZHk+CiAgICAgICAgICAgICAgICA8L3RhYmxlPgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIAogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNoZWNrLWZvb3RlciI+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiAzMy4zMyU7IGRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBmbGV4LWVuZDsgaGVpZ2h0OiAzMHB4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEwMHB4OyI+55uY54K55Lq6562+5a2X77yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGlubGluZS1ibG9jazsgd2lkdGg6IDgwcHg7IGhlaWdodDogMXB4OyBiYWNrZ3JvdW5kLWNvbG9yOiAjMDAwOyBtYXJnaW4tbGVmdDogMTBweDsgcG9zaXRpb246IHJlbGF0aXZlOyBib3R0b206IDNweDsiPjwvZGl2PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0id2lkdGg6IDMzLjMzJTsgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGZsZXgtZW5kOyBoZWlnaHQ6IDMwcHg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTIwcHg7Ij7ku5PlupPotJ/otKPkurrnrb7lrZfvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogaW5saW5lLWJsb2NrOyB3aWR0aDogODBweDsgaGVpZ2h0OiAxcHg7IGJhY2tncm91bmQtY29sb3I6ICMwMDA7IG1hcmdpbi1sZWZ0OiAxMHB4OyBwb3NpdGlvbjogcmVsYXRpdmU7IGJvdHRvbTogM3B4OyI+PC9kaXY+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogMzMuMzMlOyBkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogZmxleC1lbmQ7IGhlaWdodDogMzBweDsiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWluLXdpZHRoOiAxMDBweDsiPuWuoeaguOS6uuetvuWtl++8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBpbmxpbmUtYmxvY2s7IHdpZHRoOiA4MHB4OyBoZWlnaHQ6IDFweDsgYmFja2dyb3VuZC1jb2xvcjogIzAwMDsgbWFyZ2luLWxlZnQ6IDEwcHg7IHBvc2l0aW9uOiByZWxhdGl2ZTsgYm90dG9tOiAzcHg7Ij48L2Rpdj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2JvZHk+CiAgICAgICAgPC9odG1sPgogICAgICBgOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlueKtuaAgeWQjeensCAqLwogICAgZ2V0U3RhdHVzTmFtZShzdGF0dXMpIHsKICAgICAgLy8g5L2/55So5LiO5bGP5bmV6aKE6KeI55u45ZCM55qE5a2X5YW45pig5bCE5pa55byPCiAgICAgIGNvbnN0IHN0YXR1c0RpY3QgPSB0aGlzLmRpY3QudHlwZS5pbnZlbnRvcnlfY2hlY2tfc3RhdHVzIHx8IFtdOwogICAgICBjb25zdCBzdGF0dXNJdGVtID0gc3RhdHVzRGljdC5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PT0gc3RhdHVzKTsKICAgICAgcmV0dXJuIHN0YXR1c0l0ZW0gPyBzdGF0dXNJdGVtLmxhYmVsIDogJyc7CiAgICB9LAogICAgCiAgICAvKiog5YWz6ZetICovCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICAvLyDliqDovb3miZPljbDorr7nva4KICAgIHRoaXMubG9hZFByaW50U2V0dGluZ3MoKTsKICB9Cn07Cg=="}, {"version": 3, "sources": ["print.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgLA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "print.vue", "sourceRoot": "src/views/inventory/check", "sourcesContent": ["<template>\n  <div class=\"print-container\">\n    <!-- 打印预览界面 -->\n    <div class=\"print-header\" v-if=\"!isPrinting\">\n      <el-button type=\"primary\" icon=\"el-icon-printer\" size=\"small\" @click=\"handlePrint\">\n        打印盘点单\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-setting\" size=\"small\" @click=\"handlePrintSettings\">\n        打印设置\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-close\" size=\"small\" @click=\"handleClose\">\n        关闭\n      </el-button>\n    </div>\n    \n    <!-- 打印设置对话框 -->\n    <el-dialog title=\"打印设置\" :visible.sync=\"printSettingsVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"printSettingsForm\" :model=\"printSettings\" label-width=\"100px\">\n        <el-form-item label=\"页面边距\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginTop\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginRight\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginBottom\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginLeft\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n          </el-row>\n          <div class=\"margin-labels\">\n            <span>上</span>\n            <span>右</span>\n            <span>下</span>\n            <span>左</span>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"字体大小\">\n          <el-slider v-model=\"printSettings.fontSize\" :min=\"8\" :max=\"20\" show-input></el-slider>\n        </el-form-item>\n        \n        <el-form-item label=\"页面方向\">\n          <el-radio-group v-model=\"printSettings.orientation\">\n            <el-radio label=\"portrait\">纵向</el-radio>\n            <el-radio label=\"landscape\">横向</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"纸张大小\">\n          <el-select v-model=\"printSettings.paperSize\" placeholder=\"请选择纸张大小\">\n            <el-option label=\"A4\" value=\"A4\"></el-option>\n            <el-option label=\"A5\" value=\"A5\"></el-option>\n            <el-option label=\"B5\" value=\"B5\"></el-option>\n            <el-option label=\"Letter\" value=\"letter\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"printSettingsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"savePrintSettings\">确 定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 打印内容 -->\n    <div class=\"print-content\" id=\"printContent\">\n      <div class=\"check-header\">\n        <h2 class=\"title\">库存盘点单</h2>\n        <div class=\"header-info\">\n          <div class=\"header-item\">\n            <span class=\"label\">盘点单号：</span>\n            <span class=\"value\">{{ checkData.checkCode }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">盘点日期：</span>\n            <span class=\"value\">{{ parseTime(checkData.checkTime) }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">状态：</span>\n            <span class=\"value\">\n              <dict-tag :options=\"dict.type.inventory_check_status\" :value=\"checkData.status\" />\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"check-info\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">仓库名称：</span>\n              <span class=\"value\">{{ checkData.warehouseName }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">制单人：</span>\n              <span class=\"value\">{{ checkData.createByName || checkData.createBy }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">审核人：</span>\n              <span class=\"value\">{{ checkData.auditByName || checkData.auditBy || '未审核' }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">仓库负责人：</span>\n              <span class=\"value\">{{ checkData.managerName || '未指定' }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <div class=\"info-item\">\n              <span class=\"label\">备注：</span>\n              <span class=\"value\">{{ checkData.remark }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <div class=\"check-details\">\n        <h3>盘点物品信息</h3>\n        <el-table :data=\"checkData.details\" class=\"detail-table\">\n          <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n          <el-table-column label=\"物品编码\" prop=\"productCode\" />\n          <el-table-column label=\"物品名称\" prop=\"productName\" />\n          <el-table-column label=\"账面数量\" prop=\"bookQuantity\" />\n          <el-table-column label=\"实际数量\" prop=\"realQuantity\" />\n          <el-table-column label=\"差异数量\" prop=\"diffQuantity\">\n            <template slot-scope=\"scope\">\n              <span :class=\"getDiffClass(scope.row.diffQuantity)\">{{ scope.row.diffQuantity }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"备注\" prop=\"remark\" />\n        </el-table>\n      </div>\n      \n      <div class=\"check-footer\">\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">盘点人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 120px;\">仓库负责人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getInventoryCheck } from \"@/api/inventory/check\";\nimport { getBatchUserRealNames } from \"@/utils/userUtils\";\n\nexport default {\n  name: \"CheckPrint\",\n  dicts: ['inventory_check_status'],\n  data() {\n    return {\n      loading: false,\n      isPrinting: false,\n      printSettingsVisible: false,\n      checkData: {\n        checkCode: \"\",\n        warehouseName: \"\",\n        checkTime: null,\n        status: \"\",\n        createBy: \"\",\n        createByName: \"\",\n        auditBy: \"\",\n        auditByName: \"\",\n        managerName: \"\",\n        remark: \"\",\n        details: []\n      },\n      checkId: null,\n      printSettings: {\n        marginTop: 15,\n        marginRight: 15,\n        marginBottom: 15,\n        marginLeft: 15,\n        fontSize: 12,\n        orientation: 'portrait',\n        paperSize: 'A4'\n      }\n    };\n  },\n  created() {\n    this.checkId = this.$route.params.checkId || this.$route.params.id || this.$route.query.id;\n    console.log('盘点单打印页面初始化，盘点单ID:', this.checkId);\n    if (!this.checkId) {\n      this.$message.error('缺少盘点单ID参数');\n      return;\n    }\n    this.getCheckData();\n  },\n  methods: {\n    /** 获取盘点单信息 */\n    getCheckData() {\n      this.loading = true;\n      getInventoryCheck(this.checkId).then(response => {\n        this.checkData = response.data;\n        \n        // 获取用户真实姓名\n        const userNames = [\n          this.checkData.createBy,\n          this.checkData.auditBy\n        ].filter(name => name); // 过滤空值\n\n        if (userNames.length > 0) {\n          getBatchUserRealNames(userNames).then(nameMap => {\n            // 更新用户真实姓名\n            if (this.checkData.createBy && nameMap[this.checkData.createBy]) {\n              this.checkData.createByName = nameMap[this.checkData.createBy];\n            }\n            if (this.checkData.auditBy && nameMap[this.checkData.auditBy]) {\n              this.checkData.auditByName = nameMap[this.checkData.auditBy];\n            }\n          });\n        }\n        \n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n        this.$message.error(\"获取盘点单信息失败\");\n      });\n    },\n    \n    /** 处理打印设置 */\n    handlePrintSettings() {\n      this.printSettingsVisible = true;\n    },\n    \n    /** 保存打印设置 */\n    savePrintSettings() {\n      // 保存到本地存储\n      localStorage.setItem('checkPrintSettings', JSON.stringify(this.printSettings));\n      this.printSettingsVisible = false;\n      this.$message.success('打印设置已保存');\n    },\n    \n    /** 加载打印设置 */\n    loadPrintSettings() {\n      const savedSettings = localStorage.getItem('checkPrintSettings');\n      if (savedSettings) {\n        this.printSettings = JSON.parse(savedSettings);\n      }\n    },\n    \n    /** 获取差异数量样式 */\n    getDiffClass(diffQuantity) {\n      if (diffQuantity < 0) {\n        return 'text-red';\n      } else if (diffQuantity > 0) {\n        return 'text-green';\n      }\n      return '';\n    },\n    \n    /** 打印 */\n    handlePrint() {\n      this.isPrinting = true;\n      \n      // 确保数据已加载\n      if (!this.checkData.checkCode) {\n        this.$message.warning('数据还在加载中，请稍后再试');\n        this.isPrinting = false;\n        return;\n      }\n      \n      this.$nextTick(() => {\n        // 创建新窗口进行打印\n        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');\n        \n        // 生成打印内容\n        const printContent = this.generatePrintHTML();\n        \n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        \n        // 等待内容加载完成后打印\n        printWindow.onload = () => {\n          setTimeout(() => {\n            printWindow.print();\n            // 打印完成后不自动关闭窗口，让用户手动关闭\n            this.isPrinting = false;\n          }, 500);\n        };\n      });\n    },\n    \n    /** 生成打印页面HTML */\n    generatePrintHTML() {\n      const details = this.checkData.details || [];\n      let detailsHTML = '';\n      \n      details.forEach((item, index) => {\n        const diffClass = item.diffQuantity < 0 ? 'color: #F56C6C;' : item.diffQuantity > 0 ? 'color: #67C23A;' : '';\n        detailsHTML += `\n          <tr>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${index + 1}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productCode || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productName || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.bookQuantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.realQuantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center; ${diffClass}\">${item.diffQuantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.remark || ''}</td>\n          </tr>\n        `;\n      });\n      \n      // 根据设置确定页面方向和纸张大小\n      let pageSizeStyle = '';\n      if (this.printSettings.orientation === 'landscape') {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;\n      } else {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;\n      }\n      \n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>库存盘点单打印</title>\n          <style>\n            @page { \n              ${pageSizeStyle}\n              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; \n            }\n            \n            * {\n              box-sizing: border-box;\n            }\n            \n            html, body { \n              font-family: \"Microsoft YaHei\", SimSun, sans-serif; \n              font-size: ${this.printSettings.fontSize}pt; \n              color: #000; \n              background: #fff; \n              margin: 0; \n              padding: ${this.printSettings.marginTop/3}mm;\n              width: 100%;\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n            \n            .container {\n              width: 100%;\n              max-width: 1000px;\n              margin: 0 auto;\n            }\n            \n            .print-container {\n              padding: 20px;\n              background-color: #fff;\n              color: #000;\n            }\n            \n            .print-content {\n              max-width: 1000px;\n              margin: 0 auto;\n              font-family: \"SimSun\", \"宋体\", serif;\n              color: #000;\n            }\n            \n            .check-header {\n              text-align: center;\n              margin-bottom: 30px;\n              border-bottom: 2px solid #000;\n              padding-bottom: 10px;\n            }\n            \n            .check-header .title {\n              font-size: ${this.printSettings.fontSize + 8}pt;\n              font-weight: bold;\n              margin: 0 0 20px 0;\n            }\n            \n            .header-info {\n              display: flex;\n              justify-content: space-between;\n            }\n            \n            .header-item {\n              display: flex;\n              align-items: center;\n            }\n            \n            .header-item .label {\n              font-weight: bold;\n              margin-right: 5px;\n            }\n            \n            .check-info {\n              margin-bottom: 30px;\n            }\n            \n            .info-item {\n              margin-bottom: 15px;\n              display: flex;\n              align-items: flex-start;\n            }\n            \n            .info-item .label {\n              font-weight: bold;\n              min-width: 100px;\n              flex-shrink: 0;\n            }\n            \n            .check-details h3,\n            .approval-info h3 {\n              font-size: ${this.printSettings.fontSize + 4}pt;\n              margin: 0 0 15px 0;\n              border-left: 4px solid #409EFF;\n              padding-left: 10px;\n            }\n            \n            table {\n              width: 100%;\n              border-collapse: collapse;\n              margin-bottom: 30px;\n              table-layout: fixed;\n            }\n            \n            th {\n              background-color: #f5f5f5;\n              color: #000;\n              font-weight: bold;\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n            }\n            \n            td {\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n            \n            .check-footer {\n              margin: 30px 0;\n            }\n            \n            .footer-item {\n              margin-bottom: 15px;\n            }\n            \n            .signature-line {\n              display: inline-block;\n              width: 80px;\n              height: 1px;\n              background-color: #000;\n              margin-left: 10px;\n            }\n            \n            .text-red {\n              color: #F56C6C;\n            }\n            \n            .text-green {\n              color: #67C23A;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"print-container\">\n            <div class=\"print-content\">\n              <div class=\"check-header\">\n                <h2 class=\"title\">库存盘点单</h2>\n                <div class=\"header-info\">\n                  <div class=\"header-item\">\n                    <span class=\"label\">盘点单号：</span>\n                    <span class=\"value\">${this.checkData.checkCode || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">盘点日期：</span>\n                    <span class=\"value\">${this.parseTime(this.checkData.checkTime) || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">状态：</span>\n                    <span class=\"value\">${this.getStatusName(this.checkData.status) || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"check-info\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库名称：</span>\n                    <span>${this.checkData.warehouseName || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">制单人：</span>\n                    <span>${this.checkData.createByName || this.checkData.createBy || ''}</span>\n                  </div>\n                </div>\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人：</span>\n                    <span>${this.checkData.auditByName || this.checkData.auditBy || '未审核'}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 120px;\">仓库负责人：</span>\n                    <span>${this.checkData.managerName || '未指定'}</span>\n                  </div>\n                </div>\n                \n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 100%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">备注：</span>\n                    <span>${this.checkData.remark || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"check-details\">\n                <h3>盘点物品信息</h3>\n                <table>\n                  <thead>\n                    <tr>\n                      <th>序号</th>\n                      <th>物品编码</th>\n                      <th>物品名称</th>\n                      <th>账面数量</th>\n                      <th>实际数量</th>\n                      <th>差异数量</th>\n                      <th>备注</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    ${detailsHTML}\n                  </tbody>\n                </table>\n              </div>\n              \n              <div class=\"check-footer\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">盘点人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 120px;\">仓库负责人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    \n    /** 获取状态名称 */\n    getStatusName(status) {\n      // 使用与屏幕预览相同的字典映射方式\n      const statusDict = this.dict.type.inventory_check_status || [];\n      const statusItem = statusDict.find(item => item.value === status);\n      return statusItem ? statusItem.label : '';\n    },\n    \n    /** 关闭 */\n    handleClose() {\n      this.$router.go(-1);\n    }\n  },\n  mounted() {\n    // 加载打印设置\n    this.loadPrintSettings();\n  }\n};\n</script>\n\n<style scoped>\n.print-container {\n  padding: 20px;\n  background-color: #fff;\n  color: #000;\n}\n\n.print-header {\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.print-content {\n  max-width: 1000px;\n  margin: 0 auto;\n  font-family: \"SimSun\", \"宋体\", serif;\n  color: #000;\n}\n\n.check-header {\n  text-align: center;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #000;\n  padding-bottom: 10px;\n}\n\n.check-header .title {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0 0 20px 0;\n}\n\n.header-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.header-item {\n  display: flex;\n  align-items: center;\n}\n\n.header-item .label {\n  font-weight: bold;\n}\n\n.check-info {\n  margin-bottom: 30px;\n}\n\n.info-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.info-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.detail-table {\n  margin-bottom: 30px;\n}\n\n.detail-table ::v-deep .el-table__header th {\n  background-color: #f5f5f5;\n  color: #000;\n}\n\n.detail-table ::v-deep .el-table__row td {\n  color: #000;\n}\n\n.check-details h3,\n.approval-info h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n  border-left: 4px solid #409EFF;\n  padding-left: 10px;\n}\n\n.check-footer {\n  margin: 30px 0;\n}\n\n.footer-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-end;\n  height: 30px;\n}\n\n.footer-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.signature-line {\n  display: inline-block;\n  width: 80px;\n  height: 1px;\n  background-color: #000;\n  margin-left: 5px;\n  position: relative;\n  bottom: 3px;\n}\n\n.margin-labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n\n.text-red {\n  color: #F56C6C;\n}\n\n.text-green {\n  color: #67C23A;\n}\n</style>"]}]}