{"remainingRequest": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\in\\print.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\CKGLXT\\warehouse-system\\frontend\\src\\views\\inventory\\in\\print.vue", "mtime": 1756537496223}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1755901408199}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1755901388713}, {"path": "C:\\CKGLXT\\warehouse-system\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1755901416017}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEludmVudG9yeUluIH0gZnJvbSAiQC9hcGkvaW52ZW50b3J5L2luIjsKaW1wb3J0IHsgZ2V0QmF0Y2hVc2VyUmVhbE5hbWVzIH0gZnJvbSAiQC91dGlscy91c2VyVXRpbHMiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJJblByaW50IiwKICBkaWN0czogWydpbnZlbnRvcnlfaW5fdHlwZScsICdpbnZlbnRvcnlfaW5fc3RhdHVzJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBpc1ByaW50aW5nOiBmYWxzZSwKICAgICAgcHJpbnRTZXR0aW5nc1Zpc2libGU6IGZhbHNlLAogICAgICBpbkRhdGE6IHsKICAgICAgICBpbkNvZGU6ICIiLAogICAgICAgIHdhcmVob3VzZU5hbWU6ICIiLAogICAgICAgIGluVGltZTogbnVsbCwKICAgICAgICBpblR5cGU6ICIiLAogICAgICAgIHN0YXR1czogIiIsCiAgICAgICAgY3JlYXRlQnk6ICIiLAogICAgICAgIGNyZWF0ZUJ5TmFtZTogIiIsCiAgICAgICAgYXVkaXRCeTogIiIsCiAgICAgICAgYXVkaXRCeU5hbWU6ICIiLAogICAgICAgIHJlbWFyazogIiIsCiAgICAgICAgZGV0YWlsczogW10KICAgICAgfSwKICAgICAgaW5JZDogbnVsbCwKICAgICAgcHJpbnRTZXR0aW5nczogewogICAgICAgIG1hcmdpblRvcDogMTUsCiAgICAgICAgbWFyZ2luUmlnaHQ6IDE1LAogICAgICAgIG1hcmdpbkJvdHRvbTogMTUsCiAgICAgICAgbWFyZ2luTGVmdDogMTUsCiAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgIG9yaWVudGF0aW9uOiAncG9ydHJhaXQnLAogICAgICAgIHBhcGVyU2l6ZTogJ0E0JwogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuaW5JZCA9IHRoaXMuJHJvdXRlLnBhcmFtcy5pbklkIHx8IHRoaXMuJHJvdXRlLnBhcmFtcy5pZCB8fCB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsKICAgIGNvbnNvbGUubG9nKCflhaXlupPljZXmiZPljbDpobXpnaLliJ3lp4vljJbvvIzlhaXlupPljZVJRDonLCB0aGlzLmluSWQpOwogICAgaWYgKCF0aGlzLmluSWQpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign57y65bCR5YWl5bqT5Y2VSUTlj4LmlbAnKTsKICAgICAgcmV0dXJuOwogICAgfQogICAgdGhpcy5nZXRJbkRhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDojrflj5blhaXlupPljZXkv6Hmga8gKi8KICAgIGdldEluRGF0YSgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgZ2V0SW52ZW50b3J5SW4odGhpcy5pbklkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmluRGF0YSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgCiAgICAgICAgLy8g6I635Y+W55So5oi355yf5a6e5aeT5ZCNCiAgICAgICAgY29uc3QgdXNlck5hbWVzID0gWwogICAgICAgICAgdGhpcy5pbkRhdGEuY3JlYXRlQnksCiAgICAgICAgICB0aGlzLmluRGF0YS5hdWRpdEJ5CiAgICAgICAgXS5maWx0ZXIobmFtZSA9PiBuYW1lKTsgLy8g6L+H5ruk56m65YC8CgogICAgICAgIGlmICh1c2VyTmFtZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgZ2V0QmF0Y2hVc2VyUmVhbE5hbWVzKHVzZXJOYW1lcykudGhlbihuYW1lTWFwID0+IHsKICAgICAgICAgICAgLy8g5pu05paw55So5oi355yf5a6e5aeT5ZCNCiAgICAgICAgICAgIGlmICh0aGlzLmluRGF0YS5jcmVhdGVCeSAmJiBuYW1lTWFwW3RoaXMuaW5EYXRhLmNyZWF0ZUJ5XSkgewogICAgICAgICAgICAgIHRoaXMuaW5EYXRhLmNyZWF0ZUJ5TmFtZSA9IG5hbWVNYXBbdGhpcy5pbkRhdGEuY3JlYXRlQnldOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmICh0aGlzLmluRGF0YS5hdWRpdEJ5ICYmIG5hbWVNYXBbdGhpcy5pbkRhdGEuYXVkaXRCeV0pIHsKICAgICAgICAgICAgICB0aGlzLmluRGF0YS5hdWRpdEJ5TmFtZSA9IG5hbWVNYXBbdGhpcy5pbkRhdGEuYXVkaXRCeV07CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgICAKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluWFpeW6k+WNleS/oeaBr+Wksei0pSIpOwogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8qKiDlpITnkIbmiZPljbDorr7nva4gKi8KICAgIGhhbmRsZVByaW50U2V0dGluZ3MoKSB7CiAgICAgIHRoaXMucHJpbnRTZXR0aW5nc1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIAogICAgLyoqIOS/neWtmOaJk+WNsOiuvue9riAqLwogICAgc2F2ZVByaW50U2V0dGluZ3MoKSB7CiAgICAgIC8vIOS/neWtmOWIsOacrOWcsOWtmOWCqAogICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnaW5QcmludFNldHRpbmdzJywgSlNPTi5zdHJpbmdpZnkodGhpcy5wcmludFNldHRpbmdzKSk7CiAgICAgIHRoaXMucHJpbnRTZXR0aW5nc1Zpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmiZPljbDorr7nva7lt7Lkv53lrZgnKTsKICAgIH0sCiAgICAKICAgIC8qKiDliqDovb3miZPljbDorr7nva4gKi8KICAgIGxvYWRQcmludFNldHRpbmdzKCkgewogICAgICBjb25zdCBzYXZlZFNldHRpbmdzID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2luUHJpbnRTZXR0aW5ncycpOwogICAgICBpZiAoc2F2ZWRTZXR0aW5ncykgewogICAgICAgIHRoaXMucHJpbnRTZXR0aW5ncyA9IEpTT04ucGFyc2Uoc2F2ZWRTZXR0aW5ncyk7CiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8qKiDmoLzlvI/ljJbph5Hpop0gKi8KICAgIGZvcm1hdEFtb3VudChyb3csIGNvbHVtbiwgY2VsbFZhbHVlKSB7CiAgICAgIGlmICghY2VsbFZhbHVlKSByZXR1cm4gJzAuMDAnOwogICAgICByZXR1cm4gcGFyc2VGbG9hdChjZWxsVmFsdWUpLnRvRml4ZWQoMik7CiAgICB9LAogICAgCiAgICAvKiog5omT5Y2wICovCiAgICBoYW5kbGVQcmludCgpIHsKICAgICAgdGhpcy5pc1ByaW50aW5nID0gdHJ1ZTsKICAgICAgCiAgICAgIC8vIOehruS/neaVsOaNruW3suWKoOi9vQogICAgICBpZiAoIXRoaXMuaW5EYXRhLmluQ29kZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pWw5o2u6L+Y5Zyo5Yqg6L295Lit77yM6K+356iN5ZCO5YaN6K+VJyk7CiAgICAgICAgdGhpcy5pc1ByaW50aW5nID0gZmFsc2U7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIAogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgLy8g5Yib5bu65paw56qX5Y+j6L+b6KGM5omT5Y2wCiAgICAgICAgY29uc3QgcHJpbnRXaW5kb3cgPSB3aW5kb3cub3BlbignJywgJ19ibGFuaycsICd3aWR0aD0xMDAwLGhlaWdodD04MDAsc2Nyb2xsYmFycz15ZXMscmVzaXphYmxlPXllcycpOwogICAgICAgIAogICAgICAgIC8vIOeUn+aIkOaJk+WNsOWGheWuuQogICAgICAgIGNvbnN0IHByaW50Q29udGVudCA9IHRoaXMuZ2VuZXJhdGVQcmludEhUTUwoKTsKICAgICAgICAKICAgICAgICBwcmludFdpbmRvdy5kb2N1bWVudC53cml0ZShwcmludENvbnRlbnQpOwogICAgICAgIHByaW50V2luZG93LmRvY3VtZW50LmNsb3NlKCk7CiAgICAgICAgCiAgICAgICAgLy8g562J5b6F5YaF5a655Yqg6L295a6M5oiQ5ZCO5omT5Y2wCiAgICAgICAgcHJpbnRXaW5kb3cub25sb2FkID0gKCkgPT4gewogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgIHByaW50V2luZG93LnByaW50KCk7CiAgICAgICAgICAgIC8vIOaJk+WNsOWujOaIkOWQjuS4jeiHquWKqOWFs+mXreeql+WPo++8jOiuqeeUqOaIt+aJi+WKqOWFs+mXrQogICAgICAgICAgICB0aGlzLmlzUHJpbnRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0sIDUwMCk7CiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9LAogICAgCiAgICAvKiog55Sf5oiQ5omT5Y2w6aG16Z2iSFRNTCAqLwogICAgZ2VuZXJhdGVQcmludEhUTUwoKSB7CiAgICAgIGNvbnN0IGRldGFpbHMgPSB0aGlzLmluRGF0YS5kZXRhaWxzIHx8IFtdOwogICAgICBsZXQgZGV0YWlsc0hUTUwgPSAnJzsKICAgICAgCiAgICAgIGRldGFpbHMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICBkZXRhaWxzSFRNTCArPSBgCiAgICAgICAgICA8dHI+CiAgICAgICAgICAgIDx0ZCBzdHlsZT0iYm9yZGVyOiAxcHggc29saWQgIzAwMDsgcGFkZGluZzogOHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7Ij4ke2luZGV4ICsgMX08L3RkPgogICAgICAgICAgICA8dGQgc3R5bGU9ImJvcmRlcjogMXB4IHNvbGlkICMwMDA7IHBhZGRpbmc6IDhweDsgdGV4dC1hbGlnbjogY2VudGVyOyI+JHtpdGVtLnByb2R1Y3RDb2RlIHx8ICcnfTwvdGQ+CiAgICAgICAgICAgIDx0ZCBzdHlsZT0iYm9yZGVyOiAxcHggc29saWQgIzAwMDsgcGFkZGluZzogOHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7Ij4ke2l0ZW0ucHJvZHVjdE5hbWUgfHwgJyd9PC90ZD4KICAgICAgICAgICAgPHRkIHN0eWxlPSJib3JkZXI6IDFweCBzb2xpZCAjMDAwOyBwYWRkaW5nOiA4cHg7IHRleHQtYWxpZ246IGNlbnRlcjsiPiR7aXRlbS5xdWFudGl0eSB8fCAnJ308L3RkPgogICAgICAgICAgICA8dGQgc3R5bGU9ImJvcmRlcjogMXB4IHNvbGlkICMwMDA7IHBhZGRpbmc6IDhweDsgdGV4dC1hbGlnbjogY2VudGVyOyI+JHt0aGlzLmZvcm1hdEFtb3VudChudWxsLCBudWxsLCBpdGVtLnByaWNlKX08L3RkPgogICAgICAgICAgICA8dGQgc3R5bGU9ImJvcmRlcjogMXB4IHNvbGlkICMwMDA7IHBhZGRpbmc6IDhweDsgdGV4dC1hbGlnbjogY2VudGVyOyI+JHt0aGlzLmZvcm1hdEFtb3VudChudWxsLCBudWxsLCBpdGVtLmFtb3VudCl9PC90ZD4KICAgICAgICAgICAgPHRkIHN0eWxlPSJib3JkZXI6IDFweCBzb2xpZCAjMDAwOyBwYWRkaW5nOiA4cHg7IHRleHQtYWxpZ246IGNlbnRlcjsiPiR7aXRlbS5yZW1hcmsgfHwgJyd9PC90ZD4KICAgICAgICAgIDwvdHI+CiAgICAgICAgYDsKICAgICAgfSk7CiAgICAgIAogICAgICAvLyDmoLnmja7orr7nva7noa7lrprpobXpnaLmlrnlkJHlkoznurjlvKDlpKflsI8KICAgICAgbGV0IHBhZ2VTaXplU3R5bGUgPSAnJzsKICAgICAgaWYgKHRoaXMucHJpbnRTZXR0aW5ncy5vcmllbnRhdGlvbiA9PT0gJ2xhbmRzY2FwZScpIHsKICAgICAgICBwYWdlU2l6ZVN0eWxlID0gYHNpemU6ICR7dGhpcy5wcmludFNldHRpbmdzLnBhcGVyU2l6ZX0gbGFuZHNjYXBlO2A7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcGFnZVNpemVTdHlsZSA9IGBzaXplOiAke3RoaXMucHJpbnRTZXR0aW5ncy5wYXBlclNpemV9IHBvcnRyYWl0O2A7CiAgICAgIH0KICAgICAgCiAgICAgIHJldHVybiBgCiAgICAgICAgPCFET0NUWVBFIGh0bWw+CiAgICAgICAgPGh0bWw+CiAgICAgICAgPGhlYWQ+CiAgICAgICAgICA8bWV0YSBjaGFyc2V0PSJVVEYtOCI+CiAgICAgICAgICA8bWV0YSBuYW1lPSJ2aWV3cG9ydCIgY29udGVudD0id2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCI+CiAgICAgICAgICA8dGl0bGU+5YWl5bqT5Y2V5omT5Y2wPC90aXRsZT4KICAgICAgICAgIDxzdHlsZT4KICAgICAgICAgICAgQHBhZ2UgeyAKICAgICAgICAgICAgICAke3BhZ2VTaXplU3R5bGV9CiAgICAgICAgICAgICAgbWFyZ2luOiAke3RoaXMucHJpbnRTZXR0aW5ncy5tYXJnaW5Ub3B9bW0gJHt0aGlzLnByaW50U2V0dGluZ3MubWFyZ2luUmlnaHR9bW0gJHt0aGlzLnByaW50U2V0dGluZ3MubWFyZ2luQm90dG9tfW1tICR7dGhpcy5wcmludFNldHRpbmdzLm1hcmdpbkxlZnR9bW07IAogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAqIHsKICAgICAgICAgICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICBodG1sLCBib2R5IHsgCiAgICAgICAgICAgICAgZm9udC1mYW1pbHk6ICJNaWNyb3NvZnQgWWFIZWkiLCBTaW1TdW4sIHNhbnMtc2VyaWY7IAogICAgICAgICAgICAgIGZvbnQtc2l6ZTogJHt0aGlzLnByaW50U2V0dGluZ3MuZm9udFNpemV9cHQ7IAogICAgICAgICAgICAgIGNvbG9yOiAjMDAwOyAKICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOyAKICAgICAgICAgICAgICBtYXJnaW46IDA7IAogICAgICAgICAgICAgIHBhZGRpbmc6ICR7dGhpcy5wcmludFNldHRpbmdzLm1hcmdpblRvcC8zfW1tOwogICAgICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgICAgIC13ZWJraXQtcHJpbnQtY29sb3ItYWRqdXN0OiBleGFjdDsKICAgICAgICAgICAgICBwcmludC1jb2xvci1hZGp1c3Q6IGV4YWN0OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuY29udGFpbmVyIHsKICAgICAgICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICAgICAgICBtYXgtd2lkdGg6IDEwMDBweDsKICAgICAgICAgICAgICBtYXJnaW46IDAgYXV0bzsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLnByaW50LWNvbnRhaW5lciB7CiAgICAgICAgICAgICAgcGFkZGluZzogMjBweDsKICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOwogICAgICAgICAgICAgIGNvbG9yOiAjMDAwOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAucHJpbnQtY29udGVudCB7CiAgICAgICAgICAgICAgbWF4LXdpZHRoOiAxMDAwcHg7CiAgICAgICAgICAgICAgbWFyZ2luOiAwIGF1dG87CiAgICAgICAgICAgICAgZm9udC1mYW1pbHk6ICJTaW1TdW4iLCAi5a6L5L2TIiwgc2VyaWY7CiAgICAgICAgICAgICAgY29sb3I6ICMwMDA7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5pbi1oZWFkZXIgewogICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAzMHB4OwogICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMDAwOwogICAgICAgICAgICAgIHBhZGRpbmctYm90dG9tOiAxMHB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaW4taGVhZGVyIC50aXRsZSB7CiAgICAgICAgICAgICAgZm9udC1zaXplOiAke3RoaXMucHJpbnRTZXR0aW5ncy5mb250U2l6ZSArIDh9cHQ7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgICAgbWFyZ2luOiAwIDAgMjBweCAwOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaGVhZGVyLWluZm8gewogICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaGVhZGVyLWl0ZW0gewogICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmhlYWRlci1pdGVtIC5sYWJlbCB7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5pbi1pbmZvIHsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAzMHB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaW5mby1pdGVtIHsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4OwogICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIC5pbmZvLWl0ZW0gLmxhYmVsIHsKICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgICAgICAgICBtaW4td2lkdGg6IDEwMHB4OwogICAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaW4tZGV0YWlscyBoMywKICAgICAgICAgICAgLmFwcHJvdmFsLWluZm8gaDMgewogICAgICAgICAgICAgIGZvbnQtc2l6ZTogJHt0aGlzLnByaW50U2V0dGluZ3MuZm9udFNpemUgKyA0fXB0OwogICAgICAgICAgICAgIG1hcmdpbjogMCAwIDE1cHggMDsKICAgICAgICAgICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM0MDlFRkY7CiAgICAgICAgICAgICAgcGFkZGluZy1sZWZ0OiAxMHB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICB0YWJsZSB7CiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgICAgICAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAzMHB4OwogICAgICAgICAgICAgIHRhYmxlLWxheW91dDogZml4ZWQ7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIHRoIHsKICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1OwogICAgICAgICAgICAgIGNvbG9yOiAjMDAwOwogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMwMDA7CiAgICAgICAgICAgICAgcGFkZGluZzogOHB4OwogICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgdGQgewogICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMwMDA7CiAgICAgICAgICAgICAgcGFkZGluZzogOHB4OwogICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgICAgICAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7CiAgICAgICAgICAgICAgd29yZC1icmVhazogYnJlYWstYWxsOwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuaW4tZm9vdGVyIHsKICAgICAgICAgICAgICBtYXJnaW46IDMwcHggMDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLmZvb3Rlci1pdGVtIHsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAuc2lnbmF0dXJlLWxpbmUgewogICAgICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgICAgICAgICB3aWR0aDogODBweDsKICAgICAgICAgICAgICBoZWlnaHQ6IDFweDsKICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDAwOwogICAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4OwogICAgICAgICAgICB9CiAgICAgICAgICA8L3N0eWxlPgogICAgICAgIDwvaGVhZD4KICAgICAgICA8Ym9keT4KICAgICAgICAgIDxkaXYgY2xhc3M9InByaW50LWNvbnRhaW5lciI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InByaW50LWNvbnRlbnQiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImluLWhlYWRlciI+CiAgICAgICAgICAgICAgICA8aDIgY2xhc3M9InRpdGxlIj7lhaXlupPljZU8L2gyPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyLWluZm8iPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItaXRlbSI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImxhYmVsIj7lhaXlupPljZXlj7fvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InZhbHVlIj4ke3RoaXMuaW5EYXRhLmluQ29kZSB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItaXRlbSI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImxhYmVsIj7lhaXlupPml6XmnJ/vvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InZhbHVlIj4ke3RoaXMucGFyc2VUaW1lKHRoaXMuaW5EYXRhLmluVGltZSkgfHwgJyd9PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyLWl0ZW0iPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJsYWJlbCI+54q25oCB77yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJ2YWx1ZSI+JHt0aGlzLmdldFN0YXR1c05hbWUodGhpcy5pbkRhdGEuc3RhdHVzKSB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW4taW5mbyI+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiA1MCU7IGRpc3BsYXk6IGZsZXg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7ku5PlupPlkI3np7DvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4+JHt0aGlzLmluRGF0YS53YXJlaG91c2VOYW1lIHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiA1MCU7IGRpc3BsYXk6IGZsZXg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7lhaXlupPnsbvlnovvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4+JHt0aGlzLmdldEluVHlwZU5hbWUodGhpcy5pbkRhdGEuaW5UeXBlKSB8fCAnJ308L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiA1MCU7IGRpc3BsYXk6IGZsZXg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7liLbljZXkurrvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4+JHt0aGlzLmluRGF0YS5jcmVhdGVCeU5hbWUgfHwgdGhpcy5pbkRhdGEuY3JlYXRlQnkgfHwgJyd9PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0id2lkdGg6IDUwJTsgZGlzcGxheTogZmxleDsiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWluLXdpZHRoOiAxMDBweDsiPuWuoeaguOS6uu+8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8c3Bhbj4ke3RoaXMuaW5EYXRhLmF1ZGl0QnlOYW1lIHx8IHRoaXMuaW5EYXRhLmF1ZGl0QnkgfHwgJ+acquWuoeaguCd9PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiAxMDAlOyBkaXNwbGF5OiBmbGV4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEwMHB4OyI+5aSH5rOo77yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxzcGFuPiR7dGhpcy5pbkRhdGEucmVtYXJrIHx8ICcnfTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAKICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbi1kZXRhaWxzIj4KICAgICAgICAgICAgICAgIDxoMz7lhaXlupPnianlk4Hkv6Hmga88L2gzPgogICAgICAgICAgICAgICAgPHRhYmxlPgogICAgICAgICAgICAgICAgICA8dGhlYWQ+CiAgICAgICAgICAgICAgICAgICAgPHRyPgogICAgICAgICAgICAgICAgICAgICAgPHRoPuW6j+WPtzwvdGg+CiAgICAgICAgICAgICAgICAgICAgICA8dGg+54mp5ZOB57yW56CBPC90aD4KICAgICAgICAgICAgICAgICAgICAgIDx0aD7nianlk4HlkI3np7A8L3RoPgogICAgICAgICAgICAgICAgICAgICAgPHRoPuWFpeW6k+aVsOmHjzwvdGg+CiAgICAgICAgICAgICAgICAgICAgICA8dGg+5Y2V5Lu3PC90aD4KICAgICAgICAgICAgICAgICAgICAgIDx0aD7ph5Hpop08L3RoPgogICAgICAgICAgICAgICAgICAgICAgPHRoPuWkh+azqDwvdGg+CiAgICAgICAgICAgICAgICAgICAgPC90cj4KICAgICAgICAgICAgICAgICAgPC90aGVhZD4KICAgICAgICAgICAgICAgICAgPHRib2R5PgogICAgICAgICAgICAgICAgICAgICR7ZGV0YWlsc0hUTUx9CiAgICAgICAgICAgICAgICAgIDwvdGJvZHk+CiAgICAgICAgICAgICAgICA8L3RhYmxlPgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIAogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImluLWZvb3RlciI+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBtYXJnaW4tYm90dG9tOiAxNXB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9IndpZHRoOiAzMy4zMyU7IGRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBmbGV4LWVuZDsgaGVpZ2h0OiAzMHB4OyI+CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtd2VpZ2h0OiBib2xkOyBtaW4td2lkdGg6IDEwMHB4OyI+5LuT5bqT566h55CG5ZGY77yaPC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGlubGluZS1ibG9jazsgd2lkdGg6IDgwcHg7IGhlaWdodDogMXB4OyBiYWNrZ3JvdW5kLWNvbG9yOiAjMDAwOyBtYXJnaW4tbGVmdDogMTBweDsgcG9zaXRpb246IHJlbGF0aXZlOyBib3R0b206IDNweDsiPjwvZGl2PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0id2lkdGg6IDMzLjMzJTsgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGZsZXgtZW5kOyBoZWlnaHQ6IDMwcHg7Ij4KICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1pbi13aWR0aDogMTAwcHg7Ij7lrqHmoLjkurrnrb7lrZfvvJo8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogaW5saW5lLWJsb2NrOyB3aWR0aDogODBweDsgaGVpZ2h0OiAxcHg7IGJhY2tncm91bmQtY29sb3I6ICMwMDA7IG1hcmdpbi1sZWZ0OiAxMHB4OyBwb3NpdGlvbjogcmVsYXRpdmU7IGJvdHRvbTogM3B4OyI+PC9kaXY+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogMzMuMzMlOyBkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogZmxleC1lbmQ7IGhlaWdodDogMzBweDsiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWluLXdpZHRoOiAxMDBweDsiPuaXpeacn++8mjwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBpbmxpbmUtYmxvY2s7IHdpZHRoOiA4MHB4OyBoZWlnaHQ6IDFweDsgYmFja2dyb3VuZC1jb2xvcjogIzAwMDsgbWFyZ2luLWxlZnQ6IDEwcHg7IHBvc2l0aW9uOiByZWxhdGl2ZTsgYm90dG9tOiAzcHg7Ij48L2Rpdj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2JvZHk+CiAgICAgICAgPC9odG1sPgogICAgICBgOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlueKtuaAgeWQjeensCAqLwogICAgZ2V0U3RhdHVzTmFtZShzdGF0dXMpIHsKICAgICAgLy8g5L2/55So5LiO5bGP5bmV6aKE6KeI55u45ZCM55qE5a2X5YW45pig5bCE5pa55byPCiAgICAgIGNvbnN0IHN0YXR1c0RpY3QgPSB0aGlzLmRpY3QudHlwZS5pbnZlbnRvcnlfaW5fc3RhdHVzIHx8IFtdOwogICAgICBjb25zdCBzdGF0dXNJdGVtID0gc3RhdHVzRGljdC5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PT0gc3RhdHVzKTsKICAgICAgcmV0dXJuIHN0YXR1c0l0ZW0gPyBzdGF0dXNJdGVtLmxhYmVsIDogJyc7CiAgICB9LAogICAgCiAgICAvKiog6I635Y+W5YWl5bqT57G75Z6L5ZCN56ewICovCiAgICBnZXRJblR5cGVOYW1lKGluVHlwZSkgewogICAgICAvLyDkvb/nlKjkuI7lsY/luZXpooTop4jnm7jlkIznmoTlrZflhbjmmKDlsITmlrnlvI8KICAgICAgY29uc3QgdHlwZURpY3QgPSB0aGlzLmRpY3QudHlwZS5pbnZlbnRvcnlfaW5fdHlwZSB8fCBbXTsKICAgICAgY29uc3QgdHlwZUl0ZW0gPSB0eXBlRGljdC5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PT0gaW5UeXBlKTsKICAgICAgcmV0dXJuIHR5cGVJdGVtID8gdHlwZUl0ZW0ubGFiZWwgOiAnJzsKICAgIH0sCiAgICAKICAgIC8qKiDlhbPpl60gKi8KICAgIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8vIOWKoOi9veaJk+WNsOiuvue9rgogICAgdGhpcy5sb2FkUHJpbnRTZXR0aW5ncygpOwogIH0KfTsK"}, {"version": 3, "sources": ["print.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8KA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "print.vue", "sourceRoot": "src/views/inventory/in", "sourcesContent": ["<template>\n  <div class=\"print-container\">\n    <!-- 打印预览界面 -->\n    <div class=\"print-header\" v-if=\"!isPrinting\">\n      <el-button type=\"primary\" icon=\"el-icon-printer\" size=\"small\" @click=\"handlePrint\">\n        打印入库单\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-setting\" size=\"small\" @click=\"handlePrintSettings\">\n        打印设置\n      </el-button>\n      <el-button type=\"default\" icon=\"el-icon-close\" size=\"small\" @click=\"handleClose\">\n        关闭\n      </el-button>\n    </div>\n    \n    <!-- 打印设置对话框 -->\n    <el-dialog title=\"打印设置\" :visible.sync=\"printSettingsVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"printSettingsForm\" :model=\"printSettings\" label-width=\"100px\">\n        <el-form-item label=\"页面边距\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginTop\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginRight\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginBottom\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-input v-model.number=\"printSettings.marginLeft\" size=\"small\">\n                <template slot=\"append\">mm</template>\n              </el-input>\n            </el-col>\n          </el-row>\n          <div class=\"margin-labels\">\n            <span>上</span>\n            <span>右</span>\n            <span>下</span>\n            <span>左</span>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"字体大小\">\n          <el-slider v-model=\"printSettings.fontSize\" :min=\"8\" :max=\"20\" show-input></el-slider>\n        </el-form-item>\n        \n        <el-form-item label=\"页面方向\">\n          <el-radio-group v-model=\"printSettings.orientation\">\n            <el-radio label=\"portrait\">纵向</el-radio>\n            <el-radio label=\"landscape\">横向</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"纸张大小\">\n          <el-select v-model=\"printSettings.paperSize\" placeholder=\"请选择纸张大小\">\n            <el-option label=\"A4\" value=\"A4\"></el-option>\n            <el-option label=\"A5\" value=\"A5\"></el-option>\n            <el-option label=\"B5\" value=\"B5\"></el-option>\n            <el-option label=\"Letter\" value=\"letter\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"printSettingsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"savePrintSettings\">确 定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- 打印内容 -->\n    <div class=\"print-content\" id=\"printContent\">\n      <div class=\"in-header\">\n        <h2 class=\"title\">入库单</h2>\n        <div class=\"header-info\">\n          <div class=\"header-item\">\n            <span class=\"label\">入库单号：</span>\n            <span class=\"value\">{{ inData.inCode }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">入库日期：</span>\n            <span class=\"value\">{{ parseTime(inData.inTime) }}</span>\n          </div>\n          <div class=\"header-item\">\n            <span class=\"label\">状态：</span>\n            <span class=\"value\">\n              <dict-tag :options=\"dict.type.inventory_in_status\" :value=\"inData.status\" />\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"in-info\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">仓库名称：</span>\n              <span class=\"value\">{{ inData.warehouseName }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">入库类型：</span>\n              <span class=\"value\">\n                <dict-tag :options=\"dict.type.inventory_in_type\" :value=\"inData.inType\" />\n              </span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">制单人：</span>\n              <span class=\"value\">{{ inData.createByName || inData.createBy }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"info-item\">\n              <span class=\"label\">审核人：</span>\n              <span class=\"value\">{{ inData.auditByName || inData.auditBy || '未审核' }}</span>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <div class=\"info-item\">\n              <span class=\"label\">备注：</span>\n              <span class=\"value\">{{ inData.remark }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <div class=\"in-details\">\n        <h3>入库物品信息</h3>\n        <el-table :data=\"inData.details\" class=\"detail-table\" show-summary>\n          <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n          <el-table-column label=\"物品编码\" prop=\"productCode\" />\n          <el-table-column label=\"物品名称\" prop=\"productName\" />\n          <el-table-column label=\"入库数量\" prop=\"quantity\" />\n          <el-table-column label=\"单价\" prop=\"price\" :formatter=\"formatAmount\" />\n          <el-table-column label=\"金额\" prop=\"amount\" :formatter=\"formatAmount\" />\n          <el-table-column label=\"备注\" prop=\"remark\" />\n        </el-table>\n      </div>\n      \n      <div class=\"in-footer\">\n        <div style=\"display: flex; margin-bottom: 15px;\">\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">仓库管理员：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n          <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n            <span style=\"font-weight: bold; min-width: 100px;\">日期：</span>\n            <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getInventoryIn } from \"@/api/inventory/in\";\nimport { getBatchUserRealNames } from \"@/utils/userUtils\";\n\nexport default {\n  name: \"InPrint\",\n  dicts: ['inventory_in_type', 'inventory_in_status'],\n  data() {\n    return {\n      loading: false,\n      isPrinting: false,\n      printSettingsVisible: false,\n      inData: {\n        inCode: \"\",\n        warehouseName: \"\",\n        inTime: null,\n        inType: \"\",\n        status: \"\",\n        createBy: \"\",\n        createByName: \"\",\n        auditBy: \"\",\n        auditByName: \"\",\n        remark: \"\",\n        details: []\n      },\n      inId: null,\n      printSettings: {\n        marginTop: 15,\n        marginRight: 15,\n        marginBottom: 15,\n        marginLeft: 15,\n        fontSize: 12,\n        orientation: 'portrait',\n        paperSize: 'A4'\n      }\n    };\n  },\n  created() {\n    this.inId = this.$route.params.inId || this.$route.params.id || this.$route.query.id;\n    console.log('入库单打印页面初始化，入库单ID:', this.inId);\n    if (!this.inId) {\n      this.$message.error('缺少入库单ID参数');\n      return;\n    }\n    this.getInData();\n  },\n  methods: {\n    /** 获取入库单信息 */\n    getInData() {\n      this.loading = true;\n      getInventoryIn(this.inId).then(response => {\n        this.inData = response.data;\n        \n        // 获取用户真实姓名\n        const userNames = [\n          this.inData.createBy,\n          this.inData.auditBy\n        ].filter(name => name); // 过滤空值\n\n        if (userNames.length > 0) {\n          getBatchUserRealNames(userNames).then(nameMap => {\n            // 更新用户真实姓名\n            if (this.inData.createBy && nameMap[this.inData.createBy]) {\n              this.inData.createByName = nameMap[this.inData.createBy];\n            }\n            if (this.inData.auditBy && nameMap[this.inData.auditBy]) {\n              this.inData.auditByName = nameMap[this.inData.auditBy];\n            }\n          });\n        }\n        \n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n        this.$message.error(\"获取入库单信息失败\");\n      });\n    },\n    \n    /** 处理打印设置 */\n    handlePrintSettings() {\n      this.printSettingsVisible = true;\n    },\n    \n    /** 保存打印设置 */\n    savePrintSettings() {\n      // 保存到本地存储\n      localStorage.setItem('inPrintSettings', JSON.stringify(this.printSettings));\n      this.printSettingsVisible = false;\n      this.$message.success('打印设置已保存');\n    },\n    \n    /** 加载打印设置 */\n    loadPrintSettings() {\n      const savedSettings = localStorage.getItem('inPrintSettings');\n      if (savedSettings) {\n        this.printSettings = JSON.parse(savedSettings);\n      }\n    },\n    \n    /** 格式化金额 */\n    formatAmount(row, column, cellValue) {\n      if (!cellValue) return '0.00';\n      return parseFloat(cellValue).toFixed(2);\n    },\n    \n    /** 打印 */\n    handlePrint() {\n      this.isPrinting = true;\n      \n      // 确保数据已加载\n      if (!this.inData.inCode) {\n        this.$message.warning('数据还在加载中，请稍后再试');\n        this.isPrinting = false;\n        return;\n      }\n      \n      this.$nextTick(() => {\n        // 创建新窗口进行打印\n        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');\n        \n        // 生成打印内容\n        const printContent = this.generatePrintHTML();\n        \n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        \n        // 等待内容加载完成后打印\n        printWindow.onload = () => {\n          setTimeout(() => {\n            printWindow.print();\n            // 打印完成后不自动关闭窗口，让用户手动关闭\n            this.isPrinting = false;\n          }, 500);\n        };\n      });\n    },\n    \n    /** 生成打印页面HTML */\n    generatePrintHTML() {\n      const details = this.inData.details || [];\n      let detailsHTML = '';\n      \n      details.forEach((item, index) => {\n        detailsHTML += `\n          <tr>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${index + 1}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productCode || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.productName || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.quantity || ''}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${this.formatAmount(null, null, item.price)}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${this.formatAmount(null, null, item.amount)}</td>\n            <td style=\"border: 1px solid #000; padding: 8px; text-align: center;\">${item.remark || ''}</td>\n          </tr>\n        `;\n      });\n      \n      // 根据设置确定页面方向和纸张大小\n      let pageSizeStyle = '';\n      if (this.printSettings.orientation === 'landscape') {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;\n      } else {\n        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;\n      }\n      \n      return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>入库单打印</title>\n          <style>\n            @page { \n              ${pageSizeStyle}\n              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; \n            }\n            \n            * {\n              box-sizing: border-box;\n            }\n            \n            html, body { \n              font-family: \"Microsoft YaHei\", SimSun, sans-serif; \n              font-size: ${this.printSettings.fontSize}pt; \n              color: #000; \n              background: #fff; \n              margin: 0; \n              padding: ${this.printSettings.marginTop/3}mm;\n              width: 100%;\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n            \n            .container {\n              width: 100%;\n              max-width: 1000px;\n              margin: 0 auto;\n            }\n            \n            .print-container {\n              padding: 20px;\n              background-color: #fff;\n              color: #000;\n            }\n            \n            .print-content {\n              max-width: 1000px;\n              margin: 0 auto;\n              font-family: \"SimSun\", \"宋体\", serif;\n              color: #000;\n            }\n            \n            .in-header {\n              text-align: center;\n              margin-bottom: 30px;\n              border-bottom: 2px solid #000;\n              padding-bottom: 10px;\n            }\n            \n            .in-header .title {\n              font-size: ${this.printSettings.fontSize + 8}pt;\n              font-weight: bold;\n              margin: 0 0 20px 0;\n            }\n            \n            .header-info {\n              display: flex;\n              justify-content: space-between;\n            }\n            \n            .header-item {\n              display: flex;\n              align-items: center;\n            }\n            \n            .header-item .label {\n              font-weight: bold;\n              margin-right: 5px;\n            }\n            \n            .in-info {\n              margin-bottom: 30px;\n            }\n            \n            .info-item {\n              margin-bottom: 15px;\n              display: flex;\n              align-items: flex-start;\n            }\n            \n            .info-item .label {\n              font-weight: bold;\n              min-width: 100px;\n              flex-shrink: 0;\n            }\n            \n            .in-details h3,\n            .approval-info h3 {\n              font-size: ${this.printSettings.fontSize + 4}pt;\n              margin: 0 0 15px 0;\n              border-left: 4px solid #409EFF;\n              padding-left: 10px;\n            }\n            \n            table {\n              width: 100%;\n              border-collapse: collapse;\n              margin-bottom: 30px;\n              table-layout: fixed;\n            }\n            \n            th {\n              background-color: #f5f5f5;\n              color: #000;\n              font-weight: bold;\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n            }\n            \n            td {\n              border: 1px solid #000;\n              padding: 8px;\n              text-align: center;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n            \n            .in-footer {\n              margin: 30px 0;\n            }\n            \n            .footer-item {\n              margin-bottom: 15px;\n            }\n            \n            .signature-line {\n              display: inline-block;\n              width: 80px;\n              height: 1px;\n              background-color: #000;\n              margin-left: 10px;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"print-container\">\n            <div class=\"print-content\">\n              <div class=\"in-header\">\n                <h2 class=\"title\">入库单</h2>\n                <div class=\"header-info\">\n                  <div class=\"header-item\">\n                    <span class=\"label\">入库单号：</span>\n                    <span class=\"value\">${this.inData.inCode || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">入库日期：</span>\n                    <span class=\"value\">${this.parseTime(this.inData.inTime) || ''}</span>\n                  </div>\n                  <div class=\"header-item\">\n                    <span class=\"label\">状态：</span>\n                    <span class=\"value\">${this.getStatusName(this.inData.status) || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"in-info\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库名称：</span>\n                    <span>${this.inData.warehouseName || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">入库类型：</span>\n                    <span>${this.getInTypeName(this.inData.inType) || ''}</span>\n                  </div>\n                </div>\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">制单人：</span>\n                    <span>${this.inData.createByName || this.inData.createBy || ''}</span>\n                  </div>\n                  <div style=\"width: 50%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人：</span>\n                    <span>${this.inData.auditByName || this.inData.auditBy || '未审核'}</span>\n                  </div>\n                </div>\n                \n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 100%; display: flex;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">备注：</span>\n                    <span>${this.inData.remark || ''}</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"in-details\">\n                <h3>入库物品信息</h3>\n                <table>\n                  <thead>\n                    <tr>\n                      <th>序号</th>\n                      <th>物品编码</th>\n                      <th>物品名称</th>\n                      <th>入库数量</th>\n                      <th>单价</th>\n                      <th>金额</th>\n                      <th>备注</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    ${detailsHTML}\n                  </tbody>\n                </table>\n              </div>\n              \n              <div class=\"in-footer\">\n                <div style=\"display: flex; margin-bottom: 15px;\">\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">仓库管理员：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">审核人签字：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                  <div style=\"width: 33.33%; display: flex; align-items: flex-end; height: 30px;\">\n                    <span style=\"font-weight: bold; min-width: 100px;\">日期：</span>\n                    <div style=\"display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `;\n    },\n    \n    /** 获取状态名称 */\n    getStatusName(status) {\n      // 使用与屏幕预览相同的字典映射方式\n      const statusDict = this.dict.type.inventory_in_status || [];\n      const statusItem = statusDict.find(item => item.value === status);\n      return statusItem ? statusItem.label : '';\n    },\n    \n    /** 获取入库类型名称 */\n    getInTypeName(inType) {\n      // 使用与屏幕预览相同的字典映射方式\n      const typeDict = this.dict.type.inventory_in_type || [];\n      const typeItem = typeDict.find(item => item.value === inType);\n      return typeItem ? typeItem.label : '';\n    },\n    \n    /** 关闭 */\n    handleClose() {\n      this.$router.go(-1);\n    }\n  },\n  mounted() {\n    // 加载打印设置\n    this.loadPrintSettings();\n  }\n};\n</script>\n\n<style scoped>\n.print-container {\n  padding: 20px;\n  background-color: #fff;\n  color: #000;\n}\n\n.print-header {\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.print-content {\n  max-width: 1000px;\n  margin: 0 auto;\n  font-family: \"SimSun\", \"宋体\", serif;\n  color: #000;\n}\n\n.in-header {\n  text-align: center;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #000;\n  padding-bottom: 10px;\n}\n\n.in-header .title {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0 0 20px 0;\n}\n\n.header-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.header-item {\n  display: flex;\n  align-items: center;\n}\n\n.header-item .label {\n  font-weight: bold;\n}\n\n.in-info {\n  margin-bottom: 30px;\n}\n\n.info-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.info-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.detail-table {\n  margin-bottom: 30px;\n}\n\n.detail-table ::v-deep .el-table__header th {\n  background-color: #f5f5f5;\n  color: #000;\n}\n\n.detail-table ::v-deep .el-table__row td {\n  color: #000;\n}\n\n.in-details h3,\n.approval-info h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n  border-left: 4px solid #409EFF;\n  padding-left: 10px;\n}\n\n.in-footer {\n  margin: 30px 0;\n}\n\n.footer-item {\n  margin-bottom: 15px;\n  display: flex;\n  align-items: flex-end;\n  height: 30px;\n}\n\n.footer-item .label {\n  font-weight: bold;\n  min-width: 100px;\n  flex-shrink: 0;\n}\n\n.signature-line {\n  display: inline-block;\n  width: 80px;\n  height: 1px;\n  background-color: #000;\n  margin-left: 5px;\n  position: relative;\n  bottom: 3px;\n}\n\n.margin-labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n</style>"]}]}